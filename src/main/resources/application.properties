spring.application.name=oneloyalty-partner-ops
server.port=8080
server.tomcat.connection-timeout=${SERVICE_REQUEST_TIMEOUT:420000}
server.servlet.context-path=${SERVICE_CONTEXT_PATH:/oneloyalty-partner-ops}
spring.servlet.multipart.max-file-size=${MULTIPART_MAX_FILE_SIZE:10MB}
spring.servlet.multipart.max-request-size=${MULTIPART_MAX_FILE_SIZE:10MB}
# Logging
logging.level.org.springframework.data.convert.CustomConversions=ERROR
logging.level.org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker=ERROR
# Database
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.Oracle12cDialect
spring.jpa.properties.hibernate.default_schema=${DEFAULT_SCHEMA_ORACLE:}
spring.jpa.show-sql=${JPA_SHOW_SQL:false}
spring.datasource.url=${JDBC_URL}
spring.datasource.username=${JDBC_USERNAME}
spring.datasource.password=${JDBC_PASSWORD}
spring.datasource.hikari.connectionTimeout=30000
spring.datasource.hikari.idleTimeout=600000
spring.datasource.hikari.maxLifetime=1800000
spring.datasource.hikari.minimumIdle=10
spring.datasource.hikari.maximumPoolSize=10

# Redis
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=6379
spring.redis.database=${REDIS_DB:0}
spring.redis.password=${REDIS_PASSWORD:}
spring.redis.reset-rule.channel=${REDIS_RESET_RULE_TOPIC:oneloyalty-reset-rule-qc}
spring.redis.reset-scheme-rule.channel=${REDIS_RESET_SCHEME_RULE_TOPIC:oneloyalty-scheme-rule-qc}

# Redis for sequence config
seq.redis.host=${SEQ_REDIS_HOST:127.0.0.1}
seq.redis.port=${SEQ_REDIS_PORT:6379}
seq.redis.password=${SEQ_REDIS_PASSWORD:}
seq.redis.database=${SEQ_REDIS_DATABASE:0}
seq.redis.connection.max.total=${SEQ_REDIS_CONNECTION_TOTAL:100}
seq.redis.connection.max.idle=${SEQ_REDIS_CONNECTION_MAX_IDLE:100}
seq.redis.connection.min.idle=${SEQ_REDIS_CONNECTION_MIN_IDLE:30}
#Maker checker (deprecated)
maker-checker.url.create-change-request=${MAKER_CHECKER_URL_CREATE_CHANGE_REQUEST:changes}
maker-checker.url.change=${MAKER_CHECKER_URL_CHANGE}
maker-checker.module.card-transfer=card_transfer
maker-checker.module.scheme=Scheme
maker-checker.module.business=Business
maker-checker.module.gift-card=gift_card
maker-checker.module.cpr=cpr
maker-checker.action.create=create
maker-checker.action.update=update
maker-checker.param-keys.scheme-id=scheme_id
maker-checker.param-keys.business-id=business_id
maker-checker.module.member-card=member_card
maker-checker.module.scheme_seq=scheme_seq
maker-checker.module.counter_seq=counter
maker-checker.module.tier=tier
maker-checker.module.tcb-batch-adj-txn=tcb_batch_adj_partner_txn
maker-checker.module.program-attribute-management=program_attribute
maker-checker.module.system-attribute=system_attribute
maker-checker.module.attribute=attribute
maker-checker.module.member-attribute=member_attribute
maker-checker.module.transaction-attribute=transaction_attribute
maker-checker.module.tier-matching-management=tier_matching_management
#Oneloyalty member service
oneloyalty.member-service.url=${MEMBER_SERVICE_URL}
# Oneloyalty card service
oneloyalty.card-service.url=${CARD_SERVICE_URL}
# Onevoucher service
oneloyalty.voucher-service.url=${VOUCHER_SERVICE_URL}
# Onevoucher internal service
oneloyalty.voucher-service-internal.url=${VOUCHER_SERVICE_INTERNAL_URL}
oneloyalty.voucher-service-internal.basic.auth=${VOUCHER_SERVICE_INTERNAL_BASIC_AUTH}
oneloyalty.voucher-service-internal.x-business-code=${X_BUSINESS_CODE}
# OneU Onevoucher internal service
oneloyalty.oneu-voucher-service-internal.url=${ONEU_VOUCHER_SERVICE_INTERNAL_URL}
oneloyalty.oneu-voucher-service-internal.basic.auth=${ONEU_VOUCHER_SERVICE_INTERNAL_BASIC_AUTH}
# Oneloyalty service
oneloyalty-service.base-url=${ONELOYALTY_SERVICE_BASE_URL}
# Oneloyalty master worker
oneloyalty-master-worker.url=${ONELOYALTY_MASTER_WORKER_BASE_URL}
# Oneloyalty audit log
oneloyalty-audit-log.base-url=${ONELOYALTY_AUDIT_LOG_BASE_URL}
# Oneloyalty rules
oneloyalty-rules.base-url=${ONELOYALTY_RULES_BASE_URL}
# Oneloyalty scheme
oneloyalty-scheme.base-url=${ONELOYALTY_SCHEME_BASE_URL}
# Oneloyalty cpm service
oneloyalty-cpm-service.base-url=${ONELOYALTY_CPM_SERVICE_BASE_URL}
# Oneloyalty elastic
oneloyalty-elastic.base-url=${ONELOYALTY_ELASTIC_BASE_URL}
#Maker checker
maker-checker.url=${MAKER_CHECKER_URL}
#Maker checker internal
maker-checker-internal.url=${MAKER_CHECKER_INTERNAL_URL}
maker-checker-internal.api-key=${MAKER_CHECKER_INTERNAL_API_KEY}
#OPS Admin
ops.admin.url=${OPS_AUTHEN_URL}

#Airflow
oneloyalty-airflow-service.base-url=${ONELOYALTY_AIRFLOW_SERVICE_BASE_URL}
oneloyalty-airflow-service.basic-auth=${ONELOYALTY_AIRFLOW_SERVICE_BASIC_AUTH:YWlyZmxvdzpBaXJmbG93QDMyMQ==}
ops-integration-service.url=${ONELOYALTY_INTEGRATION_OPS}

app.attribute.value.secret=${ATTRIBUTE_VALUE_SECRET}

#SAP Order
sap.url=${SAP_BASE_URL}
sap.basic.auth=${SAP_BASIC_AUTH}

#eElastic search
app.elasticsearch.endpoints=${ES_ENDPOINTS}
app.elasticsearch.username=${ES_USERNAME:}
app.elasticsearch.password=${ES_PASSWORD:}
app.elasticsearch.enable-ssl=${ES_SSL:true}
app.elasticsearch.pingable=${ES_PINGABLE:true}
app.elasticsearch.index.scheme-management=${ES_SCHEME_MANAGEMENT_INDEX}

ops.basic.auth=${OPS_BASIC_AUTH}

app.vd-card-prefix-codes=${VD_CARD_PREFIX_CODES}

# Kafka
kafka.topic.name.common-events=${KAFKA_TOPIC_NAME_COMMON_EVENTS:c1.oneloyalty.common-events.dev}
kafka.reset-rule.topic=${KAFKA_RESET_RULE_TOPIC:oneloyalty-reset-rule-qc}
kafka.reset-scheme-rule.topic=${KAFKA_RESET_SCHEME_RULE_TOPIC:oneloyalty-scheme-rule-qc}
kafka.bootstrap-servers=${KAFKA_BOOTSTRAP_ADDRESS:127.0.0.1:9092}
kafka.truststore.location=${KAFKA_TRUSTSTORE_LOCATION:}
kafka.truststore.password=${KAFKA_TRUSTSTORE_PASSWORD:VPRqm3vG5jNzUxDV}
kafka.keystore.location=${KAFKA_KEYSTORE_LOCATION:}
kafka.keystore.password=${KAFKA_KEYSTORE_PASSWORD:b4kwmDPd4YxaNNBu}
kafka.key.password=${KAFKA_KEY_PASSWORD:b4kwmDPd4YxaNNBu}
kafka.ssl-enable=${KAFKA_SSL_ENABLE:true}
kafka.produce-enable=${KAFKA_PRODUCER_ENABLE:true}

app.sftp.cpr.host=${CPR_SFTP_HOST}
app.sftp.cpr.port=${CPR_SFTP_PORT:22}
app.sftp.cpr.username=${CPR_SFTP_USERNAME}
app.sftp.cpr.password=${CPR_SFTP_PASSWORD}
app.sftp.cpr.sessionTimeout=${SFTP_SESSION_TIMEOUT:15000}
app.sftp.cpr.channelTimeout=${SFTP_CHANNEL_TIMEOUT:15000}
app.sftp.cpr.destination-root-path=${CPR_SFTP_REMOTE_FOLDER}
app.sftp.cpr.pingable=${CPR_SFTP_PINGABLE:true}

1loyalty-partner-ops-integration.url=${ONELOYALTY_INTEGRATION_OPS}

app.exclude-vd-card-type-codes=${VD_CARD_TYPE_CODES}
app.format.date.pattern=yyyyMMdd
app.tmp.prefix.transaction.reverse=${OPS_REVERSE_TRANSACTION_INVOICE_NO_PREFIX:OPS_Reverse_%s}
app.tmp.prefix.transaction.adjustment=${OPS_ADJ_TRANSACTION_INVOICE_NO_PREFIX:OPS_Adj_Point_%s}

checklog.source.api-wrapper-value=${CHECK_LOG_SOURCE_API_WRAPPER_VALUE:oneloyalty-api-wrapper}
checklog.source.mobile.value=${CHECKLOG_SOURCE_MOBILE_VALUE:Mobile app}
checklog.source.mobile.default-store-codes=${CHECKLOG_SOURCE_MOBILE_DEFAULT_STORE_CODES:AA03MB}

app.offset.default-offset=0
app.offset.default-limit=20
app.offset.min-offset=0
app.offset.min-limit=1
app.offset.max-limit=200

gcp.storagebucket-name=${GCP_STORAGE_OPS_BUCKET_NAME}
spring.cloud.gcp.credentials.location=file:${GCP_STORAGE_OPS_SERVICE_ACCOUNT_KEY}

app.batch-adjust-transaction.partners[0].corporation-code=${TCB_CORPORATION_CODE}
app.batch-adjust-transaction.partners[0].batch-file-dir=template/tcb_adjust_transaction.xlsx
app.batch-adjust-transaction.partners[0].batch-file-name=tcb_adjust_transaction.xlsx
app.batch-adjust-transaction.partners[0].max-row-size=1000
app.batch-adjust-transaction.partners[0].bucket-dir=${TCB_BATCH_ADJ_TXN_BUCKET_DIR}
app.batch-adjust-transaction.partners[0].csv-export-headers=CUSTOMER,NAME,TRANSACTION_ID,PRODUCT,SUB_PRODUCT,CARD TYPE,PAN,AMOUNT,AMOUNTORIG,CURRENCYORIG,APPCODE,TYPE,REVREQUESTID,TIMEORIG,TIME,CHANNEL,TERMOWNER,TERMLOCATION,MCC,DESCRIPTION,CUSTOMER_GROUP,INDUSTRY_ADJ,NOTE,CODE_NOTE,RESULT_CODE,ERROR_MESSAGE,1MG_TRANSACTION_ID,POINT_AWARD,POINT_EXPIRY_DATE

app.batch-adjust-transaction.tcb_corporation_code=${TCB_CORPORATION_CODE}
app.batch-adjust-transaction.vgc_code=${VGC_BUSINESS_CODE}
app.format.tcb-export-date-pattern=dd-MM-yyyy hh:mm:ss
app.environment=${APP_ENVIRONMENT}

app.versioning.current-format=${VERSIONING_CURRENT_FORMAT:v.%d(current)}
app.versioning.basic-format=${VERSIONING_BASIC_FORMAT:v.%d}

app.gift-card-transfer.default-send-sms-password-time-sec=${GCT_DEFAULT_SEND_SMS_PASSWORD_TIME_SEC:60}

oauth2.url=${OAUTH2_URL:https://oauth-uat.vinid.dev}
oauth2.token=${OAUTH2_TOKEN:Basic dGlhVWhpOWZXZGM0alNuWE9tbEdva0dJTXBxR1hHazpveEohWnpbcV84bVdbaU0hUVhpTl1mVDdyaUhuTXs=}

notification-center.url=${NOTIFICATION_CENTER_URL:https://api-uat.int.vinid.dev/notification}
notification-center.template-id-send-password-folder=${NOTIFICATION_CENTER_TEMPLATE_ID_PASSWORD_FOLDER:72d4eefe-a1d3-4cd5-8e07-e9a285ef2d89}

encryption.aes.secret-key=${ENCRYPTION_AES_SECRET_KEY:n2r5u8x/A?D(G+Kb}

logging.level.com.netflix.config.sources.URLConfigurationSource=ERROR

ops.params.default-transaction-channel: ${OPS_PARAMS_DEFAULT_TRANSACTION_CHANNEL:OPS}
ops.params.default-transaction-service-code: ${OPS_PARAMS_DEFAULT_TRANSACTION_SERVICE_CODE:OLSTransactionRequest}
spring.profiles.active=local
app.file.max-line:${FILE_MAX_LINE:10000}
app.file.max-line-2:${FILE_MAX_LINE_2:5000}
app.file.max-line-3:${FILE_MAX_LINE_3:50000}

request.header.accept-language=${ACCEPT_LANGUAGE}
