clzName: MemberAttributeAdjustExport
elements:
  - entries:
      - { index: 0, propertyName: loyaltyId, type: String }
      - { index: 1, propertyName: memberAttribute, type: String }
      - { index: 2, propertyName: attributeValue, type: String }
      - { index: 3, propertyName: startDate, type: String }
      - { index: 4, propertyName: endDate, type: String }
      - { index: 5, propertyName: status, type: String }
      - { index: 6, propertyName: processStatus, type: String }
      - { index: 7, propertyName: errorMessage, type: String }
    sheetName: Member attribute adjust n