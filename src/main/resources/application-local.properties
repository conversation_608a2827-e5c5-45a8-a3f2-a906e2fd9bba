server.port=8080
server.servlet.context-path=/oneloyalty-partner-ops
spring.jpa.properties.hibernate.default_schema=OLOYALTY

MEMBER_SERVICE_URL=https://api-qc.int.vinid.dev/oneloyalty-member
CARD_SERVICE_URL=https://api-qc.int.vinid.dev/oneloyalty-card/v1
VOUCHER_SERVICE_URL=https://api-qc.vinid.dev/onevc/tcb-vsm-ops
VOUCHER_SERVICE_INTERNAL_URL=https://api-qc.vinid.dev/onevc/internal
ONEU_VOUCHER_SERVICE_INTERNAL_URL=https://api-qc.vinid.dev/onevc
VOUCHER_SERVICE_INTERNAL_BASIC_AUTH=
CARD_SERVICE_BASE_URL=https://api-qc.int.vinid.dev/oneloyalty-card
ONELOYALTY_SERVICE_BASE_URL=https://api-qc.int.vinid.dev/oneloyalty-service
ONELOYALTY_AUDIT_LOG_BASE_URL=https://api-qc.int.vinid.dev/oneloyalty-audit-log
ONELOYALTY_RULES_BASE_URL=https://api-qc.int.vinid.dev/oneloyalty-rules
ONELOYALTY_SCHEME_BASE_URL=https://api-qc.int.vinid.dev/oneloyalty-scheme
MAKER_CHECKER_URL_CHANGE=https://api-qc.int.vinid.dev/makerchecker/v1/changes
OPS_AUTHEN_URL=https://api-merchant-qc.int.vinid.dev/tcb-int/v1
MAKER_CHECKER_URL=https://api-qc.int.vinid.dev/makerchecker/v1
MAKER_CHECKER_INTERNAL_URL=https://api-qc.vinid.dev/loyalty-mace/v1/request
MAKER_CHECKER_INTERNAL_API_KEY=secret_lp
OPS_EVOUCHER_URL=https://api-qc.int.vinid.dev/evoucher/ops/v1
ONELOYALTY_INTEGRATION_OPS=https://api-qc.int.vinid.dev/oneloyalty-partner-ops-integration
ONELOYALTY_CPM_SERVICE_BASE_URL=https://api-qc.int.vinid.dev/oneloyalty-cpm-service

ONELOYALTY_AIRFLOW_SERVICE_BASE_URL=https://loyalty-airflow-qc.int.vinid.dev/

ES_ENDPOINTS=es.int.vinid.dev:443
ES_SCHEME_MANAGEMENT_INDEX=db-oneloyalty-scheme-management-qc
ES_PINGABLE=false
ES_SSL=true

VD_CARD_PREFIX_CODES=6666
VD_CARD_TYPE_CODES=101
TCB_CORPORATION_CODE=TCB
VGC_BUSINESS_CODE=VGC
TCB_BATCH_ADJ_TXN_BUCKET_DIR=loyalty/ops/qc/tcb-batch-adj-txn

CPR_SFTP_HOST=localhost
CPR_SFTP_PORT=2022
CPR_SFTP_REMOTE_FOLDER=/reconcile/oneloyalty-partner-ops/qc/member_card
CPR_SFTP_PINGABLE=false

GCP_STORAGE_OPS_BUCKET_NAME=vinid-loyalty-partner-ops-internal-np
GCP_STORAGE_OPS_SERVICE_ACCOUNT_KEY=conf/gcp/storage/gcp-storage-key.json
APP_ENVIRONMENT=LOCAL
ATTRIBUTE_VALUE_SECRET=2909@123
CPR_SFTP_PASSWORD=D3jTV7+5uzO9azzF2zQ=
CPR_SFTP_USERNAME=loyalty_tcb
ES_PASSWORD=rHmmwWAvFZLJev8
ES_TRANSACTION_HISTORY_ENABLE=false
ES_USERNAME=<EMAIL>
JDBC_PASSWORD=oloyalty123
JDBC_URL=********************************************************************************************************************************************)))
JDBC_USERNAME=oloyalty
KAFKA_KEY_PASSWORD=Y2WD9pUuVzpxXHW
KAFKA_KEYSTORE_PASSWORD=Y2WD9pUuVzpxXHW
KAFKA_TRUSTSTORE_PASSWORD=Vinid@321
OPS_BASIC_AUTH=internaldev:Internaldev!1234
OPS_PUBLIC_KEY=MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA6T7uDn22SVA2VgVpxQrbywRC0iOSxXr53MAwtZqElhJIQsgBwB2tuiPgXdiaI7iC63+HbmK/X9CCSY/dNdGHP/agW0/FoGqa44uyMrvGakUQQB9Ur5qToIQvdYfiQ9sPwg38KZFQp2HCPldYuOwjxGXv6PlzjfavymeJ/YswJ6QD1ajuyidIz7J6wwgY/hnFRWXikPRrhoWQJqa7rko0+4MnMG2NpvkdBvAVKvUk3qdqpu0svtHeTovVkvynpbL38xurhM1LyWGm/AqAeHyDYK1+pdKm75Y3eNnTOgnfWLWBSVN7kT/r0VRvIdR68Gui8uyYbrEW1KxBQvkXWGiXr7G8e42ilucV032Es6vi3tEb2XSG5AElqZ9i0zUP3ehChhXBYWqAdpf4NA5wREBFH5ZHWE2SMmFgJlN3hYSGUScZMzUVIShMUHt67bitDmO3LtYi8INMtU7K7BU5zqn2siaylBuEjy6VcNFbN92zxFPDaqu0HVNSCqtGpj1G0vxd/I6kclaetJdPYsENK373Y8RMHWrra+TWcgaFY6G/rWV7FjR8bW8AD9YyUPv8NstcpJPJnqAhJGNP0HeciF31ZakTzluhi9Z8APrNhyPMQaNsHzlWs6q4NmanYhU0rj5mXFL3sqcgaO4Z4izovi4SIw3lJ+hHFUpWn3gRta7mRUcCAwEAAQ==
SEQ_REDIS_HOST=loyalty-redis-master.default.svc.cluster.local
SEQ_REDIS_PASSWORD=ZTqVAbQiuzLS
VMM_MERCHANT_SERVICE_BASIC_AUTH=internal_service:3e0f0115e0191e51
VMM_PRODUCT_SERVICE_BASIC_AUTH=integration_hub:Vincart@1234
ONELOYALTY_MASTER_WORKER_BASE_URL=localhost:8082/oneloyalty-mw-service/v1
SAP_BASE_URL=https://api-sap1mg-dev.vinid.dev/RESTAdapter/salesorder
SAP_BASIC_AUTH=SAP1MG:SAP#@@&1MG
JPA_SHOW_SQL=true
X_BUSINESS_CODE=TCB
ACCEPT_LANGUAGE=vi
