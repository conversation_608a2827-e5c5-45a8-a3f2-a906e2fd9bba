package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

@Getter
@Builder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class BudgetRes {
    private Integer id;

    private ShortEntityRes program;

    private String name;

    private String code;

    private ECommonStatus status;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date startDate;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date endDate;

    private EBoolean isSubBudgetConfig;
}
