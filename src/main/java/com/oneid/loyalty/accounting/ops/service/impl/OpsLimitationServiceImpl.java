package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.AttributeValueFactory;
import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerStatus;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.constant.LimitResetType;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyRuleFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.kafka.event.ResetRuleEvent;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateLimitationReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterLimitationAvailableReq;
import com.oneid.loyalty.accounting.ops.model.req.ResetLimitationReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleConditionReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleReq;
import com.oneid.loyalty.accounting.ops.model.res.CounterShortInformationRes;
import com.oneid.loyalty.accounting.ops.model.res.CounterStatisticRes;
import com.oneid.loyalty.accounting.ops.model.res.DropdownRes;
import com.oneid.loyalty.accounting.ops.model.res.LimitationDetailAvailableRes;
import com.oneid.loyalty.accounting.ops.model.res.LimitationInReviewDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.LimitationInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.LimitationRes;
import com.oneid.loyalty.accounting.ops.model.res.LimitationStatisticRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.SumStatisticRes;
import com.oneid.loyalty.accounting.ops.service.OpsConditionService;
import com.oneid.loyalty.accounting.ops.service.OpsCounterService;
import com.oneid.loyalty.accounting.ops.service.OpsLimitationService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleRequestService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.AuditorAwareUtil;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.MongoSearchUtil;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Counter;
import com.oneid.oneloyalty.common.entity.CounterHistory;
import com.oneid.oneloyalty.common.entity.CounterLevelObject;
import com.oneid.oneloyalty.common.entity.Limitation;
import com.oneid.oneloyalty.common.entity.LimitationRequest;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramAttributeServiceType;
import com.oneid.oneloyalty.common.entity.ProgramTransactionAttribute;
import com.oneid.oneloyalty.common.entity.Rule;
import com.oneid.oneloyalty.common.entity.RuleCondition;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CounterHistoryRepository;
import com.oneid.oneloyalty.common.repository.CounterRepository;
import com.oneid.oneloyalty.common.repository.CounterRequestRepository;
import com.oneid.oneloyalty.common.repository.LimitationRepository;
import com.oneid.oneloyalty.common.repository.LimitationRequestRepository;
import com.oneid.oneloyalty.common.repository.ProgramAttributeServiceTypeRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.RuleConditionRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CounterHistoryService;
import com.oneid.oneloyalty.common.service.CounterLevelObjectService;
import com.oneid.oneloyalty.common.service.CounterService;
import com.oneid.oneloyalty.common.service.LimitationService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.service.RuleService;
import com.oneid.oneloyalty.common.util.LogData;
import com.oneid.oneloyalty.common.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.DoubleSummaryStatistics;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class OpsLimitationServiceImpl implements OpsLimitationService {

    @Value("${maker-checker.module.limitation:limitation}")
    private String moduleId;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private OpsRuleRequestService opsRuleRequestService;

    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private CounterRepository counterRepository;

    @Autowired
    private CounterService counterService;

    @Autowired
    private LimitationService limitationService;

    @Autowired
    private LimitationRepository limitationRepository;

    @Autowired
    private LimitationRequestRepository requestRepository;

    @Autowired
    private CounterRequestRepository counterRequestRepository;

    @Autowired
    private CounterHistoryRepository counterHistoryRepository;

    @Autowired
    private ProgramRepository programRepository;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private OpsRuleService opsRuleService;

    @Autowired
    private CounterHistoryService counterHistoryService;

    @Autowired
    private OpsCounterService opsCounterService;

    @Autowired
    private AuditorAwareUtil auditorAwareUtil;

    @Autowired
    private OpsConditionService opsConditionService;

    @Autowired
    private RuleService ruleService;

    @Autowired
    private AttributeValueFactory attributeValueFactory;

    @Autowired
    RuleConditionRepository ruleConditionRepository;

    @Autowired
    private ProgramAttributeServiceTypeRepository programAttributeServiceTypeRepository;

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private OneloyaltyRuleFeignClient oneloyaltyRuleFeignClient;

    @Autowired
    private ProgramTransactionAttributeService programTransactionAttributeService;

    @Autowired
    private CounterLevelObjectService counterLevelObjectService;

    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }

    private void validateCounter(Integer counterId) {
        Counter counter = counterService.findActive(counterId);
        List<Limitation> limits = limitationService.findAllByCounterId(counterId);
        if (Objects.nonNull(limits) && limits.size() > 0) {
            throw new BusinessException(ErrorCode.COUNTER_CODE_IS_BEING_USED, null, null, new Object[]{counter.getCode()});
        }
    }

    @Override
    public MakerCheckerInternalMakerRes createRequest(CreateLimitationReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        req.setBusinessId(business.getId());
        programService.findByIdAndBusinessId(req.getProgramId(), business.getId());

        validationLimitCodeDoesNotExist(req.getProgramId(), req.getCode());
        validationLimitCodeDoesNotExistInOtherReqPending(business.getId(), req.getProgramId(), req.getCode());
        validateCounter(req.getCounterId());
        validateStartDateAndEndDate(req.getStartDate(), req.getEndDate());

        req.setRequestType(ERequestType.CREATE);

        return makerCheckerInternalFeignClient
                .makerDefault(EMakerCheckerType.LIMITATION, UUID.randomUUID().toString(), req, req.getMadeReason(), req.isSendEmail());
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        MakerCheckerInternalDataDetailRes detailResData = makerCheckerInternalFeignClient.previewChecker(req.getId(), EMakerCheckerType.LIMITATION);
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            if (detailResData != null && detailResData.getPayload() != null) {
                resetRuleReqs = processApprove(detailResData);

                // Check reset rule
                if (CollectionUtils.isNotEmpty(resetRuleReqs)) {
                    APIFeignInternalResponse<?> ruleRes = oneloyaltyRuleFeignClient.checkReset(resetRuleReqs);
                    if (ruleRes.getMeta().getCode() != 200) {
                        throw new BusinessException(ErrorCode.RESET_RULE_FAILED);
                    }
                }
            }
        }
        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> plAprroved = makerCheckerInternalFeignClient.checkerDefault(EMakerCheckerType.LIMITATION, req, null);

        if (plAprroved.getMeta().getCode() != HttpStatus.OK.value()) {
            throw new BusinessException(
                    ErrorCode.MAKER_CHECKER_CANNOT_CHANGE_STATUS,
                    "Checker fail",
                    LogData.createLogData()
                            .append("id", req.getId()));

        }
        if (EApprovalStatus.APPROVED == req.getStatus() && CollectionUtils.isNotEmpty(resetRuleReqs)) {
            ResetRuleEvent<?> ruleEvent = ResetRuleEvent.builder()
                    .id(UUID.randomUUID().toString())
                    .eventType(ResetRuleEvent.RESET_RULE_EVENT_TYPE)
                    .timeStamp(System.currentTimeMillis())
                    .payload(resetRuleReqs)
                    .build();
            applicationEventPublisher.publishEvent(ruleEvent);
        }
    }

    @Override
    public void cancelInReview(CancelReq req) {
        MakerCheckerInternalDataDetailRes detailRes = makerCheckerInternalFeignClient
                .previewDetailDefault(EMakerCheckerType.LIMITATION, String.valueOf(req.getId()));
        if (!EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue().equals(detailRes.getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to cancel",
                    LogData.createLogData().append("id", String.valueOf(req.getId())));
        }
        makerCheckerInternalFeignClient.cancelDefault(EMakerCheckerType.LIMITATION, req);
    }

    private void validateApprove(CreateLimitationReq payload) {
        businessService.findActive(payload.getBusinessId());
        programService.findActive(payload.getProgramId());
        Counter counter = counterService.findById(payload.getCounterId()).orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_NOT_FOUND));
        List<Limitation> allByCounterId = limitationService.findAllByCounterId(payload.getCounterId());
        if (payload.getLimitId() == null && !allByCounterId.isEmpty()) {
            throw new BusinessException(ErrorCode.COUNTER_ID_USED_FOR_OTHER_LIMITATION, null, null, new Object[]{counter.getCode()});
        }
    }

    private List<ResetRuleReq> processApprove(MakerCheckerInternalDataDetailRes detailResData) {
        CreateLimitationReq payload = this.jsonMapper.convertValue(detailResData.getPayload(), CreateLimitationReq.class);
        String createdBy = null;
        String updatedBy;
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        if (payload.getResetCounterValue() != null) {
            ResetLimitationReq req = new ResetLimitationReq();
            req.setLimitationId(payload.getLimitId());
            req.setResetType(payload.getResetType());
            req.setResetValue(payload.getResetCounterValue());
            req.setNewDescription(payload.getNewDescriptionCounter());
            approveResetCounting(req);
            return resetRuleReqs;
        }
        validateApprove(payload);
        Limitation limitation;
        if (payload.getLimitId() != null) {
            limitation = limitationService.find(payload.getLimitId()).orElseThrow(() -> new BusinessException(ErrorCode.LIMITATION_NOT_FOUND));
            updatedBy = detailResData.getMadeByUserName();
        } else {
            if (!DateTimes.toDate(payload.getStartDate()).after(detailResData.getMadeDateToDate())) {
                throw new BusinessException(ErrorCode.LIMITATION_START_DATE_INVALID, null, null);
            }
            limitation = new Limitation();
            limitation.setName(payload.getName());
            limitation.setCode(payload.getCode());
            limitation.setProgramId(payload.getProgramId());
            limitation.setStartDate(DateTimes.toDate(payload.getStartDate()));
            limitation.setCounterId(payload.getCounterId());
            limitation.setAllowWithRemainingValue(EBoolean.of(payload.getAllowWithRemainingValue()));
            limitation.setEnableResetCounter(EBoolean.of(payload.getAllowResetCounter()));
            updatedBy = createdBy = detailResData.getMadeByUserName();

            // Create rules
            Counter counter = counterService.findActive(payload.getCounterId());
            List<Rule> rules = ruleService.findByProgramIdAndServiceTypeAndServiceCode(payload.getProgramId(), EServiceType.COUNTER, counter.getCode());
            for (Rule rule : rules) {
                if (rule.getStatus().equals(ECommonStatus.ACTIVE)) {
                    Rule limitRule = createLimitRule(limitation.getCode(), rule);
                    List<RuleCondition> conditions = ruleConditionRepository.findByRuleId(rule.getId());
                    List<ResetRuleReq.ConditionReq> conditionReqs = new ArrayList<>();
                    for (RuleCondition condition : conditions) {
                        if (condition.getStatus().equals(ECommonStatus.ACTIVE)) {
                            List<ProgramAttributeServiceType> isExistConfig = programAttributeServiceTypeRepository.findByProgramIdAndServiceTypeAndAttributeIn(payload.getProgramId(),
                                    EServiceType.LIMITATION, Collections.singletonList(condition.getAttribute()));
                            if (isExistConfig.isEmpty()) {
                                throw new OpsBusinessException(OpsErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_NOT_FOUND,
                                        "Program attribute service type not found", null, new Object[]{condition.getAttribute()});
                            }
                            RuleCondition ruleCondition = new RuleCondition();
                            ruleCondition.setRuleId(limitRule.getId());
                            ruleCondition.setAttribute(condition.getAttribute());
                            ruleCondition.setOperator(condition.getOperator());
                            ruleCondition.setValue(condition.getValue());
                            ruleCondition.setDataType(condition.getDataType());
                            ruleCondition.setStatus(condition.getStatus());
                            RuleCondition ruleConditionSaved = ruleConditionRepository.save(ruleCondition);
                            conditionReqs.add(ResetRuleReq.buildConditionReq(ruleConditionSaved));
                        }
                    }
                    ResetRuleReq resetRuleReq = ResetRuleReq.buildRuleReq(limitRule);
                    resetRuleReq.setConditions(conditionReqs);
                    resetRuleReqs.add(resetRuleReq);
                }
            }
        }
        if (!DateTimes.toDate(payload.getEndDate()).after(limitation.getStartDate())) {
            throw new BusinessException(ErrorCode.LIMITATION_END_DATE_INVALID, null, null);
        }

        limitation.setThreshold(payload.getThreshold());
        limitation.setWarningThreshold(payload.getWarningThreshold());
        limitation.setStatus(ECommonStatus.of(payload.getStatus()));
        limitation.setDescription(payload.getDescription());
        limitation.setEndDate(DateTimes.toDate(payload.getEndDate()));
        limitation.setVersion(detailResData.getVersion());
        limitation.setRequestCode(detailResData.getRequestCode());

        String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
        opsReqPendingValidator.updateInfoChecker(limitation, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
        limitationService.save(limitation);
        return resetRuleReqs;
    }

    private Rule createLimitRule(String limitCode, Rule rule) {
        Rule limitRule = new Rule();
        limitRule.setProgramId(rule.getProgramId());
        limitRule.setCode(String.format("%s_%s", EServiceType.LIMITATION, UUID.randomUUID()));
        limitRule.setName(rule.getName());
        limitRule.setDescription(rule.getDescription());
        limitRule.setRuleLogic(rule.getRuleLogic());
        limitRule.setServiceType(EServiceType.LIMITATION);
        limitRule.setServiceCode(limitCode);
        limitRule.setStatus(rule.getStatus());
        limitRule.setStartDate(rule.getStartDate());
        limitRule.setEndDate(rule.getEndDate());
        ruleService.save(limitRule);
        return limitRule;
    }

    @Override
    public LimitationRes getAvailableLimitationByRequestId(Integer limitationId) {
        return getLimitationById(limitationId);
    }

    @Override
    public LimitationDetailAvailableRes getAvailableLimitationDetailById(Integer id) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Limitation limitation = limitationRepository.findById(id)
                .orElseThrow(() -> new BusinessException(ErrorCode.LIMITATION_NOT_FOUND, "Limitation not found", null, new Integer[]{id}));
        programService.findByIdAndBusinessId(limitation.getProgramId(), business.getId());
        return getLimitationDetail(limitation);
    }

    private LimitationDetailAvailableRes getLimitationDetail(Limitation limitation) {
        Program program = programService.find(limitation.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        Counter counter = counterRepository.findById(limitation.getCounterId()).orElseThrow(
                () -> new BusinessException(ErrorCode.COUNTER_NOT_FOUND, "Counter not found", null)
        );

        List<RuleRes> limitationRules = opsRuleService.getRule(
                limitation.getCode(),
                limitation.getProgramId(),
                EServiceType.LIMITATION
        );

        List<RuleRes> counterRules = opsRuleService.getRule(
                counter.getCode(),
                limitation.getProgramId(),
                EServiceType.COUNTER
        );

        Integer numberDayCounterExpire = null;
        Date current = new Date();
        if (counter.getEndDate().before(current)) {
            numberDayCounterExpire = -1;
        } else {
            long milliSecondCurrentDay = current.getTime()
                    - current.getTime() % DateTimes.DAY_IN_MILLIS;

            long milliSecondExpired = counter.getEndDate().getTime() - milliSecondCurrentDay;

            numberDayCounterExpire = Math.toIntExact(milliSecondExpired / DateTimes.DAY_IN_MILLIS);
        }
        String editKey = opsReqPendingValidator.generateEditKey(limitation.getRequestCode(), limitation.getVersion());

        CounterLevelObject counterLevelObject = null;
        ProgramTransactionAttribute programTransactionAttribute;

        if (Objects.nonNull(counter.getLevel())) {
            counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(program.getId(), counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
        }
        programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(program.getId(), counter.getCounterLevelAttribute()).orElse(null);
        return LimitationDetailAvailableRes.builder()
                .id(limitation.getId())
                .editKey(editKey)
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .counter(CounterShortInformationRes.builder()
                        .id(counter.getId())
                        .name(counter.getName())
                        .code(counter.getCode())
                        .counterType(counter.getType())
                        .startDate(counter.getStartDate())
                        .endDate(counter.getEndDate())
                        .period(counter.getPeriod())
                        .counterLevel(Objects.nonNull(counterLevelObject)
                                ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                        .counterAttribute(counter.getCounterAttribute())
                        .numberDayCounterExpire(numberDayCounterExpire)
                        .counterStatus(counter.getStatus())
                        .rules(counterRules)
                        .resetType(limitation.getResetType() != null ? limitation.getResetType() : LimitResetType.RESET_VALUE)
                        .resetValue(counter.getResetValue())
                        .description(counter.getDescription())
                        .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                                ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                                programTransactionAttribute.getName()) : null)
                        .counterLevelType(counter.getLevelType())
                        .build())
                .code(limitation.getCode())
                .name(limitation.getName())
                .description(limitation.getDescription())
                .status(limitation.getStatus())
                .createdBy(limitation.getCreatedBy())
                .updatedBy(limitation.getUpdatedBy())
                .approvedBy(limitation.getApprovedBy())
                .createdAt(limitation.getCreatedAt())
                .updatedAt(limitation.getUpdatedAt())
                .approvedAt(limitation.getApprovedAt())
                .rules(limitationRules)
                .threshold(limitation.getThreshold())
                .warningThreshold(limitation.getWarningThreshold())
                .allowResetCounter(limitation.getEnableResetCounter())
                .allowWithRemainingValue(limitation.getAllowWithRemainingValue())
                .version(limitation.getVersion())
                .startDate(limitation.getStartDate())
                .endDate(limitation.getEndDate())
                .build();
    }

    @Override
    public LimitationDetailAvailableRes getChangeableByRequestId(Integer requestId) {
        Limitation limitation = limitationRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.LIMITATION_REQUEST_NOT_FOUND,
                        "Limitation not found", null, new Integer[]{requestId}));

        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.LIMITATION.getType(), limitation.getRequestCode());

        return getLimitationDetail(limitation);
    }

    @Override
    public Page<LimitationRes> getAvailableLimitations(FilterLimitationAvailableReq req, Pageable pageable) {
        Page<Limitation> limitations = limitationRepository.findAll(req.getSpecificationBuilder(), pageable);
        Program program = programService.findById(req.getProgramId());
        ShortEntityRes programInfo = new ShortEntityRes(program.getId(), program.getName(), program.getCode());
        return new PageImpl<>(
                limitations.getContent().stream().map(limitation -> {
                    Counter counter = counterService.findById(limitation.getCounterId()).orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_NOT_FOUND));
                    CounterLevelObject counterLevelObject = null;
                    ProgramTransactionAttribute programTransactionAttribute = null;
                    if (Objects.nonNull(counter.getLevel())) {
                        counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(program.getId(), counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
                    }
                    programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(program.getId(), counter.getCounterLevelAttribute()).orElse(null);
                    return LimitationRes.builder()
                            .id(limitation.getId())
                            .program(programInfo)
                            .counterId(limitation.getCounterId())
                            .requestId(limitation.getId())
                            .status(limitation.getStatus())
                            .name(limitation.getName())
                            .code(limitation.getCode())
                            .description(limitation.getDescription())
                            .threshold(limitation.getThreshold())
                            .allowResetCounter(limitation.getEnableResetCounter())
                            .allowWithRemainingValue(limitation.getAllowWithRemainingValue())
                            .warningThreshold(limitation.getWarningThreshold())
                            .startDate(limitation.getStartDate())
                            .endDate(limitation.getEndDate())
                            .counter(CounterShortInformationRes.builder()
                                    .id(counter.getId())
                                    .name(counter.getName())
                                    .code(counter.getCode())
                                    .counterType(counter.getType())
                                    .startDate(counter.getStartDate())
                                    .endDate(counter.getEndDate())
                                    .period(counter.getPeriod())
                                    .counterLevel(Objects.nonNull(counterLevelObject)
                                            ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                                    .counterAttribute(counter.getCounterAttribute())
                                    .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                                            ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                                            programTransactionAttribute.getName()) : null)
                                    .counterLevelType(counter.getLevelType())
                                    .build())
                            .build();
                }).collect(Collectors.toList()), pageable, limitations.getTotalElements());
    }

    @Override
    public Page<LimitationInReviewRes> getInReview(Integer programId,
                                                   String code,
                                                   String name,
                                                   ERequestType requestType,
                                                   Long fromStartDate,
                                                   Long toStartDate,
                                                   String status,
                                                   Integer offset,
                                                   Integer limit,
                                                   MakerCheckerInternalPreviewReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Map<String, Object> searchPayload = (new MongoSearchUtil())
                .setFilter("payload.program_id", programId, false, false, false)
                .setFilter("payload.code", code, false, false, false)
                .setFilter("payload.name", name, false, true, false, MongoSearchUtil.OPTION_IGNORE_CASE_INSENSITIVITY)
                .setFilter("payload.request_type", requestType, false, false, false)
                .setFilter("payload.status", status, false, false, false)
                .setFilter("payload.start_date", Objects.nonNull(fromStartDate) && Objects.nonNull(toStartDate) ? MakerCheckerInternalPreviewReq.RangeDateReq.builder().fromDate(fromStartDate).toDate(toStartDate).build() : null, false, false, false)
                .build();

        req.setBusinessId(business.getId());
        req.setProperties(searchPayload);
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(EMakerCheckerType.LIMITATION, req, offset, limit, null);
        List<LimitationInReviewRes> res = previewRes.getData().stream()
                .map(this::convertPreview)
                .collect(Collectors.toList());
        return new PageImpl<>(res, new OffsetBasedPageRequest(offset, limit), previewRes.getMeta().getTotal());
    }

    private LimitationInReviewRes convertPreview(MakerCheckerInternalDataDetailRes data) {
        CreateLimitationReq createLimitationReq = this.jsonMapper.convertValue(data.getPayload(), CreateLimitationReq.class);
        Program program = programService.find(createLimitationReq.getProgramId()).orElse(null);
        return LimitationInReviewRes
                .builder()
                .requestId(data.getId())
                .program(program != null ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .code(createLimitationReq.getCode())
                .name(createLimitationReq.getName())
                .startDate(DateTimes.toDate(createLimitationReq.getStartDate()))
                .endDate(DateTimes.toDate(createLimitationReq.getEndDate()))
                .approvalStatus(EApprovalStatus.valueOf(data.getStatus()))
                .status(ECommonStatus.of(createLimitationReq.getStatus()))
                .createdBy(data.getMadeByUserName())
                .createdAt(data.getMadeDateToDate())
                .approvedBy(data.getCheckedByUserName())
                .approvedAt(data.getCheckedDateToDate())
                .requestType(createLimitationReq.getRequestType())
                .build();
    }

    private String getLimitationRequestType(Integer limitationId, BigDecimal resetCounterValue) {
        if (limitationId != null && resetCounterValue != null) {
            return "RESET_COUNTER";
        } else if (limitationId != null && resetCounterValue == null) {
            return ERequestType.EDIT.getValue();
        } else if (limitationId == null && resetCounterValue == null) {
            return ERequestType.CREATE.getValue();
        }
        return "UNKNOWN";
    }

    @Override
    public List<CounterShortInformationRes> getAvailableCounters(Integer businessId) {
        if (Objects.isNull(businessId)) {
            Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
            businessId = business.getId();
        }
        List<Counter> counters = counterRepository.findActiveByBusinessIdAndServiceType(businessId,
                EServiceType.LIMITATION, new Date());
        return this.mapToListCounterShortInformationRes(counters);
    }

    @Override
    public List<CounterShortInformationRes> getAvailableCountersByBusinessIdAndProgramId(Integer programId) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());

        List<Counter> counters = counterRepository.findAvailableByBusinessIdAndProgramIdAndServiceType(
                business.getId(), programId, EServiceType.LIMITATION, new Date()
        );
        return this.mapToListCounterShortInformationRes(counters);
    }

    @Override
    public List<CounterShortInformationRes> getCountersByBusinessIdAndProgramIdAndStatus(Integer programId, String status) {
        ECommonStatus eStatus = ECommonStatus.of(status);
        if (eStatus == null) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Invalid status", null);
        }
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        SpecificationBuilder specification = new SpecificationBuilder();
        specification.add(new SearchCriteria("serviceType", EServiceType.LIMITATION, SearchOperation.EQUAL));
        if (status != null) {
            specification.add(new SearchCriteria("status", eStatus, SearchOperation.EQUAL));
        }
        specification.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));
        specification.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));
        specification.add(new SearchCriteria("endDate", new Date(), SearchOperation.GREATER_THAN_EQUAL_DATE));
        List<Counter> counters = counterRepository.findAll(specification);
        return this.mapToListCounterShortInformationRes(counters);
    }

    @Override
    public MakerCheckerInternalMakerRes update(Integer limitationId, CreateLimitationReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Limitation limitation = limitationService.find(limitationId).orElseThrow(
                () -> new BusinessException(ErrorCode.LIMITATION_NOT_FOUND, null, null)
        );

        if (!DateTimes.toDate(req.getEndDate()).after(limitation.getStartDate())) {
            throw new BusinessException(ErrorCode.LIMITATION_END_DATE_INVALID, null, null);
        }

        programService.findByIdAndBusinessId(limitation.getProgramId(), business.getId());
        opsReqPendingValidator.verifyEditKey(req.getEditKey(), limitation.getRequestCode(), limitation.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.LIMITATION.getType(), limitation.getRequestCode());

        req.setBusinessId(business.getId());
        req.setLimitId(limitationId);
        req.setProgramId(limitation.getProgramId());
        req.setCode(limitation.getCode());
        req.setName(limitation.getName());
        req.setStartDate(DateTimes.toEpochSecond(limitation.getStartDate()));
        req.setCounterId(limitation.getCounterId());
        req.setAllowResetCounter(limitation.getEnableResetCounter().getValue());
        req.setAllowWithRemainingValue(limitation.getAllowWithRemainingValue().getValue());
        req.setRequestType(ERequestType.EDIT);

        return makerCheckerInternalFeignClient
                .makerDefault(EMakerCheckerType.LIMITATION, limitation.getRequestCode(), req, req.getMadeReason(), req.isSendEmail());
    }

    @Override
    public MakerCheckerInternalMakerRes resetCounterRequest(Integer limitationId, CreateLimitationReq req) {
        if (req.getResetCounterValue() == null) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "missing reset counter value", null);
        }
        Limitation limitation = limitationService.find(limitationId).orElseThrow(
                () -> new BusinessException(ErrorCode.LIMITATION_NOT_FOUND, null, null)
        );
        if (LimitResetType.SUBTRACTION_AMOUNT.equals(req.getResetType())) {
            SumStatisticRes summaryStatistic = getSummaryStatistic(limitationId);
            if (Objects.nonNull(summaryStatistic.getMin()) && req.getResetCounterValue().compareTo(BigDecimal.valueOf(summaryStatistic.getMin())) > 0) {
                throw new OpsBusinessException(OpsErrorCode.RESET_VALUE_MUST_LESS_COUNTER_ACCUMULATION,
                        "Reset value must be lower than counter accumulation", null);
            }
        }
        opsReqPendingValidator.verifyEditKey(req.getEditKey(), limitation.getRequestCode(), limitation.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.LIMITATION.getType(), limitation.getRequestCode());

        Program program = programService.findActive(limitation.getProgramId());
        businessService.findActive(program.getBusinessId());
        req.setLimitId(limitationId);
        MakerCheckerInternalMakerReq<CreateLimitationReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateLimitationReq>builder()
                .requestCode(limitation.getRequestCode())
                .requestName(EMakerCheckerType.LIMITATION.getName())
                .payload(req)
                .build();
        return makerCheckerInternalFeignClient
                .makerDefault(EMakerCheckerType.LIMITATION, UUID.randomUUID().toString(), createFeignInternalReq);
    }

    @Override
    public SumStatisticRes getSummaryStatistic(Integer limitationId) {
        SumStatisticRes result = new SumStatisticRes();
        List<LimitationStatisticRes.History> histories = limitationStatistic(limitationId, null, null, null, ECommonStatus.ACTIVE, Pageable.unpaged()).getHistories();
        DoubleSummaryStatistics statistics = histories
                .stream()
                .map(LimitationStatisticRes.History::getTotalCounted)
                .filter(Objects::nonNull)
                .collect(Collectors.summarizingDouble(BigDecimal::intValue));
        if (statistics.getCount() != 0) {
            result.setMax(statistics.getMax());
            result.setMin(statistics.getMin());
        }
        return result;
    }

    @Override
    public LimitationInReviewDetailRes getInReviewLimitationRequestById(String id) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(EMakerCheckerType.LIMITATION.getType(), id);
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Program - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.LIMITATION.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Program - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }

        return convertPreviewDetail(previewDetailRes.getData());
    }

    private LimitationInReviewDetailRes convertPreviewDetail(MakerCheckerInternalDataDetailRes data) {
        CreateLimitationReq createLimitationReq = this.jsonMapper.convertValue(data.getPayload(), CreateLimitationReq.class);
        Business business = businessService.find(createLimitationReq.getBusinessId()).orElse(null);
        Program program = programService.find(createLimitationReq.getProgramId()).orElse(null);
        Counter counter = counterService.findById(createLimitationReq.getCounterId()).orElse(null);
        CounterLevelObject counterLevelObject = null;
        ProgramTransactionAttribute programTransactionAttribute = null;
        if (Objects.nonNull(counter.getLevel())) {
            counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(program.getId(), counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
        }
        programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(program.getId(), counter.getCounterLevelAttribute()).orElse(null);
        return LimitationInReviewDetailRes.builder()
                .requestId(data.getId())
                .business(business != null ? new ShortEntityRes(business.getId(), business.getName(), business.getCode()) : null)
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .status(createLimitationReq.getStatus())
                .code(createLimitationReq.getCode())
                .name(createLimitationReq.getName())
                .description(createLimitationReq.getDescription())
                .startDate(DateTimes.toDate(createLimitationReq.getStartDate()))
                .endDate(DateTimes.toDate(createLimitationReq.getEndDate()))
                .approvalStatus(EApprovalStatus.valueOf(data.getStatus()))
                .allowResetCounter(EBoolean.of(createLimitationReq.getAllowResetCounter()))
                .createdBy(data.getMadeByUserName())
                .createdAt(data.getMadeDateToDate())
                .approvedBy(data.getCheckedByUserName())
                .approvedAt(data.getCheckedDateToDate())
                .madeReason(data.getMadeReason())
                .reason(data.getComment())
                .requestType(getLimitationRequestType(createLimitationReq.getLimitId(), createLimitationReq.getResetCounterValue()))
                .counter(CounterShortInformationRes
                        .builder()
                        .code(counter.getCode())
                        .counterType(counter.getType())
                        .name(counter.getName())
                        .startDate(counter.getStartDate())
                        .endDate(counter.getEndDate())
                        .counterLevel(Objects.nonNull(counterLevelObject)
                                ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                        .period(counter.getPeriod())
                        .id(counter.getId())
                        .counterStatus(counter.getStatus())
                        .counterAttribute(counter.getCounterAttribute())
                        .rules(opsRuleService.getRule(counter.getCode(), createLimitationReq.getProgramId(), EServiceType.COUNTER))
                        .resetType(createLimitationReq.getResetType() != null ? createLimitationReq.getResetType() : LimitResetType.RESET_VALUE)
                        .resetValue(createLimitationReq.getResetCounterValue() != null ? createLimitationReq.getResetCounterValue() : counter.getResetValue())
                        .description(counter.getDescription())
                        .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                                ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                                programTransactionAttribute.getName()) : null)
                        .counterLevelType(counter.getLevelType())
                        .build())
                .allowWithRemainingValue(EBoolean.of(createLimitationReq.getAllowWithRemainingValue()))
                .threshold(createLimitationReq.getThreshold())
                .warningThreshold(createLimitationReq.getWarningThreshold())
                .build();
    }

    @Override
    public List<RuleRes> getAvailableCounterRuleById(Integer id) {

        Counter counter = counterRepository.findById(id).orElseThrow(
                () -> new BusinessException(ErrorCode.COUNTER_NOT_FOUND, "Counter not found", null)
        );
        return opsRuleService.getRule(counter.getCode(), counter.getProgramId(), EServiceType.COUNTER);
    }

    @Override
    @Transactional
    public void approveResetCounting(ResetLimitationReq req) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        OPSAuthenticatedPrincipal opsAuthenticatedPrincipal = (OPSAuthenticatedPrincipal) authentication.getPrincipal();
        String currentAuditor = opsAuthenticatedPrincipal.getUserName();
        Integer id = req.getLimitationId();
        BigDecimal resetValue = BigDecimal.ZERO;
        if (LimitResetType.SUBTRACTION_AMOUNT.equals(req.getResetType())) {
            SumStatisticRes summaryStatistic = getSummaryStatistic(id);
            if (Objects.nonNull(summaryStatistic.getMin())) {
                if (req.getResetValue().compareTo(BigDecimal.valueOf(summaryStatistic.getMin())) > 0) {
                    throw new OpsBusinessException(OpsErrorCode.RESET_VALUE_MUST_LESS_COUNTER_ACCUMULATION,
                            "Reset value must be lower than counter accumulation", null);
                } else {
                    resetValue = BigDecimal.valueOf(summaryStatistic.getMin()).subtract(req.getResetValue());
                }
            }
        } else {
            resetValue = req.getResetValue();
        }
        Limitation limitation = limitationService.findActive(id);

        Date currentTime = DateTimes.currentTime();
        if (currentTime.before(limitation.getStartDate()) || currentTime.after(limitation.getEndDate())) {
            throw new BusinessException(
                    ErrorCode.LIMITATION_NOT_ACTIVE,
                    "Limitation is not active",
                    LogData.createLogData().append("limit_id", id)
            );
        }

        if (!EBoolean.YES.equals(limitation.getEnableResetCounter())) {
            throw new BusinessException(
                    ErrorCode.LIMITATION_NOT_ENABLE_RESET,
                    "Limitation is not enable reset counter value",
                    LogData.createLogData().append("limit_id", id)
            );
        }

        // Check reset value mus less than limit threshold
        if (resetValue.compareTo(limitation.getThreshold()) > 0) {
            throw new BusinessException(
                    ErrorCode.LIMITATION_RESET_COUNTER_INVALID_RESET_VALUE,
                    "The Reset value is greater than limit limit threshold",
                    LogData.createLogData().append("limit_id", id)
            );
        }

        Counter oldCounter = counterService.findActive(limitation.getCounterId());
        if (currentTime.before(oldCounter.getStartDate()) || currentTime.after(oldCounter.getEndDate())) {
            throw new BusinessException(
                    ErrorCode.COUNTER_NOT_ACTIVE,
                    "Counter is not active",
                    LogData.createLogData().append("limit_id", id)
            );
        }

        if (!EServiceType.LIMITATION.equals(oldCounter.getServiceType())) {
            throw new BusinessException(
                    ErrorCode.LIMITATION_NOT_ENABLE_RESET,
                    "The counter service type invalid for limitation",
                    LogData.createLogData().append("limit_id", id)
            );
        }

        if (resetValue.compareTo(BigDecimal.ZERO) > 0 && !ECounterPeriod.NONE.equals(oldCounter.getPeriod())) {
            throw new BusinessException(
                    ErrorCode.LIMITATION_RESET_COUNTER_NOT_SUPPORT_PERIOD,
                    "Counter period type is not support reset",
                    LogData.createLogData().append("limit_id", id)
            );
        }

        // Inactive old counter
        oldCounter.setStatus(ECommonStatus.INACTIVE);
        counterRepository.save(oldCounter);

        // Create new Counter
        Counter newCounter = (Counter) SerializationUtils.clone(oldCounter);
        newCounter.setId(null);
        newCounter.setStatus(ECommonStatus.ACTIVE);
        newCounter.setResetValue(resetValue);
        newCounter.setApprovedAt(new Date());
        newCounter.setApprovedBy(currentAuditor);
        newCounter.setCreatedAt(new Date());
        newCounter.setUpdatedAt(new Date());
        // Change to new description
        if (!StringUtils.isBlank(req.getNewDescription())) {
            newCounter.setDescription(req.getNewDescription());
        }
        counterRepository.save(newCounter);

        // Update new counter for Limitation
        limitation.setCounterId(newCounter.getId());
        limitation.setResetType(req.getResetType());
        limitation = limitationService.save(limitation);

        // Reset counter history if reset value > 0
        if (resetValue.compareTo(BigDecimal.ZERO) > 0) {
            // Non-Period
            if (ECounterPeriod.NONE.equals(oldCounter.getPeriod())) {
                counterHistoryRepository.reset(oldCounter.getId(), newCounter.getId(), resetValue);
            }
        }
    }

    private LimitationRequest createRequest(CreateLimitationReq req, Integer version) {
        Date currentTime = DateTimes.currentTime();

        Business business = businessService.findActive(req.getBusinessId());

        // Validate limitation code;
        Limitation limitation = limitationService.findByProgramIdAndCode(req.getProgramId(), req.getCode());
        if (limitation != null) {
            throw new BusinessException(
                    ErrorCode.LIMITATION_CODE_EXISTED,
                    "Limitation code existed",
                    null
            );
        }

        programService.findActive(req.getProgramId());
        Counter counter = counterRepository.findById(req.getCounterId()).orElse(null);

        if (counter == null || !counter.getBusinessId().equals(req.getBusinessId())) {
            throw new BusinessException(ErrorCode.COUNTER_NOT_FOUND, "Counter not found", null,
                    new CreateLimitationReq[]{req});
        } else if (counter.getStatus() != ECommonStatus.ACTIVE) {
            throw new BusinessException(ErrorCode.COUNTER_NOT_ACTIVE, "Counter not active", null,
                    new CreateLimitationReq[]{req});
        }

        if (req.getStartDate() <= DateTimes.currentTimeSeconds())
            throw new BusinessException(
                    ErrorCode.LIMITATION_START_DATE_INVALID,
                    null,
                    null
            );

        if (req.getEndDate() < DateTimes.currentTimeSeconds() || req.getEndDate() < DateTimes.addDays(req.getStartDate()))
            throw new BusinessException(
                    ErrorCode.LIMITATION_END_DATE_INVALID,
                    null,
                    null
            );

        LimitationRequest entity = new LimitationRequest();

        entity.setBusinessId(business.getId());
        entity.setName(req.getName());
        entity.setCode(req.getCode());
        entity.setDescription(req.getDescription());
        entity.setProgramId(req.getProgramId());
        entity.setStartDate(DateTimes.toDate(req.getStartDate()));
        entity.setEndDate(DateTimes.toDate(req.getEndDate()));
        entity.setCounterId(req.getCounterId());
        entity.setThreshold(req.getThreshold());
        entity.setLimitationStatus(ECommonStatus.of(req.getStatus()));
        entity.setRequestStatus(ECommonStatus.PENDING);
        entity.setApprovalStatus(EApprovalStatus.PENDING);
        entity.setVersion(version);

        return requestRepository.save(entity);
    }

    private LimitationRes getLimitationById(Integer limitationId) {
        Limitation limitation = limitationRepository.findById(limitationId)
                .orElseThrow(() -> new BusinessException(ErrorCode.LIMITATION_REQUEST_NOT_FOUND,
                        "Limitation not found", null, new Integer[]{limitationId}));

        Program program = programService.find(limitation.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        Business business = businessService.findActive(program.getBusinessId());

        List<RuleRes> rules = opsRuleRequestService.getRuleRequests(
                limitation.getId(),
                limitation.getProgramId(),
                EServiceType.LIMITATION
        );

        Counter counter = counterRepository.findById(limitation.getCounterId()).orElseThrow(
                () -> new BusinessException(ErrorCode.COUNTER_NOT_FOUND, "Counter not found", null)
        );

        Integer numberDayCounterExpire = null;
        Date current = new Date();
        if (counter.getEndDate().before(current)) {
            numberDayCounterExpire = -1;
        } else {
            long milliSecondCurrentDay = current.getTime()
                    - current.getTime() % DateTimes.DAY_IN_MILLIS;

            long milliSecondExpired = counter.getEndDate().getTime() - milliSecondCurrentDay;

            numberDayCounterExpire = Math.toIntExact(milliSecondExpired / DateTimes.DAY_IN_MILLIS);
        }

        CounterLevelObject counterLevelObject = null;
        ProgramTransactionAttribute programTransactionAttribute = null;

        if (Objects.nonNull(counter.getLevel())) {
            counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(program.getId(), counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
        }
        programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(program.getId(), counter.getCounterLevelAttribute()).orElse(null);

        return LimitationRes.builder()
                .id(limitation.getId())
                .approvalStatus(EApprovalStatus.APPROVED)
                .version(limitation.getVersion())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .endDate(limitation.getEndDate())
                .rejectedReason(null)
                .status(limitation.getStatus())
                .name(limitation.getName())
                .description(limitation.getDescription())
                .code(limitation.getCode())
                .startDate(limitation.getStartDate())
                .requestStatus(limitation.getStatus())
                .rules(rules)
                .threshold(limitation.getThreshold())
                .warningThreshold(limitation.getWarningThreshold())
                .allowResetCounter(limitation.getEnableResetCounter())
                .allowWithRemainingValue(limitation.getAllowWithRemainingValue())
                .createdBy(limitation.getCreatedBy())
                .updatedBy(limitation.getUpdatedBy())
                .approvedBy(limitation.getApprovedBy())
                .createdAt(DateTimes.toEpochSecond(limitation.getCreatedAt()))
                .updatedAt(DateTimes.toEpochSecond(limitation.getUpdatedAt()))
                .approvedAt(DateTimes.toEpochSecond(limitation.getApprovedAt()))
                .counter(CounterShortInformationRes.builder()
                        .id(counter.getId())
                        .name(counter.getName())
                        .code(counter.getCode())
                        .counterType(counter.getType())
                        .startDate(counter.getStartDate())
                        .endDate(counter.getEndDate())
                        .period(counter.getPeriod())
                        .counterLevel(Objects.nonNull(counterLevelObject)
                                ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                        .counterAttribute(counter.getCounterAttribute())
                        .numberDayCounterExpire(numberDayCounterExpire)
                        .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                                ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                                programTransactionAttribute.getName()) : null)
                        .counterLevelType(counter.getLevelType())
                        .build())
                .build();
    }

    private Page<LimitationRes> pageEmpty(Pageable pageable) {
        return new PageImpl<>(Collections.emptyList(), pageable, 0);
    }

    @Override
    public LimitationStatisticRes limitationStatistic(Integer limitationId, Date startedAt, Date endedAt, String code, ECommonStatus historyStatus, Pageable pageable) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Limitation limitation = limitationService.find(limitationId)
                .orElseThrow(() -> new BusinessException(ErrorCode.LIMITATION_NOT_FOUND, null, null));

        programService.findByIdAndBusinessId(limitation.getProgramId(), business.getId());

        Counter counter = counterService.findById(limitation.getCounterId())
                .orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_NOT_FOUND, null, null));

        SpecificationBuilder specification = new SpecificationBuilder();

        specification.add(new SearchCriteria("counterId", counter.getId(), SearchOperation.EQUAL));

        if (StringUtil.isNotEmpty(code))
            specification.add(new SearchCriteria("code", code, SearchOperation.EQUAL));

        if (!ObjectUtils.isEmpty(historyStatus))
            specification.add(new SearchCriteria("status", historyStatus, SearchOperation.EQUAL));

        //If counterPeriod is not NONE => filter startDate, endDate
        if (!ECounterPeriod.NONE.equals(counter.getPeriod())) {
            if (startedAt == null || endedAt == null) {
                throw new BusinessException(
                        ErrorCode.COUNTER_STATISTIC_REQUIRED_FILTER, null, null,
                        new Object[]{String.format("started_at and ended_at for %s period", counter.getPeriod().getDisplayName().toLowerCase())}
                );
            }

            specification.add(new SearchCriteria("startDate", startedAt, SearchOperation.GREATER_THAN_EQUAL_DATE));
            specification.add(new SearchCriteria("endDate", endedAt, SearchOperation.LESS_THAN_EQUAL_DATE));
        }

        List<CounterHistory> counterHistories = counterHistoryService.find(specification);

        Page<CounterHistory> counterHistoryPage = counterHistoryService.find(specification, pageable);

        BigDecimal sumCounting = BigDecimal.ZERO;
        Set<String> setCode = new HashSet<>();
        Date startDate = new Date();
        Date endDate = counterHistories.isEmpty() ? new Date() : counterHistories.get(0).getEndDate();

        for (CounterHistory c : counterHistories) {
            sumCounting = sumCounting.add(c.getCounting());
            setCode.add(c.getCode());
            if (c.getStartDate() != null && c.getStartDate().before(startDate))
                startDate = c.getStartDate();
            if (c.getEndDate() != null && c.getEndDate().after(endDate))
                endDate = c.getEndDate();
        }


        List<LimitationStatisticRes.History> histories = counterHistoryPage.stream()
                .map(history -> LimitationStatisticRes.History.builder()
                        .code(history.getCode())
                        .createdAt(history.getCreatedAt())
                        .totalCounted(history.getCounting())
                        .threshold(limitation.getThreshold())
                        .reachedThresholdAt(null)
                        .warningThreshold(limitation.getWarningThreshold())
                        .reachedWarningThresholdAt(null)
                        .unit(counter.getCounterAttribute() != null ? counter.getCounterAttribute().getValue() : null)
                        .status(history.getStatus())
                        .build()
                ).collect(Collectors.toList());

        CounterStatisticRes.Statistic statistic = new CounterStatisticRes().new Statistic();
        statistic.setTypePeriod(counter.getPeriod().getValue());
        statistic.setTypeCounted(counter.getCounterAttribute() != null ? counter.getCounterAttribute().getValue() : null);
        statistic.setTypeLevel(counter.getLevelType().getValue());
        if (Objects.nonNull(counter.getLevel())) {
            statistic.setTypeLevelValue(counter.getLevel().getValue());
        } else {
            statistic.setTypeLevelValue(counter.getCounterLevelAttribute());
        }
        statistic.setTotalLevel(setCode.size());
        statistic.setTotalCounted(sumCounting);
        statistic.setTotalPeriod(opsCounterService.mapPeriodToNumber(counter.getPeriod(), startDate, endDate));

        LimitationStatisticRes res = new LimitationStatisticRes();
        res.setHistories(histories);
        res.setStatistic(statistic);
        res.setCounterStatusHeader(opsCounterService.getCounterActiveStatus(counter));
        res.setTotalHistories((int) counterHistoryPage.getTotalElements());

        return res;
    }


    private List<CounterShortInformationRes> mapToListCounterShortInformationRes(List<Counter> counters) {
        return counters.stream().map(
                counter -> {
                    Optional<Program> programOpt = programService.find(counter.getProgramId());

                    CounterLevelObject counterLevelObject = null;
                    ProgramTransactionAttribute programTransactionAttribute = null;

                    if (programOpt.isPresent()) {
                        if (Objects.nonNull(counter.getLevel())) {
                            counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(programOpt.get().getId(), counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
                        }
                        programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(programOpt.get().getId(), counter.getCounterLevelAttribute()).orElse(null);
                    }

                    return CounterShortInformationRes.builder()
                        .id(counter.getId())
                        .name(counter.getName())
                        .code(counter.getCode())
                        .description(counter.getDescription())
                        .counterType(counter.getType())
                        .startDate(counter.getStartDate())
                        .endDate(counter.getEndDate())
                        .period(counter.getPeriod())
                            .counterLevel(Objects.nonNull(counterLevelObject)
                                    ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                        .counterAttribute(counter.getCounterAttribute())
                            .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                                    ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                                    programTransactionAttribute.getName()) : null)
                            .counterLevelType(counter.getLevelType())
                            .build();
                }
        ).collect(Collectors.toList());
    }

    private void validationLimitCodeDoesNotExist(Integer programId, String limitCode) {
        Limitation limitation = limitationService.findByProgramIdAndCode(programId, limitCode);
        if (limitation != null) {
            throw new BusinessException(
                    ErrorCode.LIMITATION_CODE_EXISTED,
                    "Limitation code existed",
                    null
            );
        }
    }

    private void validationLimitCodeDoesNotExistInOtherReqPending(Integer businessId, Integer programId, String limitCode) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .status(EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue())
                .businessId(businessId)
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(EMakerCheckerType.LIMITATION, previewReq, null, null, null);

        Optional<CreateLimitationReq> any = Optional.ofNullable(previewRes.getData()).orElseGet(ArrayList::new).stream()
                .map(data -> this.jsonMapper.convertValue(data.getPayload(), CreateLimitationReq.class))
                .filter(ele -> Objects.nonNull(ele.getCode()))
                .filter(ele -> Objects.nonNull(ele.getProgramId()))
                .filter(ele -> ele.getProgramId().equals(programId) && ele.getCode().equals(limitCode))
                .findAny();
        if (any.isPresent()) {
            throw new BusinessException(ErrorCode.LIMITATION_CODE_EXISTED,
                    "[VALIDATION LIMIT CODE] limit code existed in other requests pending", limitCode);
        }
    }

    private void validateStartDateAndEndDate(Long startDate, Long endDate) {

        if (startDate <= DateTimes.currentTimeSeconds())
            throw new BusinessException(
                    ErrorCode.LIMITATION_START_DATE_INVALID,
                    null,
                    null
            );

        if (endDate <= startDate)
            throw new BusinessException(
                    ErrorCode.LIMITATION_END_DATE_INVALID,
                    null,
                    null
            );
    }

    private void validateRules(
            Integer programId,
            EServiceType serviceType,
            List<RuleReq> rulesReq
    ) {
        Map<String, ConditionAttributeDto> attributeMap = getAttributeMap(programId);

        // Validate rules request
        for (RuleReq ruleReq : rulesReq) {
            validateRule(programId, serviceType, attributeMap, ruleReq);
        }
    }

    private void validateRule(
            Integer programId,
            EServiceType serviceType,
            Map<String, ConditionAttributeDto> attributeMap,
            RuleReq ruleReq
    ) {
        if (!ruleReq.isArchive() && CollectionUtils.isEmpty(ruleReq.getConditions())) {
            throw new BusinessException(
                    OpsErrorCode.RULE_CONDITION_IS_NOT_EMPTY.getValue(),
                    "Rule condition must not empty",
                    null
            );
        }

        // Validate rule code in case create new
        if (ruleReq.getCode() != null && ruleReq.getId() == null) {
            Rule rule = ruleService.findByProgramIdAndCodeAndServiceType(
                    programId,
                    serviceType.getValue(),
                    ruleReq.getCode()
            );
            if (rule != null) {
                throw new BusinessException(
                        ErrorCode.DUPLICATE_RULE_CODE,
                        "Rule code is duplicate",
                        null,
                        new Object[]{ruleReq.getCode()}
                );
            }
        }

        // Validate rule condition
        ruleReq.getConditions().forEach(e -> validateRuleCondition(programId, attributeMap, e));
    }

    private void validateRuleCondition(
            Integer programId,
            Map<String, ConditionAttributeDto> attributeMap,
            RuleConditionReq ruleConditionReq
    ) {
        if (!ruleConditionReq.isArchive()) {
            ConditionAttributeDto conditionAttribute = attributeMap.get(ruleConditionReq.getAttribute());
            if (conditionAttribute == null) {
                throw new BusinessException(
                        ErrorCode.RULE_ATTRIBUTE_NOT_FOUND,
                        "Rule condition attribute is not found",
                        null,
                        new Object[]{ruleConditionReq.getAttribute()}
                );
            }
            ruleConditionReq.setDataTypeDisplay(conditionAttribute.getDataTypeDisplay());

            // Validate rule condition value
            AttributeValueStrategy<?> valueStrategy = attributeValueFactory.lookup(conditionAttribute);
            String conditionValue = valueStrategy.getWriteValue(
                    ruleConditionReq.getAttribute(),
                    ruleConditionReq.getOperator(),
                    ruleConditionReq.getValue(),
                    programId
            );
            if (conditionValue == null) {
                throw new BusinessException(
                        OpsErrorCode.RULE_CONDITION_VALUE_IS_REQUIRED.getValue(),
                        "Rule condition value must not empty",
                        null
                );
            }
        }
    }

    private Map<String, ConditionAttributeDto> getAttributeMap(Integer programId) {
        Collection<ConditionAttributeDto> attributes =
                opsConditionService.conditionAttributeDtos(programId);
        return attributes.stream()
                .collect(
                        Collectors.toMap(
                                ConditionAttributeDto::getAttribute,
                                conditionAttributeDto -> conditionAttributeDto
                        ));
    }
}