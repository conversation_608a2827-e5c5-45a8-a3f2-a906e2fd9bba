package com.oneid.loyalty.accounting.ops.constant;

import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonValue;

public enum ETransferPointFrom {

    RECEIVER("RECEIVER"),
    SENDER("SENDER");

    private String value;

    ETransferPointFrom(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    public static ETransferPointFrom lookup(String value) {
        if (StringUtils.isEmpty(value)) return null;
        return Stream.of(values())
                .filter(each -> each.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}
