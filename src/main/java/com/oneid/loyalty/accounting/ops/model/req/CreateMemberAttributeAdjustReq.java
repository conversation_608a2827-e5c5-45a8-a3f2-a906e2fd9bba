package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateMemberAttributeAdjustReq {

    @NotNull(message = "'program_id' must not be null")
    private Integer programId;

    @NotNull(message = "'is_multiple_attribute' must not be null")
    private EBoolean isMultipleAttribute;

    @NotBlank(message = "'name' must not be blank")
    @Size(max = 32, message = "name: length must be between 1 and 32")
    private String name;

    @Size(max = 255, message = "description: length must be between 1 and 255")
    private String description;

    private String memberAttribute;

    private String attributeValue;

    @Temporal(value = TemporalType.TIMESTAMP)
    private Date startDate;

    @Temporal(value = TemporalType.TIMESTAMP)
    private Date endDate;

    @Convert(converter = ECommonStatus.Converter.class)
    private ECommonStatus status;

    @Size(max = 255, message = "'Made Reason' cannot exceed 255 characters")
    private String madeReason;

    private boolean sendEmail;

    @AssertTrue(message = "'memberAttribute', 'attributeValue' and 'status' are required ")
    private boolean isSingleAttributeFieldsValid() {
        if (EBoolean.NO.equals(isMultipleAttribute)) {
            return memberAttribute != null && !memberAttribute.isBlank()
                    && attributeValue != null && !attributeValue.isBlank()
                    && status != null;
        }
        return true;
    }
}