package com.oneid.loyalty.accounting.ops.model.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberAttributeAdjustExport implements Serializable{

    private static final long serialVersionUID = 3620887706792855056L;

    private String loyaltyCusId;

    private String memberAttribute;

    private String attributeValue;

    private String startDate;

    private String endDate;

    private String status;

    private String processStatus;

    private String errorMessage;
}