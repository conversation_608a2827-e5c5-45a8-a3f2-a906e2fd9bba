package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberAttributeAdjustReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifyMemberAttributeAdjustReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberAttributeAdjustDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberAttributeAdjustRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.constant.ERequestProcessStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

public interface MemberAttributeAdjustService {

    ResourceDTO verifyExcel(VerifyMemberAttributeAdjustReq request, MultipartFile file) throws Exception;

    ResourceDTO create(CreateMemberAttributeAdjustReq req, MultipartFile file) throws Exception;

    List<ConditionAttributeDto> getAttributeByProgramId(Integer programId);

    Page<MemberAttributeAdjustRes> getInReviews(
            Integer programId,
            String name,
            EApprovalStatus approvalStatus,
            String createdBy,
            Date createdStart,
            Date createdEnd,
            String approvedBy,
            Date approvedStart,
            Date approvedEnd,
            Pageable pageable
    );

    void approveBatchRequest(ApprovalReq req);

    void cancelInReview(CancelReq req);

    MemberAttributeAdjustDetailRes getDetail(String code);

    Page<MemberAttributeAdjustDetailRes> getDetails(String code, ERequestProcessStatus processStatus, Pageable pageable);

    ResourceDTO exportFile(String code);

    Page<MemberAttributeAdjustRes> getListAvailable(Integer programId,
                                                    String name,
                                                    EBatchRequestProcessStatus status,
                                                    Date startDate,
                                                    Date endDate,
                                                    Integer offset,
                                                    Integer limit
    );

}