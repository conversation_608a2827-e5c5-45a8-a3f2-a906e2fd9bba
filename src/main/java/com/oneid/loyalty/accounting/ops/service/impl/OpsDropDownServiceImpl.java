package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.loyalty.accounting.ops.component.service.AttributeValueService;
import com.oneid.loyalty.accounting.ops.constant.EDir;
import com.oneid.loyalty.accounting.ops.constant.EOpsFunctionCode;
import com.oneid.loyalty.accounting.ops.constant.EStatus;
import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.feign.model.req.ActivatedVoucherPageFeignReq;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyCPMServiceFeignClient;
import com.oneid.loyalty.accounting.ops.model.req.MerchantDetailReq;
import com.oneid.loyalty.accounting.ops.model.res.CardTypeRes;
import com.oneid.loyalty.accounting.ops.model.res.DropDownStoreRes;
import com.oneid.loyalty.accounting.ops.model.res.DropdownCurrencyRes;
import com.oneid.loyalty.accounting.ops.model.res.DropdownRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTypeRes;
import com.oneid.loyalty.accounting.ops.model.res.LegacyTierRes;
import com.oneid.loyalty.accounting.ops.model.res.MerchantDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.PartnerSupplierRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramRes;
import com.oneid.loyalty.accounting.ops.model.res.ReasonCodeRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityGroupByParentRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.TierPolicyRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionCodeRes;
import com.oneid.loyalty.accounting.ops.service.OpsDropDownService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.AuditorAwareUtil;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterLevelType;
import com.oneid.oneloyalty.common.constant.ECounterRuleType;
import com.oneid.oneloyalty.common.constant.EMerchantServiceType;
import com.oneid.oneloyalty.common.constant.EMerchantType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ETransactionType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Campaign;
import com.oneid.oneloyalty.common.entity.CardPolicy;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.CurrencyRate;
import com.oneid.oneloyalty.common.entity.Function;
import com.oneid.oneloyalty.common.entity.MemberStatus;
import com.oneid.oneloyalty.common.entity.MerchantServiceType;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramAttributeServiceType;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.entity.ReasonCode;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.entity.TransactionCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.AttributeServiceCodeRepository;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CampaignRepository;
import com.oneid.oneloyalty.common.repository.CardPolicyRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.CurrencyRateRepository;
import com.oneid.oneloyalty.common.repository.CurrencyRepository;
import com.oneid.oneloyalty.common.repository.FunctionRepository;
import com.oneid.oneloyalty.common.repository.GiftCardBinRepository;
import com.oneid.oneloyalty.common.repository.GiftCardTypeRepository;
import com.oneid.oneloyalty.common.repository.MemberStatusRepository;
import com.oneid.oneloyalty.common.repository.MessageEventRepository;
import com.oneid.oneloyalty.common.repository.PartnerSupplierRepository;
import com.oneid.oneloyalty.common.repository.PosRepository;
import com.oneid.oneloyalty.common.repository.ProgramAttributeServiceTypeRepository;
import com.oneid.oneloyalty.common.repository.ProgramCorporationRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.ProgramTierPolicyRepository;
import com.oneid.oneloyalty.common.repository.ProgramTierRepository;
import com.oneid.oneloyalty.common.repository.ReasonCodeRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.repository.TransactionCodeRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CardTypeService;
import com.oneid.oneloyalty.common.service.ChainService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.CounterLevelObjectService;
import com.oneid.oneloyalty.common.service.CounterService;
import com.oneid.oneloyalty.common.service.PosService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTierService;
import com.oneid.oneloyalty.common.service.ProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.service.StoreService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OpsDropDownServiceImpl implements OpsDropDownService {

    private final int batch_size = 999; // max value = 999
    private final String VOUCHER_PURPOSE_GIFT = "GIFT";
    private final String VOUCHER_SALE_STATUS_ACTIVE = "ACTIVE";

    @Autowired
    BusinessRepository businessRepository;

    @Autowired
    CorporationRepository corporationRepository;

    @Autowired
    TransactionCodeRepository transactionCodeRepository;

    @Autowired
    ChainRepository chainRepository;

    @Autowired
    StoreRepository storeRepository;

    @Autowired
    PosRepository posRepository;

    @Autowired
    CardPolicyRepository cardPolicyRepository;

    @Autowired
    ProgramRepository programRepository;

    @Autowired
    GiftCardBinRepository gcBinRepository;

    @Autowired
    GiftCardTypeRepository gcTypeRepository;

    @Autowired
    ProgramCorporationRepository programCorporationRepository;

    @Autowired
    private CardTypeService cardTypeService;

    @Autowired
    CurrencyRepository currencyRepository;

    @Autowired
    CurrencyRateRepository currencyRateRepository;

    @Autowired
    private ProgramTierService programTierService;

    @Autowired
    private ReasonCodeRepository reasonCodeRepository;

    @Autowired
    private FunctionRepository functionRepository;

    @Autowired
    private ProgramTierPolicyRepository programTierPolicyRepository;

    @Autowired
    private MessageEventRepository messageEventRepository;

    @Autowired
    private PartnerSupplierRepository partnerSupplierRepository;

    @Autowired
    private ProgramTierRepository programTierRepository;

    @Autowired
    AuditorAware<OPSAuthenticatedPrincipal> auditorAware;

    @Autowired
    BusinessService businessService;

    @Autowired
    ProgramService programService;

    @Autowired
    private AuditorAwareUtil auditorAwareUtil;

    @Autowired
    private StoreService storeService;

    @Autowired
    private PosService posService;

    @Autowired
    private ChainService chainService;

    @Autowired
    private CorporationService corporationService;

    @Autowired
    private CampaignRepository campaignRepository;

    @Autowired
    private AttributeValueService attributeValueService;

    @Autowired
    private MemberStatusRepository memberStatusRepository;

    @Autowired
    private CounterService counterService;

    @Autowired
    private CounterLevelObjectService counterLevelObjectService;

    @Autowired
    private ProgramTransactionAttributeService programTransactionAttributeService;

    @Autowired
    private ProgramAttributeServiceTypeRepository programAttributeServiceTypeRepository;

    @Autowired
    private AttributeServiceCodeRepository attributeServiceCodeRepository;

    @Autowired
    private OneloyaltyCPMServiceFeignClient oneloyaltyCPMServiceFeignClient;

    @Override
    public List<DropdownRes> business(final ECommonStatus status) {
        return businessRepository.findAll().stream()
                .filter(e -> status == null || status == e.getStatus())
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> corporation(Integer businessId, final ECommonStatus status) {
        return corporationRepository.findByBusinessId(businessId).stream()
                .filter(e -> status == null || status == e.getStatus())
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> getActiveCorporationsByProgramId(Integer programId) {
        return corporationRepository.findActiveCorporationByProgramId(programId).stream().map(
                f -> {
                    Object[] l = (Object[]) f;
                    return new DropdownRes((int) l[0], (String) l[1], (String) l[2], (String) l[3]);
                }
        ).collect(Collectors.toList());
    }

    @Override
    public List<TransactionCodeRes> getActiveTransactionCodesByProgramId(Integer programId, String type, ECommonStatus status) {
        SpecificationBuilder<TransactionCode> searchBuilder = new SpecificationBuilder<>();
        searchBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));
        if (Objects.nonNull(type)) {
            searchBuilder.add(new SearchCriteria("transactionType", ETransactionType.of(type), SearchOperation.EQUAL));
        }
        if (Objects.nonNull(status)) {
            searchBuilder.add(new SearchCriteria("status", status, SearchOperation.EQUAL));
        }
        return transactionCodeRepository.findAll(searchBuilder).stream().sorted((f1, f2) -> Integer.compare(f2.getId(), f1.getId()))
                .map(f -> TransactionCodeRes.builder()
                        .id(f.getId())
                        .code(f.getCode())
                        .name(f.getName())
                        .type(f.getTransactionCodeType())
                        .status(f.getStatus())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> corporationByProgram(Integer programId, ECommonStatus status) {
        if (Objects.nonNull(status)) {
            return corporationRepository.findCorporationByProgramIdAndStatus(programId, status).stream().map(
                    f -> {
                        Object[] l = (Object[]) f;
                        return new DropdownRes((int) l[0], (String) l[1], (String) l[2], (String) l[3]);
                    }
            ).collect(Collectors.toList());
        }
        return corporationRepository.findCorporationByProgramId(programId).stream().map(
                f -> {
                    Object[] l = (Object[]) f;
                    return new DropdownRes((int) l[0], (String) l[1], (String) l[2], (String) l[3]);
                }
        ).collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> corporationByProgram(Integer programId) {
        return programCorporationRepository.findByProgramId(programId).stream().map(
                f -> {
                    Corporation corporation = corporationRepository.findById(f.getCorporationId()).orElse(null);
                    if (corporation == null)
                        return null;
                    return new DropdownRes(corporation.getId(), corporation.getCode(), corporation.getName());
                }
        ).collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> chain(Integer corporationId, ECommonStatus status) {
        return chainRepository.findByCorporationId(corporationId).stream()
                .filter(e -> status == null || status == e.getStatus())
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> store(Integer chainId, ECommonStatus status) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        SpecificationBuilder<Store> searchBuilder = new SpecificationBuilder<>();
        searchBuilder.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));
        if (Objects.nonNull(chainId)) {
            searchBuilder.add(new SearchCriteria("chainId", chainId, SearchOperation.EQUAL));
        }
        if (Objects.nonNull(status)) {
            searchBuilder.add(new SearchCriteria("status", status, SearchOperation.EQUAL));
        }
        return storeRepository.findAll(searchBuilder).stream()
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropDownStoreRes> getActiveStoresByChainAndProgram(Integer chainId, Integer programId, String codePrefix) {
        Pageable pageable = new OffsetBasedPageRequest(0, Integer.valueOf(PageConstant.DEFAULT_LIMIT), null);
        codePrefix = Strings.concat(codePrefix, "%");
        if (Objects.nonNull(chainId)) {
            Pageable noPaging = Pageable.unpaged();
            return storeRepository.findActiveByChainIdAndCodePrefix(chainId, codePrefix, noPaging)
                    .stream()
                    .map(this::mapToDropDownStoreRes).collect(Collectors.toList());
        }
        return storeRepository
                .findActiveByProgramIdAndCodePrefix(programId, codePrefix, pageable)
                .stream()
                .map(this::mapToDropDownStoreRes).collect(Collectors.toList());
    }

    private DropDownStoreRes mapToDropDownStoreRes(Object o) {
        Object[] l = (Object[]) o;
        return new DropDownStoreRes(
                new DropdownRes((int) l[0], (String) l[1], (String) l[2], (String) l[3]),
                new DropdownRes((int) l[4], (String) l[5], (String) l[6], (String) l[7]),
                new DropdownRes((int) l[8], (String) l[9], (String) l[10], (String) l[11])
        );
    }

    @Override
    public List<DropdownRes> terminal(Integer storeId, ECommonStatus status) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        SpecificationBuilder<Pos> searchBuilder = new SpecificationBuilder<>();
        searchBuilder.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));
        if (Objects.nonNull(storeId)) {
            searchBuilder.add(new SearchCriteria("storeId", storeId, SearchOperation.EQUAL));
        }
        if (Objects.nonNull(status)) {
            searchBuilder.add(new SearchCriteria("status", status, SearchOperation.EQUAL));
        }
        return posRepository.findAll(searchBuilder).stream()
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> cardPolicy(Integer businessId, ECardPolicyType cardPolicyType) {
        SpecificationBuilder<CardPolicy> builder = new SpecificationBuilder<>();
        builder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));
        builder.add(new SearchCriteria("policyType", cardPolicyType, SearchOperation.EQUAL));
        builder.add(new SearchCriteria("status", ECommonStatus.ACTIVE, SearchOperation.EQUAL));

        return cardPolicyRepository.findAll(builder).stream().map(
                f -> new DropdownRes(f.getId(), f.getCode(), f.getName())
        ).collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> program(Integer businessId) {
        return programRepository.findByBusinessId(businessId)
                .stream().map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> gcBinByBusinessAndProgram(Integer businessId, Integer programId) {
        if (businessId == null && programId == null) {
            return new LinkedList<>();
        }
        return gcBinRepository.findAllByBusinessIdAndProgramIdAndStatus(businessId, programId, ECommonStatus.ACTIVE)
                .stream().map(f -> new DropdownRes(f.getId(), f.getBinCode(), f.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<GiftCardTypeRes> gcTypeByBusinessAndProgram(Integer businessId, Integer programId) {
        if (businessId == null && programId == null) {
            return new LinkedList<>();
        }
        return gcTypeRepository.findAllByBusinessIdAndProgramIdAndStatus(businessId, programId, ECommonStatus.ACTIVE)
                .stream().map(f -> GiftCardTypeRes.of(f, null, null, null, null))
                .collect(Collectors.toList());
    }

    @Override
    public List<CardTypeRes> getCardTypesByBusinessAndProgramId(Integer businessId, Integer programId) {
        if (businessId == null || programId == null)
            return Collections.emptyList();

        return cardTypeService.findByBusinessIdAndProgramIdAndStatus(businessId, programId, ECommonStatus.ACTIVE)
                .stream()
                .map(cardType -> {
                    CardTypeRes cardTypeRes = new CardTypeRes();

                    cardTypeRes.setId(cardType.getId());
                    cardTypeRes.setName(cardType.getName());
                    cardTypeRes.setDescription(cardType.getDescription());
                    cardTypeRes.setStatus(cardType.getStatus().getValue());
                    cardTypeRes.setCardType(cardType.getCardType());
                    cardTypeRes.setCardPolicyId(cardType.getCardPolicyId());

                    return cardTypeRes;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> currencyForTransaction(Integer businessId) {
        List<CurrencyRate> currencyRatesOfBusiness = currencyRateRepository.findAllByBusinessId(businessId);
        return currencyRatesOfBusiness.stream().map(it -> currencyRepository.findById(it.getCurrencyId()).orElse(null))
                .filter(Objects::nonNull)
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownCurrencyRes> baseCurrencyForTransaction(Integer businessId) {
        List<CurrencyRate> currencyRatesOfBusiness = currencyRateRepository.findAllByBusinessId(businessId);
        final Map<Integer, Double> baseCurrencySellRateMap = currencyRatesOfBusiness.stream()
                .collect(Collectors.toMap(
                        CurrencyRate::getBaseCurrencyId,
                        CurrencyRate::getSellRate,
                        (o1, o2) -> o1
                ));
        return currencyRatesOfBusiness.stream().map(it -> currencyRepository.findById(it.getBaseCurrencyId()).orElse(null))
                .filter(Objects::nonNull)
                .distinct()
                .map(f -> DropdownCurrencyRes.builder()
                        .code(f.getCode())
                        .name(f.getName())
                        .id(f.getId())
                        .sellRate(baseCurrencySellRateMap.get(f.getId()))
                        .build()
                )
                .collect(Collectors.toList());
    }

    @Override
    public List<LegacyTierRes> getTiers(Integer programId) {
        if (programId == null)
            return Collections.emptyList();

        SpecificationBuilder<ProgramTier> specification = new SpecificationBuilder<ProgramTier>();

        specification.add(new SearchCriteria("status", ECommonStatus.ACTIVE, SearchOperation.EQUAL));
        specification.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        return programTierService.searchAll(specification)
                .stream()
                .map(LegacyTierRes::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public List<LegacyTierRes> getActivatedTiers(Integer programId, Sort sort) {
        Business business = businessService.findActiveByCode(auditorAware.getCurrentAuditor().get().getBusinessCode());
        programService.findByIdAndBusinessId(programId, business.getId());
        List<ProgramTier> tiers = programTierRepository.findByProgramIdAndStatus(programId, ECommonStatus.ACTIVE, sort);
        List<LegacyTierRes> res = tiers.stream()
                .map(LegacyTierRes::valueOf)
                .collect(Collectors.toList());

        return res;
    }

    @Override
    public List<ReasonCodeRes> getActiveReasonCodes(Integer businessId, Integer programId, EOpsFunctionCode functionCode) {
        functionCode = (functionCode != null ? functionCode : EOpsFunctionCode.POINT_ADJ);

        Function function = functionRepository.findByCode(functionCode.getValue());

        if (function == null)
            return Collections.emptyList();

        return reasonCodeRepository.findByBusinessIdAndProgramIdAndStatusFunctionId(businessId, programId, ECommonStatus.ACTIVE, function.getId())
                .stream()
                .map(entity -> ReasonCodeRes.builder()
                        .id(entity.getId())
                        .code(entity.getCode())
                        .programId(entity.getProgramId())
                        .businessId(entity.getBusinessId())
                        .functionCodeId(entity.getFunctionId())
                        .name(entity.getName())
                        .enName(entity.getEnName())
                        .description(entity.getDesciption())
                        .status(entity.getStatus())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<ReasonCodeRes> getAllReasonCodes(Integer programId, EOpsFunctionCode functionCode, ECommonStatus status) {
        functionCode = (functionCode != null ? functionCode : EOpsFunctionCode.POINT_ADJ);

        Function function = functionRepository.findByCode(functionCode.getValue());

        if (function == null)
            return Collections.emptyList();

        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        SpecificationBuilder<ReasonCode> searchBuilder = new SpecificationBuilder<>();
        searchBuilder.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));
        if (Objects.nonNull(programId)) {
            searchBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));
        }
        searchBuilder.add(new SearchCriteria("functionId", function.getId(), SearchOperation.EQUAL));
        if (Objects.nonNull(status)) {
            searchBuilder.add(new SearchCriteria("status", status, SearchOperation.EQUAL));
        }

        return reasonCodeRepository.findAll(searchBuilder)
                .stream()
                .map(entity -> ReasonCodeRes.builder()
                        .id(entity.getId())
                        .code(entity.getCode())
                        .programId(entity.getProgramId())
                        .businessId(entity.getBusinessId())
                        .functionCodeId(entity.getFunctionId())
                        .name(entity.getName())
                        .enName(entity.getEnName())
                        .description(entity.getDesciption())
                        .status(entity.getStatus())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public Collection<ShortEntityGroupByParentRes> searchTerminals(List<Integer> storeIds, String nameOrCode) {
        List<Pos> posList = new LinkedList<>();

        List<Integer> storeIdsTemple = new ArrayList<>(batch_size);

        for (int i = 0; i < storeIds.size(); ++i) {
            storeIdsTemple.add(storeIds.get(i));
            if (i % batch_size == 0 || i == storeIds.size() - 1) {
                posList.addAll(
                        posRepository.findByStoreIdInAndNameOrCode(
                                storeIdsTemple, nameOrCode + "%", ECommonStatus.ACTIVE)
                );
                storeIdsTemple.clear();
            }
        }

        Map<Integer, ShortEntityGroupByParentRes> resMap = new HashMap<>();
        for (Pos pos : posList) {
            if (!resMap.containsKey(pos.getStoreId())) {
                resMap.put(pos.getStoreId(),
                        ShortEntityGroupByParentRes
                                .builder()
                                .id(pos.getStoreId())
                                .children(new LinkedList<>())
                                .build());
            }
            resMap.get(pos.getStoreId()).getChildren()
                    .add(new ShortEntityRes(pos.getId(), pos.getName(), pos.getCode()));
        }
        return resMap.values();
    }

    @Override
    public Collection<ShortEntityGroupByParentRes> searchStores(List<Integer> chainIds, String nameOrCode) {
        List<Store> storeList = new LinkedList<>();

        List<Integer> chainIdsTemple = new ArrayList<>(batch_size);

        for (int i = 0; i < chainIds.size(); ++i) {
            chainIdsTemple.add(chainIds.get(i));
            if (i % batch_size == 0 || i == chainIds.size() - 1) {
                storeList.addAll(
                        storeRepository.findByChainIdInAndNameOrCode(
                                chainIdsTemple, nameOrCode + "%", ECommonStatus.ACTIVE
                        )
                );
                chainIdsTemple.clear();
            }
        }

        Map<Integer, ShortEntityGroupByParentRes> resMap = new HashMap<>();
        for (Store store : storeList) {
            if (!resMap.containsKey(store.getChainId())) {
                resMap.put(store.getChainId(),
                        ShortEntityGroupByParentRes
                                .builder()
                                .id(store.getChainId())
                                .children(new LinkedList<>())
                                .build());
            }
            resMap.get(store.getChainId()).getChildren()
                    .add(new ShortEntityRes(store.getId(), store.getName(), store.getCode()));
        }
        return resMap.values();
    }

    @Override
    public Collection<ShortEntityGroupByParentRes> searchChains(List<Integer> corporationIds, String nameOrCode) {
        List<Chain> chainList = chainRepository.findByCorporationIdInAndNameOrCode(corporationIds, nameOrCode + "%", ECommonStatus.ACTIVE);
        Map<Integer, ShortEntityGroupByParentRes> resMap = new HashMap<>();
        for (Chain chain : chainList) {
            if (!resMap.containsKey(chain.getCorporationId())) {
                resMap.put(chain.getCorporationId(),
                        ShortEntityGroupByParentRes
                                .builder()
                                .id(chain.getCorporationId())
                                .children(new LinkedList<>())
                                .build());
            }
            resMap.get(chain.getCorporationId()).getChildren()
                    .add(new ShortEntityRes(chain.getId(), chain.getName(), chain.getCode()));
        }
        return resMap.values();
    }

    @Override
    public List<ProgramRes> getActivePrograms(Integer businessId, List<Integer> excludeProgramIds) {
        List<Integer> excludeIds = new ArrayList<Integer>();

        if (CollectionUtils.isNotEmpty(excludeProgramIds)) {
            excludeIds.addAll(excludeProgramIds);
        }

        return programRepository.findByBusinessId(businessId)
                .stream()
                .filter(entity -> entity.getStatus().equals(ECommonStatus.ACTIVE) && !excludeIds.contains(entity.getId()))
                .map(entity -> {
                    ProgramRes res = new ProgramRes();
                    res.setProgramCode(entity.getCode());
                    res.setProgramId(entity.getId());
                    res.setProgramName(entity.getName());
                    res.setStatus(entity.getStatus());

                    return res;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<TierPolicyRes> getActiveTierPolicies(Integer businessId, Integer programId) {
        return programTierPolicyRepository.findActiveByProgramId(programId)
                .stream()
                .map(entity -> TierPolicyRes.builder()
                        .requestId(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .collect(Collectors.toList());
    }

    public List<DropdownRes> getMessageEvent() {
        return messageEventRepository.findByStatus(ECommonStatus.ACTIVE).stream()
                .map(event -> new DropdownRes(event.getId(), event.getCode(), event.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> getActivatedCurrenciesByBusinessCode(String businessCode) {
        if (Objects.isNull(businessCode)) {
            businessCode = auditorAwareUtil.getBusinessCode();
        }
        Business business = businessRepository.findByCode(businessCode);

        if (business == null)
            throw new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null);

        return currencyRepository.findByBusinessId(business.getId())
                .stream()
                .filter(each -> each.getStatus().equals(ECommonStatus.ACTIVE))
                .map(entity -> new DropdownRes(entity.getId(), entity.getCode(), entity.getName(), entity.getEnName()))
                .collect(Collectors.toList());
    }

    @Override
    public Page<AttributeCombobox> getSaleIntegration(String keyword, Pageable pageable) {
        return attributeValueService.getVoucherCodeAttributeValue(null, keyword, pageable);
    }

    @Override
    public List<AttributeCombobox> getActivatedVouchers(Integer programId, String voucherStatus, String keyword, Integer page, Integer limit) {
        return  attributeValueService.getListActivatedVoucher(programId, voucherStatus, keyword, page, limit);
    }

    @Override
    public List<PartnerSupplierRes> getActivatedPartnerSuppliers(Integer businessId) {
        return partnerSupplierRepository.findByBusinessIdAndStatus(businessId, ECommonStatus.ACTIVE)
                .stream()
                .map(entity -> PartnerSupplierRes.builder()
                        .id(entity.getId())
                        .code(entity.getCode())
                        .name(entity.getName())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> geActivatedtStoresByCorporationId(Integer corporationId) {
        return storeRepository.findByCorporationIdAndStatus(corporationId, ECommonStatus.ACTIVE)
                .stream()
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> getActivatedChainsByCorporationCode(String businessCode, String corporationCode, ECommonStatus status) {
        return corporationRepository.findByBusinessAndCorporationCode(businessCode, corporationCode)
                .map(corporation -> {
                    return chainRepository.findByCorporationId(corporation.getId())
                            .stream()
                            .filter(e -> status == null || status == e.getStatus())
                            .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName(), f.getEnName()))
                            .collect(Collectors.toList());
                })
                .orElse(Collections.emptyList());
    }

    @Override
    public List<DropdownRes> getActivatedChainsByCorporationId(Integer corporationId, ECommonStatus status) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        SpecificationBuilder<Chain> searchBuilder = new SpecificationBuilder<>();
        searchBuilder.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));
        if (Objects.nonNull(corporationId)) {
            searchBuilder.add(new SearchCriteria("corporationId", corporationId, SearchOperation.EQUAL));
        }
        if (Objects.nonNull(status)) {
            searchBuilder.add(new SearchCriteria("status", status, SearchOperation.EQUAL));
        }
        return chainRepository.findAll(searchBuilder)
                .stream()
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName(), f.getEnName()))
                .collect(Collectors.toList());
    }

    @Override
    public MerchantDetailRes getMerchantDetail(MerchantDetailReq req) {
        MerchantDetailRes result = new MerchantDetailRes();
        Business business;
        if (Objects.nonNull(req.getBusinessCode())) {
            business = businessService.findActiveByCode(req.getBusinessCode());
        } else {
            business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        }

        List<MerchantDetailRes.MerchantRes> merchants = Optional.of(req.getMerchants()).orElseGet(ArrayList::new).stream().map(ele -> {
            MerchantDetailRes.MerchantRes merchantRes = new MerchantDetailRes.MerchantRes();
            if (Objects.nonNull(ele.getCorporationCode())) {
                Corporation corporation = corporationService.find(business.getId(), ele.getCorporationCode());
                if (Objects.isNull(corporation)) {
                    throw new BusinessException(ErrorCode.CORPORATION_NOT_FOUND);
                }
                merchantRes.setCorporation(new ShortEntityRes(corporation.getId(), corporation.getName(), corporation.getCode()));
                if (Objects.nonNull(ele.getPosCode())) {

                    Pos pos = posRepository.findByCorporationIdAndCode(corporation.getId(), ele.getPosCode());
                    if (Objects.isNull(pos)) {
                        throw new BusinessException(ErrorCode.POS_NOT_FOUND);
                    }
                    merchantRes.setPos(new ShortEntityRes(pos.getId(), pos.getName(), pos.getCode()));
                    Store store = storeService.find(pos.getStoreId());
                    if (Objects.isNull(store)) {
                        throw new BusinessException(ErrorCode.STORE_NOT_FOUND);
                    }
                    merchantRes.setStore(new ShortEntityRes(store.getId(), store.getName(), store.getCode()));
                    Chain chain = chainService.find(pos.getChainId()).orElseThrow(() -> new BusinessException(ErrorCode.CHAIN_NOT_FOUND));
                    merchantRes.setChain(new ShortEntityRes(chain.getId(), chain.getName(), chain.getCode()));
                }
                if (Objects.nonNull(ele.getStoreCode())) {
                    Store store = storeRepository.findByCorporationIdAndCode(corporation.getId(), ele.getStoreCode());
                    if (Objects.isNull(store)) {
                        throw new BusinessException(ErrorCode.STORE_NOT_FOUND);
                    }
                    merchantRes.setStore(new ShortEntityRes(store.getId(), store.getName(), store.getCode()));
                }
                if (Objects.nonNull(ele.getChainCode())) {
                    Chain chain = chainService.find(business.getId(), ele.getChainCode());
                    if (Objects.isNull(chain)) {
                        throw new BusinessException(ErrorCode.CHAIN_NOT_FOUND);
                    }
                    merchantRes.setChain(new ShortEntityRes(chain.getId(), chain.getName(), chain.getCode()));
                }
            }
            return merchantRes;
        }).collect(Collectors.toList());
        result.setMerchants(merchants);
        if (Objects.nonNull(req.getProgramCode())) {
            Program program = programService.find(business.getId(), req.getProgramCode()).orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND));
            result.setProgram(new ShortEntityRes(program.getId(), program.getName(), program.getCode()));
        }
        return result;
    }

    @Override
    public List<DropdownRes> getAllCampaign(Integer programId, ECommonStatus status) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        SpecificationBuilder<Campaign> searchBuilder = new SpecificationBuilder<>();
        searchBuilder.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));
        if (Objects.nonNull(programId)) {
            searchBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));
        }
        if (Objects.nonNull(status)) {
            searchBuilder.add(new SearchCriteria("status", status, SearchOperation.EQUAL));
        }
        return campaignRepository.findAll(searchBuilder).stream()
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> getAllMemberStatus(Integer programId, ECommonStatus status) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        SpecificationBuilder<MemberStatus> searchBuilder = new SpecificationBuilder<>();
        searchBuilder.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));

        if (Objects.nonNull(programId)) {
            searchBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));
        }

        if (Objects.nonNull(status)) {
            searchBuilder.add(new SearchCriteria("status", status, SearchOperation.EQUAL));
        }

        return memberStatusRepository.findAll(searchBuilder, Sort.by(Sort.Direction.ASC, "viName")).stream()
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getViName(), f.getEnName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> getPartner(EMerchantServiceType merchantServiceType, EMerchantType merchantType, ECommonStatus status) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());

        return corporationService.filter(business.getId(), status, merchantType, merchantServiceType)
                .stream()
                .filter(objects -> Objects.nonNull(objects[0]) && Objects.nonNull(objects[1]))
                .map(objects -> {
                    Corporation corporation = (Corporation) objects[0];
                    MerchantServiceType serviceType = (MerchantServiceType) objects[1];
                    DropdownRes ahihi = new DropdownRes(corporation.getId(), corporation.getCode(), corporation.getName(), corporation.getEnName(), corporation.getLogoUrl(), serviceType.getStartDate().toString(), serviceType.getEndDate().toString());
                    return new DropdownRes(corporation.getId(), corporation.getCode(), corporation.getName(), corporation.getEnName(), corporation.getLogoUrl(), serviceType.getStartDate().toString(), serviceType.getEndDate().toString());
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> getCounters(Integer programId, EServiceType serviceType, ECommonStatus status, ECounterRuleType counterRuleType) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());

        return counterService.findAvailableByBusinessIdAndProgramIdAndServiceType(business.getId(), programId, serviceType, null, status, counterRuleType)
                .stream()
                .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getName(), f.getLevel(), f.getPeriod()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> getCounterLevels(Integer programId, ECounterLevelType counterLevelType, ECommonStatus status) {
        if (ECounterLevelType.OBJECT.equals(counterLevelType)) {
            return counterLevelObjectService.findByProgramIdAndStatusOrderByCodeAsc(programId, status)
                    .stream()
                    .map(f -> new DropdownRes(f.getId(), f.getCode(), f.getValue()))
                    .collect(Collectors.toList());
        } else if (ECounterLevelType.ATTRIBUTE.equals(counterLevelType)) {
            List<ProgramAttributeServiceType> listProgramAttributeServiceTypes = programAttributeServiceTypeRepository
                    .findByProgramIdAndServiceTypeAndEnableCounterLevel(programId, EServiceType.COUNTER, EBoolean.YES);

            Map<String, List<String>> map = listProgramAttributeServiceTypes.stream()
                    .collect(Collectors.toMap(ProgramAttributeServiceType::getAttribute, e -> Objects.nonNull(e.getOperators()) ? e.getOperators() : new ArrayList<>(), (k1, k2) -> k2));

            return programTransactionAttributeService.findByProgramOrderByAttributeAsc(programId)
                    .stream()
                    .filter(attr -> Objects.nonNull(map.get(attr.getAttribute())))
                    .map(f -> new DropdownRes(f.getId(), f.getAttribute(), f.getName()))
                    .collect(Collectors.toList());
        } else {
            return null;
        }
    }

    @Override
    public List<DropdownRes> getServiceCodeConfig(String program, String mappingProgram) {

        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());

        Program entity = programRepository.findByBusinessIdAndCode(business.getId(), mappingProgram).orElseThrow(null);

        List<DropdownRes> result = attributeServiceCodeRepository.findByProgramIdAndStatus(entity.getId(), ECommonStatus.ACTIVE)
                .stream()
                .map(f -> new DropdownRes(null, f.getCode(), f.getValue()))
                .collect(Collectors.toList());

        List<DropdownRes> result1 = oneloyaltyCPMServiceFeignClient.getServiceCodeConfigList(program, mappingProgram).getData()
                .stream()
                .map(f -> new DropdownRes(null, f.getServiceCode(), f.getServiceCode()))
                .collect(Collectors.toList());

        result.addAll(result1.stream()
                .filter(entity1 -> result.stream()
                        .noneMatch(entity0 -> entity0.getCode().equals(entity1.getCode())))
                .collect(Collectors.toList()));

        return result;
    }
}