package com.oneid.loyalty.accounting.ops.model.dto;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;
import lombok.Data;

@Data
public class MemberAttributeAdjustExcelDTO {
    @ExcelRow
    private int rowIndex;

    @ExcelCellName("LOYALTY_ID")
    private String loyaltyId;

    private String responseStatus = OPSConstant.VALID;

    private String errorMessage;
}