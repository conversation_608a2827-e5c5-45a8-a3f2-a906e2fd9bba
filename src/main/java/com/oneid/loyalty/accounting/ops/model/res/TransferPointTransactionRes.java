package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.ETransferPointFrom;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EPayWithPointStatus;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import com.oneid.oneloyalty.common.entity.*;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@JsonInclude(value = Include.NON_NULL)
@EqualsAndHashCode
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TransferPointTransactionRes {

    private String invoiceNo;

    private String txnRefNo;

    private Long memberId;

    private String memberCode;

    private String accountCode;

    private EOpsIdType accountType;

    private DropdownRes scheme;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date transactionTime;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date expiredAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date redeemAt;

    private BigDecimal redeemPoint;

    private BigDecimal awardPoint;

    private PoolShortRes poolShort;

    private String status;

    private String coreErrorMessage;

    private ShortEntityRes program;

    private String createdBy;

    private Date updatedAt;

    private String updatedBy;

    private Date approvedAt;

    private String approvedBy;

    private ShortEntityRes pool;

    private List<PointTransferDetail> pointTransferDetails;

    private ETransferPointFrom from;

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class PointTransferDetail {
        private ETransferPointFrom from;

        private String txnRefNo;

        private String memberCode;

        private Long memberId;

        private BigDecimal point;

        private BigDecimal balanceBefore;

        private BigDecimal balanceAfter;

        private ShortEntityRes pool;

        private Date transactionTime;

        private ETransactionStatus status;

        private String coreErrorMessage;
    }



}