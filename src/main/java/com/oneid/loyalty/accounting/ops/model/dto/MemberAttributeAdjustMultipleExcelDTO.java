package com.oneid.loyalty.accounting.ops.model.dto;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;
import lombok.Data;

@Data
public class MemberAttributeAdjustMultipleExcelDTO {
    @ExcelRow
    private int rowIndex;

    @ExcelCellName("LOYALTY_ID")
    private String loyaltyId;

    @ExcelCellName("MEMBER_ATTRIBUTE")
    private String memberAttribute;

    @ExcelCellName("ATTRIBUTE_VALUE")
    private String attributeValue;

    @ExcelCellName("START_DATE")
    private String startDate;

    @ExcelCellName("END_DATE")
    private String endDate;

    @ExcelCellName("STATUS")
    private String status;

    private String responseStatus = OPSConstant.VALID;

    private String errorMessage;
}