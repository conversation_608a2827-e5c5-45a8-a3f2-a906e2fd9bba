package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.ETransactionTemplateType;
import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.SMSAnnotationRes;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionBatchRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionMemberRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.RevertTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchAvailableTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionConfirmReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSapSaleOrderCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSapSaleOrderUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSearchReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifyTransactionBatchRequestReq;
import com.oneid.loyalty.accounting.ops.model.res.TransactionBatchRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionSearchRes;
import com.oneid.loyalty.accounting.ops.service.OpsTransactionService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.RequestPojo;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECharacterSet;
import com.oneid.oneloyalty.common.constant.ERequestProcessStatus;
import com.oneid.oneloyalty.common.constant.ESmsAnnotation;
import com.oneid.oneloyalty.common.constant.ETransactionBatchType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("v1/transactions")
@Validated
public class TransactionsController extends BaseController {
    @Autowired
    OpsTransactionService opsTransactionService;

    @Autowired
    OpsCommonExcelService opsCommonExcelService;

    @PostMapping("/list")
    @Authorize(role = AccessRole.TRANSACTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getTransactions(
            @Valid @RequestParam(value = "offset", defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", defaultValue = "10") @Min(1) @Max(200) Integer limit,
            @Valid @RequestBody TransactionSearchReq transactionSearchReq
    ) {
        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit, null);
        Page<TransactionRes> page = opsTransactionService.search(transactionSearchReq, pageRequest);
        return success(page, offset, limit);
    }

    @GetMapping("/available")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getListTransaction(
            @RequestParam(name = "program_id") @NotNull(message = "'program_id' must not be null'") Integer programId,
            @RequestParam(name = "batch_no", required = false) Long batchNo,
            @RequestParam(name = "batch_name", required = false) String batchName,
            @RequestParam(name = "type", required = false) EBatchRequestType batchRequestType,
            @RequestParam(name = "transaction_batch_type", required = false) ETransactionBatchType transactionBatchType,
            @RequestParam(name = "enable_sms", required = false) EBoolean enableSMS,
            @RequestParam(name = "failed_records", required = false) EBoolean failedRecords,
            @RequestParam(name = "created_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date createdStart,
            @RequestParam(name = "created_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date createdEnd,
            @RequestParam(name = "process_status", required = false) EBatchRequestProcessStatus status,
            @RequestParam(name = "approved_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date approvedStart,
            @RequestParam(name = "approved_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date approvedEnd,
            @RequestParam(name = "created_by", required = false) String createdBy,
            @RequestParam(name = "approved_by", required = false) String approvedBy,
            @MakerCheckerOffsetPageable Pageable pageable
    ) {
        SearchAvailableTransactionReq req = SearchAvailableTransactionReq.builder()
                .programId(programId)
                .batchNo(batchNo)
                .batchName(batchName)
                .batchRequestType(batchRequestType)
                .transactionBatchType(transactionBatchType)
                .enableSMS(enableSMS)
                .failedRecords(failedRecords)
                .createdStart(createdStart)
                .createdEnd(createdEnd)
                .status(status)
                .createdBy(createdBy)
                .approvedEnd(approvedEnd)
                .approvedStart(approvedStart)
                .approvedBy(approvedBy)
                .build();

        Page<TransactionSearchRes> page = opsTransactionService.searchAvailable(req, pageable);

        return success(page, (int) pageable.getOffset(), pageable.getPageSize());
    }

    @GetMapping("/in-review/{batch_no}/export")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.EXPORT})
    public ResponseEntity<?> exportFileInReview(@PathVariable(name = "batch_no") Long batchNo) {
        ResourceDTO dto = opsTransactionService.exportFileInReview(batchNo);
        return new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(dto.getFilename()), HttpStatus.OK);
    }

    @GetMapping("/available/{batch_no}/export")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.EXPORT})
    public ResponseEntity<?> exportTransactionDetails(@PathVariable(name = "batch_no") Long batchNo) {
        ResourceDTO dto = opsTransactionService.exportFileAvailable(batchNo);
        return new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(dto.getFilename()), HttpStatus.OK);
    }

    @Authorize(role = AccessRole.TRANSACTION, permissions = {AccessPermission.VIEW})
    @GetMapping(value = "{transaction_id}")
    public ResponseEntity<?> getTransactionDetails(@PathVariable(name = "transaction_id") String pointTnxId) {
        return success(opsTransactionService.getTransactionDetails(pointTnxId));
    }

    @PostMapping
    @Authorize(role = AccessRole.TRANSACTION, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> createTransaction(@RequestBody @Valid CreateTransactionReq createTransactionReq) {
        return new ResponseEntity<APIResponse<?>>(opsTransactionService.createTransaction(createTransactionReq), HttpStatus.OK);
    }

    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.VIEW})
    @GetMapping(value = "batch/{id}")
    public ResponseEntity<?> getBatchDetail(@PathVariable("id") Long batchId) {
        return success(opsTransactionService.getTransactionBatchDetails(batchId));
    }

    @PostMapping("/batch/verify")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> verifyTransactionByBatch(
            @RequestPojo(name = "content", errorCode = ErrorCode.PAYLOAD_MUST_NOT_BE_NULL) @Valid VerifyTransactionBatchRequestReq request,
            @RequestPart(name = "file") MultipartFile multipartFile
    ) throws Exception {
        CreateTransactionBatchRequestReq req = CreateTransactionBatchRequestReq.builder()
                .businessId(null)
                .programId(request.getProgramId())
                .transactionBatchType(request.getTransactionBatchType())
                .genInvoiceNoMethod(request.getGenInvoiceNoMethod())
                .genTransactionTimeMethod(request.getGenTransactionTimeMethod())
                .build();
        ResourceDTO dto = opsTransactionService.verifyTransactionByBatch(req, multipartFile);
        return dto != null ? new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(multipartFile.getOriginalFilename()), HttpStatus.BAD_REQUEST)
                : success(null);
    }

    @PostMapping("/batch")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> createTransactionByBatch(
            @RequestPojo(name = "content", errorCode = ErrorCode.PAYLOAD_MUST_NOT_BE_NULL) @Valid CreateTransactionBatchRequestReq req,
            @RequestPart(name = "file") MultipartFile multipartFile
    ) throws Exception {
        ResourceDTO dto = opsTransactionService.createTransactionByBatch(req, multipartFile);
        return Objects.nonNull(dto.getResource()) ? new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(multipartFile.getOriginalFilename()), HttpStatus.BAD_REQUEST)
                : success(Integer.parseInt(dto.getFilename()));
    }

    @PostMapping(value = "reverse")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.REFUNDING_TRANSACTION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.REFUNDING_CRATE_NEW_TRANSACTION, permissions = {AccessPermission.EDIT})
    }, any = true)
    public ResponseEntity<?> revertTransaction(@RequestBody @Valid RevertTransactionReq revertTransactionReq) {
        return new ResponseEntity<APIResponse<?>>(opsTransactionService.revertTransaction(revertTransactionReq), HttpStatus.OK);
    }

    @GetMapping(value = "{transaction_id}/checklog")
    public ResponseEntity<?> getCheckLog(@PathVariable("transaction_id") String pointTnxId) {
        return success(opsTransactionService.checkLog(pointTnxId));
    }

    @PostMapping(value = "confirm")
    @Authorize(role = AccessRole.TRANSACTION, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> confirmTransaction(@RequestBody @Valid TransactionConfirmReq createTransactionReq) {
        return new ResponseEntity<APIResponse<?>>(opsTransactionService.confirmTransaction(createTransactionReq), HttpStatus.OK);
    }

    @GetMapping("/template")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> getTemplateBathRequest(@RequestParam(name = "type") ETransactionTemplateType type) throws IOException {
        return opsCommonExcelService.getTemplate(OPSConstant.TXN, type.getFileName());
    }

    @GetMapping("/member")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> findMemberDetail(
            @RequestParam("program_id") Integer programId,
            @RequestParam("id_type") EOpsIdType idType,
            @RequestParam("id_no") String idNo
    ) {
        return success(opsTransactionService.findMemberTransaction(programId, idType, idNo));
    }

    @PostMapping(value = "/requests/member")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> createMemberTransactionRequest(@RequestBody @Valid CreateTransactionMemberRequestReq req) {
        return success(opsTransactionService.createTransactionBatchRequestForMember(req));
    }

    @GetMapping(value = "/requests/sms-annotations")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> getAnnotations() {
        return success(
                ESmsAnnotation.getAll()
                        .stream()
                        .map(smsAnnotation -> SMSAnnotationRes
                                .builder()
                                .annoCode(smsAnnotation.getValue())
                                .annoDes(smsAnnotation.getDisplayName())
                                .build())
                        .collect(Collectors.toList())
        );
    }

    @GetMapping(value = "/requests/in-review/{id}")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewTransactionBatchRequestDetail(@PathVariable Long id) {
        return success(opsTransactionService.getTransactionBatchRequestDetail(id));
    }

    @GetMapping(value = "/requests/in-review")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewTransactionRequests(
            @RequestParam(value = "batch_request_id", required = true) Long batchRequestId,
            @RequestParam(value = "process_status", required = false) ERequestProcessStatus processStatus,
            @RequestParam(value = "offset", defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = "limit", defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit) {

        Page<TransactionRequestRes> page = opsTransactionService
                .getInReviewTransactionRequests(batchRequestId, processStatus, new OffsetBasedPageRequest(offset, limit, Sort.by("id").ascending()));

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping(value = "/requests/available")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableTransactionRequests(
            @RequestParam(value = "batch_request_id") Long batchRequestId,
            @RequestParam(value = "process_status", required = false) ERequestProcessStatus processStatus,
            @MakerCheckerOffsetPageable Pageable pageable) {
        Page<TransactionRequestRes> page = opsTransactionService.getAvailableTransactionRequests(batchRequestId, processStatus,
                new OffsetBasedPageRequest((int) pageable.getOffset(), pageable.getPageSize()));
        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }

    @GetMapping(value = "random-invoice-no")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> randomInvoiceNo(@RequestParam(value = "pattern") String pattern,
                                             @RequestParam(value = "type") ECharacterSet type) {
        return success(opsTransactionService.randomInvoiceNumber(pattern, type));
    }

    @GetMapping(value = "in-review")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReview(
            @RequestParam(value = "program_id") Integer programId,
            @RequestParam(value = "batch_no", required = false) Long batchNo,
            @RequestParam(value = "batch_name", required = false) String batchName,
            @RequestParam(value = "type", required = false) EBatchRequestType type,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "created_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date createdStart,
            @RequestParam(value = "created_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date createdEnd,
            @RequestParam(value = "approved_by", required = false) String approvedBy,
            @RequestParam(value = "approved_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date approvedStart,
            @RequestParam(value = "approved_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date approvedEnd,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @MakerCheckerOffsetPageable Pageable pageable) {
        Page<TransactionBatchRequestRes> page = opsTransactionService.filterTransaction(programId, batchNo, batchName, type, approvalStatus, createdBy, createdStart, createdEnd, approvedBy, approvedStart, approvedEnd, pageable);

        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }

    @GetMapping("/available/{id}/statistic")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getTransactionStatisticById(@PathVariable("id") Long requestId) {
        return success(opsTransactionService.getTransactionStatisticById(requestId));
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.APPROVE_OR_REJECT})
    })
    public ResponseEntity<?> approveBatchRequest(@Valid @RequestBody ApprovalReq req) {
        return success(opsTransactionService.approveBatchRequest(req));
    }

    @PostMapping("/requests/in-review/{id}/cancel")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> cancelRequest(@PathVariable("id") Long id,
                                           @RequestBody CancelReq req) {
        req.setId(id);
        opsTransactionService.cancelInReview(req);
        return success(null);
    }

    @PostMapping("/requests/retry/{id}")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> retryBatchRequest(@PathVariable Long id) {
        return success(opsTransactionService.retryBatchRequest(id));
    }

    @PostMapping("/requests/sap-sale-orders")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> createSapSaleOrder(@Valid @RequestBody TransactionSapSaleOrderCreateReq req) {
        return success(opsTransactionService.createSapSaleOrder(req));
    }

    @PutMapping("/requests/{batchRequestId}/sap-sale-orders")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> updateSapSaleOrder(@PathVariable Long batchRequestId, @Valid @RequestBody TransactionSapSaleOrderUpdateReq req) {
        return success(opsTransactionService.updateSapSaleOrder(batchRequestId, req));
    }

    @GetMapping("/requests/sap-sale-orders/customer/{sapCustomer}")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> getSapSaleOrders(@PathVariable String sapCustomer) {
        return success(opsTransactionService.getSapSaleOrders(sapCustomer));
    }

    @GetMapping("/requests/sap-sale-orders/{sapSaleOrder}")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> getSapSaleOrder(@PathVariable String sapSaleOrder) {
        return success(opsTransactionService.getSapSaleOrder(sapSaleOrder));
    }

    @GetMapping("/requests/{batchRequestId}/sap-sale-orders")
    @Authorize(role = AccessRole.TRANSACTION_REQUEST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> getSapSaleOrder(@PathVariable Long batchRequestId) {
        return success(opsTransactionService.getSapSaleOrder(batchRequestId));
    }

    @GetMapping("/details/{txnRefNo}")
    @Authorize(role = AccessRole.TRANSACTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getTransactionInfoDetail(@PathVariable String txnRefNo) {
        return success(opsTransactionService.getTransactionInfoDetail(txnRefNo));
    }

    @GetMapping("/details/{txnRefNo}/refund-txn-history")
    @Authorize(role = AccessRole.TRANSACTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getRefundTransactionHistory(@PathVariable String txnRefNo) {
        return success(opsTransactionService.getRefundTransactionHistory(txnRefNo));
    }
}