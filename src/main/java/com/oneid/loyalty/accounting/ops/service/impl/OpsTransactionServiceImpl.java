package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.config.CheckLogSourceConfigPram;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.constant.EOpsCancellationType;
import com.oneid.loyalty.accounting.ops.constant.EOpsFunctionCode;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.EOpsTransactionType;
import com.oneid.loyalty.accounting.ops.constant.ETransferPointFrom;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MasterWorkerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyElasticFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.SapFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ConfirmTransactionFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CreateTransactionFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CustomerFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalSendEmailReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MasterWorkerTransactionRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ReverseTransactionFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ReverseTransactionInfoFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.SAPSaleOrderCreateFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.SAPSaleOrderUpdateFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.TransactionInfoFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.IsSyncElasticlFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.SAPSaleOrderCreateFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.SAPSaleOrderGetFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.SAPSaleOrderUpdateFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.TransactionPointWrapper;
import com.oneid.loyalty.accounting.ops.model.dto.ActionTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.AdjustTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.EarnBurnSaleTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.dto.RevertFullTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.RevertPartialTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.RevertPointTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionBatchRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionMemberRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.RevertTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchAvailableTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionConfirmReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSapSaleOrderCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSapSaleOrderUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSearchReq;
import com.oneid.loyalty.accounting.ops.model.res.DropdownRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberTransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.PWPTransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.PointAwardDetail;
import com.oneid.loyalty.accounting.ops.model.res.PointRedeemDetail;
import com.oneid.loyalty.accounting.ops.model.res.PoolShortRes;
import com.oneid.loyalty.accounting.ops.model.res.RefundTransactionHistoryRes;
import com.oneid.loyalty.accounting.ops.model.res.SapSaleOrderDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.SapSaleOrderRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionBatchRequestDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionBatchRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionCheckLogRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionExportEntry;
import com.oneid.loyalty.accounting.ops.model.res.TransactionRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionSearchRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionStatisticRes;
import com.oneid.loyalty.accounting.ops.model.res.TransferPointTransactionRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsChainService;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.loyalty.accounting.ops.service.OpsProgramService;
import com.oneid.loyalty.accounting.ops.service.OpsStoreService;
import com.oneid.loyalty.accounting.ops.service.OpsTerminalService;
import com.oneid.loyalty.accounting.ops.service.OpsTransactionService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.Assert;
import com.oneid.loyalty.accounting.ops.util.AuditorAwareUtil;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.loyalty.accounting.ops.util.excel.entry.EntryContext;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EAdjustmentType;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECharacterSet;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EGenerationInvoiceNoMethod;
import com.oneid.oneloyalty.common.constant.EGenerationTransactionTimeMethod;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.EIdentifyType;
import com.oneid.oneloyalty.common.constant.EPayWithPointStatus;
import com.oneid.oneloyalty.common.constant.EPoolType;
import com.oneid.oneloyalty.common.constant.ERequestProcessStatus;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.constant.ESyncWithElastic;
import com.oneid.oneloyalty.common.constant.ETransactionAttributeDataTypeDisplay;
import com.oneid.oneloyalty.common.constant.ETransactionBatchType;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import com.oneid.oneloyalty.common.constant.ETransactionType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.EscrowPointTransactionHistory;
import com.oneid.oneloyalty.common.entity.Function;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.MemberStatus;
import com.oneid.oneloyalty.common.entity.PayWithPointTransactionHistory;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramCorporation;
import com.oneid.oneloyalty.common.entity.ProgramTransactionAttribute;
import com.oneid.oneloyalty.common.entity.ReasonCode;
import com.oneid.oneloyalty.common.entity.RefundTransactionHistory;
import com.oneid.oneloyalty.common.entity.RewardPool;
import com.oneid.oneloyalty.common.entity.SapSaleOrderCall;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.entity.TransactionBatchRequest;
import com.oneid.oneloyalty.common.entity.TransactionHistory;
import com.oneid.oneloyalty.common.entity.TransactionHistoryAttribute;
import com.oneid.oneloyalty.common.entity.TransactionRequest;
import com.oneid.oneloyalty.common.entity.UserProfile;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CurrencyRateRepository;
import com.oneid.oneloyalty.common.repository.CurrencyRepository;
import com.oneid.oneloyalty.common.repository.FunctionRepository;
import com.oneid.oneloyalty.common.repository.PoolRepository;
import com.oneid.oneloyalty.common.repository.PosRepository;
import com.oneid.oneloyalty.common.repository.ProgramCorporationRepository;
import com.oneid.oneloyalty.common.repository.ProgramProductRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.ReasonCodeRepository;
import com.oneid.oneloyalty.common.repository.SapSaleOrderCallRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.repository.TransactionBatchRequestRepository;
import com.oneid.oneloyalty.common.repository.TransactionHistoryAttributeRepository;
import com.oneid.oneloyalty.common.repository.TransactionHistoryRepository;
import com.oneid.oneloyalty.common.repository.TransactionRepository;
import com.oneid.oneloyalty.common.repository.UserProfileRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.ChainService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.CurrencyService;
import com.oneid.oneloyalty.common.service.EscrowPointTransactionHistoryService;
import com.oneid.oneloyalty.common.service.FunctionService;
import com.oneid.oneloyalty.common.service.MemberService;
import com.oneid.oneloyalty.common.service.MemberStatusService;
import com.oneid.oneloyalty.common.service.PayWithPointTransactionService;
import com.oneid.oneloyalty.common.service.PoolService;
import com.oneid.oneloyalty.common.service.PosService;
import com.oneid.oneloyalty.common.service.ProgramCorporationService;
import com.oneid.oneloyalty.common.service.ProgramProductService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.service.ReasonCodeService;
import com.oneid.oneloyalty.common.service.RefundTransactionHistoryService;
import com.oneid.oneloyalty.common.service.RewardPoolService;
import com.oneid.oneloyalty.common.service.SchemeService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.service.TransactionAuditTrailService;
import com.oneid.oneloyalty.common.service.TransactionBatchRequestService;
import com.oneid.oneloyalty.common.service.TransactionHistoryService;
import com.oneid.oneloyalty.common.service.TransactionRequestService;
import com.oneid.oneloyalty.common.util.DateUtil;
import com.oneid.oneloyalty.common.util.JsonUtil;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import com.poiji.bind.Poiji;
import com.poiji.exception.PoijiExcelType;
import joptsimple.internal.Strings;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class OpsTransactionServiceImpl implements OpsTransactionService {
    @Value("${app.tmp.prefix.transaction.adjustment}")
    private String adjustedTransactionInvoiceNoPrefix;

    @Value("${app.tmp.prefix.transaction.reverse}")
    private String reverseTransactionInvoiceNoPrefix;

    @Value("${ops.params.default-transaction-channel}")
    private String defaultTransactionChannel;

    @Value("${ops.params.default-transaction-service-code}")
    private String defaultTransactionServiceCode;

    @Value("${app.file.max-line}")
    private Long fileMaxLine;

    @Value("${app.file.max-line-2}")
    private Long fileMaxLine2;

    private final char RANDOM_CHAR = '#';

    private final int MIN_PATTERN_LENGTH = 10;

    private final int MAX_PATTERN_LENGTH = 50;

    private final String SAP_API_STATUS_SUCCESS = "01";

    private final String SAP_CALL_TYPE_CREATE = "CREATE";

    private final String SAP_CALL_TYPE_UPDATE = "UPDATE";

    private final String SAP_CALL_STATUS_SUCCESS = "SUCCESS";

    private DateTimeFormatter FORMATTER_dd_MM_YYYY = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");

    private final SimpleDateFormat FORMATTER_yyyyMMdd = new SimpleDateFormat("yyyyMMdd");

    @Autowired
    private CheckLogSourceConfigPram checkLogSourceConfigPram;

    @Autowired
    TransactionHistoryRepository transactionHistoryRepository;

    @Autowired
    TransactionRepository transactionRepository;

    @Autowired
    private ProgramRepository programRepository;

    @Autowired
    private ReasonCodeRepository reasonCodeRepository;

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private ChainRepository chainRepository;

    @Autowired
    private ProgramCorporationRepository programCorporationRepository;

    @Autowired
    private PosRepository posRepository;

    @Autowired
    private PoolRepository poolRepository;

    @Autowired
    private CurrencyRepository currencyRepository;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private CorporationService corporationService;

    @Autowired
    private ChainService chainService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private PosService posService;

    @Autowired
    private PoolService poolService;

    @Autowired
    private OpsBusinessService opsBusinessService;

    @Autowired
    private OpsProgramService opsProgramService;

    @Autowired
    private OpsCorporationService opsCorporationService;

    @Autowired
    private OpsChainService opsChainService;

    @Autowired
    private OpsStoreService opsStoreService;

    @Autowired
    private OpsTerminalService opsTerminalService;

    @Autowired
    private MemberService memberService;

    @Autowired
    private TransactionHistoryService transactionHistoryService;

    @Autowired
    private CurrencyService currencyService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private SchemeService schemeService;

    @Autowired
    private ReasonCodeService reasonCodeService;

    @Autowired
    private ProgramTransactionAttributeService programTransactionAttributeService;

    @Autowired
    private TransactionBatchRequestRepository transactionBatchRequestRepository;

    @Autowired
    private CurrencyRateRepository currencyRateRepository;

    @Autowired
    private MasterWorkerFeignClient masterWorkerFeignClient;

    @Autowired
    private SapFeignClient sapFeignClient;

    @Autowired
    private OneloyaltyServiceFeignClient oneloyaltyServiceFeignClient;

    @Autowired
    private TransactionAuditTrailService transactionAuditTrailService;

    @Autowired
    private FunctionRepository functionRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private ProgramProductService programProductService;

    @Autowired
    private ProgramCorporationService programCorporationService;

    @Autowired
    private FunctionService functionService;

    @Autowired
    private TransactionBatchRequestService transactionBatchRequestService;

    @Autowired
    private TransactionRequestService transactionRequestService;

    @Autowired
    private OpsCommonExcelService commonExcelService;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private TransactionHistoryAttributeRepository transactionHistoryAttributeRepository;

    @Autowired
    private SapSaleOrderCallRepository sapSaleOrderCallRepository;

    @Autowired
    private AuditorAwareUtil auditorAwareUtil;

    @Autowired
    private AttributeMasterDataRepository attributeMasterDataRepository;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private RewardPoolService rewardPoolService;

    @Autowired
    private PayWithPointTransactionService payWithPointTransactionService;

    @Autowired
    private MemberStatusService memberStatusService;

    @Autowired
    private EscrowPointTransactionHistoryService escrowPointTransactionHistoryService;

    @Autowired
    private RefundTransactionHistoryService refundTransactionHistoryService;

    @Autowired
    private ProgramProductRepository programProductRepository;

    @Autowired
    private OneloyaltyElasticFeignClient oneloyaltyElasticFeignClient;

    @Autowired
    private ObjectMapper jsonMapper;

    @Override
    public Page<TransactionRes> search(TransactionSearchReq transactionSearchReq, Pageable pageRequest,
                                       Boolean getRefundedInvoice) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        transactionSearchReq.setBusinessId(business.getId());
        EOpsCancellationType cancellationType = transactionSearchReq.getCancellationType();

        // find member id
        if (transactionSearchReq.getAccountType() != null && transactionSearchReq.getAccountCode() != null) {
            Member member = null;
            try {
                member = memberService.find(new CustomerIdentify(transactionSearchReq.getAccountCode(), transactionSearchReq.getAccountType().getMapping()), transactionSearchReq.getProgramId());
            } catch (BusinessException e) {

            }
            if (member == null) {
                return Page.empty(pageRequest);
            }
            transactionSearchReq.setMemberId(member.getId());
        }

        Long memberId = transactionSearchReq.getMemberId();

        if (transactionSearchReq.getProgramId() == null &&
                (StringUtils.isNotBlank(transactionSearchReq.getMemberCode()) || transactionSearchReq.getUserId() != null)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Search with user_id/member_code is required program_id", null);
        } else if (StringUtils.isNotBlank(transactionSearchReq.getMemberCode())
                || StringUtils.isNotBlank(transactionSearchReq.getUserId())) {

            Program program = programRepository.findByIdAndBusinessId(transactionSearchReq.getProgramId(), business.getId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null)
            );

            if (transactionSearchReq.getBusinessId() != null &&
                    !program.getBusinessId().equals(transactionSearchReq.getBusinessId())) {
                return Page.empty(pageRequest); // conflict condition search;
            }

            if (StringUtils.isNotBlank(transactionSearchReq.getUserId())) {
                UserProfile userProfile = userProfileRepository.findByMasterUserIdAndBusinessId(
                        transactionSearchReq.getUserId(), program.getBusinessId()
                );
                if (userProfile == null) {
                    throw new BusinessException(ErrorCode.USER_PROFILE_NOT_FOUND, "User profile not found", null);
                }
                Long memberIdTmp = memberService.findByUserProfileAndProgramId(userProfile.getId(), program.getId())
                        .orElseThrow(
                                () -> new BusinessException(ErrorCode.MEMBER_NOT_FOUND, "Member not found", null)
                        ).getId();
                if (memberIdTmp.equals(memberId) || memberId == null) {
                    memberId = memberIdTmp;
                } else { // conflict condition search relative member
                    return Page.empty(pageRequest);
                }
            }
            if (StringUtils.isNotBlank(transactionSearchReq.getMemberCode())) {
                Optional<Member> member = memberService.find(transactionSearchReq.getMemberCode(), transactionSearchReq.getProgramId());
                if (member.isPresent()) {
                    Long memberIdTmp = member.get().getId();
                    if (memberIdTmp.equals(memberId) || memberId == null) {
                        memberId = memberIdTmp;
                    } else {  // conflict condition search relative member
                        return Page.empty(pageRequest);
                    }
                } else {
                    return new PageImpl<TransactionRes>(Collections.emptyList(), pageRequest, 0);
                }
            }
        }

        Page<TransactionHistory> pageTxn = transactionHistoryService.getTnx(
                transactionSearchReq.getBusinessId(),
                transactionSearchReq.getProgramId(),
                memberId,
                transactionSearchReq.getStoreId(),
                transactionSearchReq.getTerminalId(),
                Objects.nonNull(transactionSearchReq.getTransactionFrom()) ? new Date(transactionSearchReq.getTransactionFrom() * 1000) : null,
                Objects.nonNull(transactionSearchReq.getTransactionTo()) ? new Date(transactionSearchReq.getTransactionTo() * 1000) : null,
                transactionSearchReq.getInvoiceNumber(),
                cancellationType != null ? cancellationType.getValue() : null,
                transactionSearchReq.getCorporationId(),
                transactionSearchReq.getChainId(),
                transactionSearchReq.getTnxRefNo(),
                transactionSearchReq.getStatus(),
                transactionSearchReq.getAttributes(),
                transactionSearchReq.getTransactionType() != null ? transactionSearchReq.getTransactionType() : null,
                transactionSearchReq.getIsAdjustAward(),
                transactionSearchReq.getTransactionValue(),
                Objects.nonNull(transactionSearchReq.getValueMin()) ? BigDecimal.valueOf(Double.parseDouble(transactionSearchReq.getValueMin())) : null,
                Objects.nonNull(transactionSearchReq.getValueMax()) ? BigDecimal.valueOf(Double.parseDouble(transactionSearchReq.getValueMax())) : null,
                pageRequest,
                transactionSearchReq.getPoolType()
        );

        List<TransactionHistory> txns = pageTxn.getContent().stream().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(txns))
            return new PageImpl<TransactionRes>(Collections.emptyList(), pageRequest, 0);

        Set<Integer> businessIds = txns.stream().map(TransactionHistory::getBusinessId).collect(Collectors.toSet());
        Map<Integer, Business> businessByIds = opsBusinessService.getMapById(businessIds);

        Set<Integer> corporationIds = txns.stream().map(TransactionHistory::getCorporationId).collect(Collectors.toSet());
        Map<Integer, Corporation> corporationByIds = opsCorporationService.getMapById(corporationIds);

        Set<Integer> chainIds = txns.stream().map(TransactionHistory::getChainId).collect(Collectors.toSet());
        Map<Integer, Chain> chainByIds = opsChainService.getMapById(chainIds);

        Set<Integer> storeIds = txns.stream().map(TransactionHistory::getStoreId).collect(Collectors.toSet());
        Map<Integer, Store> storeByIds = opsStoreService.getMapById(storeIds);

        Set<Integer> posIds = txns.stream().map(TransactionHistory::getPosId).collect(Collectors.toSet());
        Map<Integer, Pos> posByIds = opsTerminalService.getMapById(posIds);

        Set<Integer> programIds = txns.stream().map(TransactionHistory::getProgramId).collect(Collectors.toSet());
        Map<Integer, Program> programByIds = opsProgramService.getMapById(programIds);

        Set<Long> memberIds = txns.stream().map(TransactionHistory::getMemberId).collect(Collectors.toSet());
        Map<Long, Member> members = memberService.findByIdIn(memberIds).stream()
                .collect(Collectors.toMap(Member::getId, member -> member));

        if (getRefundedInvoice != null) {
            if (memberId == null) {
                // todo verify business logic when cannot search by member id?
                return new PageImpl<TransactionRes>(Collections.emptyList(), pageRequest, 0);
            }
        }

        final Long finalMemberId = memberId;
        Map<String, Optional<TransactionRes.TransactionRefunded>> originalInvoiceToTransactionRefundedMap =
                Boolean.TRUE.equals(getRefundedInvoice) ? txns.stream()
                        .collect(Collectors.toMap(
                                TransactionHistory::getInvoiceNo,
                                txnH -> {
                                    List<TransactionHistory> txnHistoryRefundeds = transactionHistoryService
                                            .findTxnRefundedByMemberIdAndOriginalInvoiceNo(
                                                    finalMemberId, txnH.getInvoiceNo()
                                            );

                                    return Optional.ofNullable(getTransactionRefunded(txnH, txnHistoryRefundeds));
                                },
                                (first, second) -> first)) : new HashMap<>();

        return new PageImpl<>(txns.stream().map(t -> {
                    PoolShortRes poolShort = null;
                    RewardPool rewardPool = null;
                    if (Objects.nonNull(t.getPoolId())) {
                        Pool pool = poolService.find(t.getPoolId()).orElse(null);
                        if (Objects.nonNull(pool)) {
                            rewardPool = rewardPoolService.findById(pool.getRefId()).orElse(null);
                        }
                    } else if (Objects.nonNull(t.getRewardPoolId())) {
                        rewardPool = rewardPoolService.findById(t.getRewardPoolId()).orElse(null);
                    }
                    poolShort = Objects.nonNull(rewardPool) ? new PoolShortRes(rewardPool.getId(), rewardPool.getPoolCode(), rewardPool.getName(), null, null, null, rewardPool.getPoolType()) : null;

                    return TransactionRes.valueOfList(
                            t,
                            businessByIds.get(t.getBusinessId()),
                            corporationByIds.get(t.getCorporationId()),
                            chainByIds.get(t.getChainId()),
                            storeByIds.get(t.getStoreId()),
                            posByIds.get(t.getPosId()),
                            programByIds.get(t.getProgramId()),
                            members.get(t.getMemberId()),
                            originalInvoiceToTransactionRefundedMap
                                    .getOrDefault(t.getInvoiceNo(), Optional.ofNullable(null))
                                    .orElse(null),
                            null,
                            null,
                            null,
                            poolShort,
                            null
                    );
                })
                .collect(Collectors.toList()), pageRequest, pageTxn.getTotalElements());
    }

    @Override
    public TransactionRes getTransactionDetails(String pointTnxId) {
        List<TransactionHistory> transactionHistoryListAll = transactionHistoryService.getByTnxPointIds(Collections.singletonList(pointTnxId));
        if (transactionHistoryListAll.size() == 0)
            throw new OpsBusinessException(OpsErrorCode.TRANSACTION_NOT_FOUND, "transaction not found", pointTnxId);

        Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistoryListAll);
        EOpsTransactionType transactionType = getTransactionType(points);
        TransactionHistory transactionHistory = transactionHistoryListAll.get(0);

        Business business = businessRepository.findById(transactionHistory.getBusinessId()).orElse(null);
        Corporation corporation = corporationService.find(transactionHistory.getCorporationId()).orElse(null);
        Chain chain = chainService.find(transactionHistory.getChainId()).orElse(null);
        Store store = storeService.find(transactionHistory.getStoreId());
        Pos pos = posService.find(transactionHistory.getPosId()).orElse(null);

        Program program = programRepository.findById(transactionHistory.getProgramId()).orElse(null);


        Member member = memberService.find(transactionHistory.getMemberId()).orElse(null);

        List<Scheme> schemes = schemeService.findByProgramIdAndListIds(transactionHistory.getProgramId(),
                transactionHistoryListAll
                        .stream().map(TransactionHistory::getSchemeId)
                        .collect(Collectors.toList())
        );

        List<Pool> pools = poolRepository.findAllByIdIn(
                transactionHistoryListAll
                        .stream().map(TransactionHistory::getPoolId)
                        .collect(Collectors.toList())
        );

        List<Currency> currencies = currencyService.findByCurrenciesId(
                pools.stream().map(Pool::getCurrencyId).collect(Collectors.toList())
        );
        Map<Integer, ShortEntityRes> mapSchemeIdToRes, mapPoolIdToRes, mapCurrencyIdToRes;

        mapSchemeIdToRes = schemes.stream().collect(Collectors.toMap(
                Scheme::getId,
                scheme -> new ShortEntityRes(scheme.getId(), scheme.getName(), scheme.getCode())
        ));
        mapPoolIdToRes = pools.stream().collect(Collectors.toMap(
                Pool::getId,
                pool -> new ShortEntityRes(pool.getId(), pool.getName(), pool.getCode())
        ));
        mapCurrencyIdToRes = currencies.stream().collect(Collectors.toMap(
                Currency::getId,
                currency -> new ShortEntityRes(currency.getId(), currency.getName(), currency.getCode())
        ));

        Map<Integer, Integer> poolIdToCurrencyId = pools.stream()
                .collect(Collectors.toMap(Pool::getId, Pool::getCurrencyId));


        List<PointAwardDetail> pointAwardDetails = transactionHistoryListAll.stream()
                .filter(txnH -> (txnH.getType() == ETransactionType.AWARD || txnH.getType() == ETransactionType.ADJUSTMENT) && txnH.getAwardPoint().compareTo(BigDecimal.ZERO) > 0
                )
                .map(txnH -> new PointAwardDetail(
                        mapPoolIdToRes.get(txnH.getPoolId()),
                        txnH.getAwardPoint(),
                        mapCurrencyIdToRes.get(poolIdToCurrencyId.get(txnH.getPoolId())),
                        mapSchemeIdToRes.get(txnH.getSchemeId()),
                        txnH.getAwardRetentionTime() != null
                                ? txnH.getAwardRetentionTime().toInstant().getEpochSecond()
                                : null,
                        txnH.getBalanceBefore(),
                        txnH.getBalanceAfter()
                )).collect(Collectors.toList());

        List<PointRedeemDetail> pointRedeemDetails = transactionHistoryListAll.stream()
                .filter(txnH -> (txnH.getType() == ETransactionType.REDEEM || txnH.getType() == ETransactionType.ADJUSTMENT) && txnH.getRedeemPoint().compareTo(BigDecimal.ZERO) > 0
                )
                .map(txnH -> new PointRedeemDetail(
                        mapPoolIdToRes.get(txnH.getPoolId()),
                        mapCurrencyIdToRes.get(poolIdToCurrencyId.get(txnH.getPoolId())),
                        mapSchemeIdToRes.get(txnH.getSchemeId()),
                        txnH.getRedeemPoint(),
                        txnH.getBalanceBefore(),
                        txnH.getBalanceAfter()
                )).collect(Collectors.toList());

        Long finalMemberId = transactionHistory.getMemberId();
        String invoiceNo = transactionHistory.getInvoiceNo();
        List<TransactionHistory> txnHistoryRefundeds = transactionHistoryService
                .findTxnRefundedByMemberIdAndOriginalInvoiceNo(
                        finalMemberId, invoiceNo
                );

        TransactionRes.TransactionRefunded refunded = getTransactionRefunded(transactionHistory, txnHistoryRefundeds);

        ReasonCode reasonCode = null;

        if (transactionHistory.getReasonCode() != null) {
            reasonCode = reasonCodeRepository.findByBusinessIdAndProgramIdAndCode(business.getId(), program.getId(), transactionHistory.getReasonCode());
        }

        Double sellRate = null;
        
        /* Currency baseCurrency = getBaseCurrencyFromTransaction(transactionHistory, program, transactionType);
        if (baseCurrency != null && transactionHistory.getPoolId() != null) {
            sellRate = findSellRate(baseCurrency.getCode(), transactionHistory.getPoolId(), transactionHistory.getBusinessId() );
        } */

        Pool pool = null;
        if (transactionHistory.getPoolId() != null) {
            pool = poolRepository.findById(transactionHistory.getPoolId()).orElse(null);
        }

        UserProfile userProfile = member.getUserProfileId() != null ? userProfileRepository.findById(member.getUserProfileId())
                .orElseThrow(() -> new BusinessException(ErrorCode.USER_PROFILE_NOT_FOUND, "User profile not found", null)) : null;

        return TransactionRes.valueOf(
                points,
                transactionHistory,
                transactionType,
                business,
                corporation,
                chain,
                store,
                pos,
                program,
                member,
                userProfile,
                refunded,
                pointAwardDetails,
                pointRedeemDetails,
                reasonCode,
                pool,
                sellRate
        );
    }

    @Override
    public TransactionBatchRequestDetailRes getTransactionBatchDetails(Long batchId) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findByIdAndBusinessId(batchId, business.getId());
        Program program = programService.findByIdAndBusinessId(batchRequest.getProgramId(), business.getId());

        return TransactionBatchRequestDetailRes
                .builder()
                .batchNo(batchRequest.getId())
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .batchRequestType(batchRequest.getType())
                .transactionBatchType(batchRequest.getTransactionBatchType())
                .batchName(batchRequest.getName())
                .referenceCode(batchRequest.getReferenceCode())
                .description(batchRequest.getDescription())
                .isReplaceDes(batchRequest.getIsReplaceDes())
                .createdBy(batchRequest.getCreatedBy())
                .createdAt(batchRequest.getCreatedAt())
                .updatedBy(batchRequest.getCreatedBy())
                .updatedAt(batchRequest.getCreatedAt())
                .approvedBy(batchRequest.getApprovedBy())
                .approvedAt(batchRequest.getApprovedAt())
                .finishedAt(EBatchRequestProcessStatus.COMPLETED.equals(batchRequest.getProcessStatus()) ? batchRequest.getUpdatedAt() : null)
                .processStatus(batchRequest.getProcessStatus())
                .build();
    }

    @Override
    public Page<TransactionSearchRes> searchAvailable(SearchAvailableTransactionReq req, Pageable page) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        req.setBusinessId(business.getId());
        programService.findByIdAndBusinessId(req.getProgramId(), business.getId());

        Page<Object[]> result = transactionBatchRequestService.filter(
                page,
                EApprovalStatus.APPROVED,
                req.getCreatedBy(),
                req.getCreatedStart(),
                req.getCreatedEnd(),
                req.getApprovedBy(),
                req.getApprovedStart(),
                req.getApprovedEnd(),
                req.getBusinessId(),
                req.getProgramId(),
                req.getBatchNo(),
                req.getBatchName(),
                req.getBatchRequestType(),
                req.getTransactionBatchType(),
                req.getEnableSMS(),
                req.getFailedRecords(),
                req.getStatus(),
                null,
                false
        );

        List<TransactionSearchRes> transactionBatchRequestRes = result.stream().map(
                batch -> {
                    TransactionBatchRequest transactionBatchRequest = (TransactionBatchRequest) batch[0];
                    Program program = (Program) batch[2];

                    EBoolean enableSMS = StringUtils.EMPTY.equals(transactionBatchRequest.getSmsTemplate()) ||
                            transactionBatchRequest.getSmsTemplate() == null ?
                            EBoolean.NO : EBoolean.YES;
                    Date generateDate = !EBatchRequestProcessStatus.COMPLETED.equals(transactionBatchRequest.getProcessStatus()) ?
                            null : transactionBatchRequest.getUpdatedAt();
                    return TransactionSearchRes.builder()
                            .program(Objects.nonNull(program) ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                            .batchNo(transactionBatchRequest.getId())
                            .batchName(transactionBatchRequest.getName())
                            .batchRequestType(transactionBatchRequest.getType())
                            .transactionBatchType(transactionBatchRequest.getTransactionBatchType())
                            .enableSMS(enableSMS)
                            .failedRecords(transactionBatchRequest.getFailedRequests())
                            .createdAt(transactionBatchRequest.getCreatedAt())
                            .generateDate(generateDate)
                            .processStatus(transactionBatchRequest.getProcessStatus())
                            .createdBy(transactionBatchRequest.getCreatedBy())
                            .approvedAt(transactionBatchRequest.getApprovedAt())
                            .approvedBy(transactionBatchRequest.getApprovedBy())
                            .build();
                }
        ).collect(Collectors.toList());

        return new PageImpl<>(transactionBatchRequestRes, page, result.getTotalElements());
    }

    @Override
    public APIResponse<?> createTransaction(CreateTransactionReq req) {
        Business business = businessRepository.findById(req.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));

        if (!business.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, "Business not active", null);

        Program program = programRepository.findByIdAndBusinessId(req.getProgramId(), business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        if (!program.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, "Program not active", null);

        ProgramCorporation programCorporation = programCorporationRepository.findByProgramIdAndCorporationId(program.getId(), req.getCorporationId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_CORPORATION_NOT_FOUND, "Program Corporation not found", null));

        Chain chain = chainRepository.findByBusinessIdAndCorporationIdAndId(business.getId(), programCorporation.getCorporationId(), req.getChainId())
                .orElseThrow(() -> new BusinessException(ErrorCode.CHAIN_NOT_FOUND, "Chain not found", null));


        Store store = storeRepository.findByIdAndBusinessIdAndCorporationIdAndChainId(req.getStoreId(), business.getId(), programCorporation.getCorporationId(), chain.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.STORE_NOT_FOUND, "Store not found", null));

        if (!store.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.STORE_NOT_ACTIVE, "Store not active", null);

        Pos pos = posRepository.findByIdAndStoreId(req.getTerminalId(), store.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.POS_NOT_FOUND, "Terminal (POS) not found", null));

        if (!pos.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.TERMINAL_NOT_ACTIVE, "Terminal (POS) not active", null);

        CreateTransactionReq.Transaction transaction = req.getTransaction();

        TransactionInfoFeignReq transactionInfoFeignReq = TransactionInfoFeignReq.builder()
                .invoiceNo(transaction.getInvoiceNo())
                .transactionTime(req.getTransactionTime())
                .terminalCode(pos.getCode())
                .storeCode(store.getCode())
                .channelId(chain.getId())
                .build();

        CreateTransactionFeignReq createTransactionFeignReq = CreateTransactionFeignReq.builder()
                .customerIdentifier(CustomerFeignReq.builder()
                        .id(req.getMemberCode())
                        .idType(req.getIdType().getMapping())
                        .build())
                .businessCode(business.getCode())
                .programCode(program.getCode())
                .transaction(transactionInfoFeignReq)
                .createdBy(
                        ((OPSAuthenticatedPrincipal) SecurityContextHolder.getContext()
                                .getAuthentication()
                                .getPrincipal())
                                .getUserName())
                .build();

        if (req.getTransactionType().equals(EOpsTransactionType.ADJUSTMENT)) {
            if (transaction.getAdjustPoint() == null || transaction.getAdjustPoint().compareTo(BigDecimal.ZERO) != 1)
                throw new BusinessException(OpsErrorCode.ADJUST_POINT_MUST_GREATER_THAN_ZERO.getValue(), "Adjust point must greater than zero", null);

            Function function = functionRepository.findByCode(EOpsFunctionCode.POINT_ADJ.getValue());

            ReasonCode reasonCode = reasonCodeRepository.findByBusinessIdAndProgramIdAndId(business.getId(), program.getId(), transaction.getReasonId())
                    .orElseThrow(() -> new BusinessException(ErrorCode.REASON_CODE_NOT_FOUND, "Reason Code not found", null));

            if (reasonCode.getFunctionId() != function.getId())
                throw new BusinessException(OpsErrorCode.REASON_CODE_IS_INVALID.getValue(), "Reason Code is invalid", null);

            Pool pool = poolRepository.findByIdAndBusinessIdAndProgramId(transaction.getPoolId(), business.getId(), program.getId())
                    .orElseThrow(() -> new BusinessException(ErrorCode.POOL_NOT_FOUND, "Pool not found", null));

            transactionInfoFeignReq.setInvoiceNo(String.format(adjustedTransactionInvoiceNoPrefix, new Date().toInstant().toEpochMilli()));
            transactionInfoFeignReq.setReasonCode(reasonCode.getCode());
            transactionInfoFeignReq.setPoolCode(pool.getCode());
            transactionInfoFeignReq.setAdjustPoint(transaction.getAdjustPoint());
            transactionInfoFeignReq.setAdjustmentType(transaction.getAdjustmentType());

            if (!pool.getStatus().equals(ECommonStatus.ACTIVE))
                throw new BusinessException(ErrorCode.POOL_NOT_ACTIVE, "Pool not active", null);

            return oneloyaltyServiceFeignClient.createAdjustmentTransaction(createTransactionFeignReq);
        } else {
            Currency currency = currencyRepository.findByCodeAndBusinessId(transaction.getCurrencyCode(), business.getId());
            createTransactionFeignReq.setCurrencyCode(currency.getCode());
            transactionInfoFeignReq.setGrossAmount(transaction.getGrossAmount());
            transactionInfoFeignReq.setGmv(transaction.getGmv());
            transactionInfoFeignReq.setRedeemPoint(transaction.getRedeemPoint());

            switch (req.getTransactionType()) {
                case SALE:
                    return oneloyaltyServiceFeignClient.createSaleTransaction(createTransactionFeignReq);

                case EARN:
                    transactionInfoFeignReq.setRedeemPoint(BigDecimal.ZERO);
                    return oneloyaltyServiceFeignClient.createEarnTransaction(createTransactionFeignReq);

                case BURN:
                    transactionInfoFeignReq.setGmv(BigDecimal.ZERO);
                    transactionInfoFeignReq.setGrossAmount(BigDecimal.ZERO);

                    return oneloyaltyServiceFeignClient.createBurnTransaction(createTransactionFeignReq);

                default:
            }
        }

        throw new OpsBusinessException(OpsErrorCode.TRANSACTION_TYPE_NOT_SUPPORT, "Transaction type not support", null);
    }

    @Override
    public ResourceDTO verifyTransactionByBatch(CreateTransactionBatchRequestReq req, MultipartFile file) throws Exception {
        List dtos = Optional.ofNullable(this.mappingToExcelDto(req.getTransactionBatchType(), file))
                .orElse(Collections.emptyList());
        boolean isValid = this.verifyCreateTransaction(req, dtos);
        return isValid ? null : this.exportFileExcel(req.getTransactionBatchType(), file.getOriginalFilename(), dtos);
    }


    @Override
    public ResourceDTO createTransactionByBatch(CreateTransactionBatchRequestReq req, MultipartFile file) throws Exception {
        Assert.assertTrue(StringUtils.isBlank(req.getBatchName()), "batch_name not be empty");
        List dtos = Optional.ofNullable(this.mappingToExcelDto(req.getTransactionBatchType(), file))
                .orElse(Collections.emptyList());
        if (EGenerationInvoiceNoMethod.CUSTOM.equals(req.getGenInvoiceNoMethod())) {
            validateGenInvoiceNo(req.getGenInvoiceNoPattern());
        }
        boolean isValid = this.verifyCreateTransaction(req, dtos);
        if (!isValid) {
            return this.exportFileExcel(req.getTransactionBatchType(), file.getOriginalFilename(), dtos);
        }
        // Create transaction
        Long batchId = this.saveTransaction(req, dtos, EBatchRequestType.BATCH);
        return ResourceDTO.builder()
                .filename(String.valueOf(batchId))
                .resource(null)
                .build();
    }

    @Override
    public APIResponse<?> revertTransaction(RevertTransactionReq payload) {
        List<TransactionHistory> transactionHistories = transactionHistoryService.getByTnxPointIds(Collections.singletonList(payload.getTransactionId()));

        if (transactionHistories.size() == 0)
            throw new OpsBusinessException(OpsErrorCode.TRANSACTION_NOT_FOUND, "Transaction not found", null);

        Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistories);

        EOpsTransactionType transactionType = getTransactionType(points);

        if (EOpsTransactionType.ADJUSTMENT.equals(transactionType))
            throw new UnsupportedOperationException("Unsupported reverting adjustment transaction type!");

        TransactionHistory transaction = transactionHistories.get(0);

        Business business = businessRepository.findById(transaction.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));

        if (!business.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, "Business not active", null);

        Program program = programRepository.findById(transaction.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        if (!program.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, "Program not active", null);

        Currency baseCurrency = getBaseCurrencyFromTransaction(transaction, program, transactionType);

        if (!payload.getOriginalInvoiceNo().equals(transaction.getInvoiceNo()))
            throw new OpsBusinessException(OpsErrorCode.TRANSACTION_NOT_FOUND, "Transaction not found", null);

        ReverseTransactionInfoFeignReq transactionInfoFeignReq;

        if (payload.getIsPartial()) {
            if (payload.getNewTxnInfo() == null)
                throw new OpsBusinessException(OpsErrorCode.NEW_TXN_INFO_FOR_REVERT_NOT_FOUND, "new txn info for revert not found", null);

            Store store = storeService.findActive(payload.getNewTxnInfo().getStoreId());
            Pos terminal = posService.findActive(payload.getNewTxnInfo().getTerminalId());

            RevertTransactionReq.NewTxnInfo txnInfo = payload.getNewTxnInfo();

            transactionInfoFeignReq = ReverseTransactionInfoFeignReq.builder()
                    .storeCode(store.getCode())
                    .terminalCode(terminal.getCode())
                    .invoiceNo(txnInfo.getInvoiceNo() != null ? txnInfo.getInvoiceNo() : generateTxnInvoiceNo())
                    .refundAmount(payload.getNewTxnInfo().getRefundAmount())
                    .redeemPoint(payload.getNewTxnInfo().getRedeemPoint())
                    .transactionTime(txnInfo.getTransactionTime() != null ? txnInfo.getTransactionTime() : new Date().toInstant().getEpochSecond())
                    .description(payload.getNewTxnInfo() != null ? payload.getNewTxnInfo().getDescription() : null)
                    .build();
        } else { // full
            Store store = storeService.findActive(transaction.getStoreId());
            Pos terminal = posService.findActive(transaction.getPosId());

            transactionInfoFeignReq = ReverseTransactionInfoFeignReq.builder()
                    .storeCode(store.getCode())
                    .terminalCode(terminal.getCode())
                    .invoiceNo(generateTxnInvoiceNo())
                    .refundAmount(transaction.getGrossAmount())
                    .redeemPoint(BigDecimal.valueOf(0))
                    .transactionTime(new Date().toInstant().getEpochSecond())
                    .build();
        }

        ReverseTransactionFeignReq req = ReverseTransactionFeignReq.builder()
                .customerIdentifier(CustomerFeignReq.builder()
                        .id(transaction.getMemberProductAccountCode())
                        .idType(transaction.getMemberProductAccountType())
                        .build())
                .transaction(transactionInfoFeignReq)
                .originalInvoiceNo(payload.getOriginalInvoiceNo())
                .businessCode(business.getCode())
                .programCode(program.getCode())
                .currencyCode(baseCurrency == null ? null : baseCurrency.getCode())
                .createdBy(((OPSAuthenticatedPrincipal) SecurityContextHolder.getContext()
                        .getAuthentication().getPrincipal())
                        .getUserName())
                .build();

        return oneloyaltyServiceFeignClient.refundTransaction(req);
    }

    @Override
    public List<TransactionCheckLogRes> checkLog(String pointTnxId) {
        return transactionAuditTrailService.findAllByTransactionRef(pointTnxId).stream()
                .map(en -> TransactionCheckLogRes.builder()
                        .id(en.getId())
                        .transactionRef(en.getTransactionRef())
                        .businessId(en.getBusinessId())
                        .eventType(en.getEventType())
                        .userId(en.getUserId())
                        .payload(en.getPayload())
                        .sourse(convertSourceToResponse(en.getSource(), en.getPayload()))
                        .createdAt(en.getCreatedAt() != null ? en.getCreatedAt().toInstant().getEpochSecond() : null)
                        .additionalSourceInfo(en.getAdditionalSourceInfo())
                        .build()
                ).collect(Collectors.toList());
    }

    @Override
    public Page<TransactionRes> search(
            Integer businessId,
            Integer programId,
            String userProfileId,
            String transactionType,
            Integer transactionTimeFrom,
            Integer transactionTimeTo,
            Pageable pageable) {
        UserProfile userProfile = userProfileRepository.findByMasterUserIdAndBusinessId(userProfileId, businessId);

        Business business = businessRepository.findById(businessId)
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        Program program = programRepository.findByIdAndBusinessId(programId, business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        if (userProfile == null)
            throw new BusinessException(ErrorCode.USER_PROFILE_NOT_FOUND, null, null);

        Member member = memberService.findByUserProfileAndProgramId(userProfile.getId(), program.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_NOT_FOUND, null, null));

        ETransactionType eTransactionType = null;

        if (StringUtils.isNoneBlank(transactionType))
            eTransactionType = ETransactionType.of(transactionType);

        Date startTime = null;
        Date endTime = null;

        if (transactionTimeFrom == null || transactionTimeTo == null) {
            endTime = new Date();

            startTime = Date.from(
                    LocalDate.now().minusDays(90).atStartOfDay()
                            .atZone(ZoneId.systemDefault())
                            .toInstant());
        } else {
            startTime = Date.from(Instant.ofEpochSecond(transactionTimeFrom));
            endTime = Date.from(Instant.ofEpochSecond(transactionTimeTo));
        }

        Page<String> page = transactionHistoryRepository.getPointTnxIds(
                businessId,
                programId,
                userProfile.getId(),
                eTransactionType,
                startTime,
                endTime,
                pageable);

        List<String> pointTnxIds = page.getContent().stream().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pointTnxIds))
            return new PageImpl<TransactionRes>(Collections.emptyList(), pageable, 0);

        List<TransactionHistory> transactionHistoryListAll = transactionHistoryService.getByTnxPointIds(pointTnxIds);

        Map<String, List<TransactionHistory>> transactionHistoryByPointTnxIds = transactionHistoryListAll.stream()
                .collect(
                        Collectors.groupingBy(
                                TransactionHistory::getPointTransactionId,
                                Collectors.toList()));

        List<TransactionHistory> transactionHistories = transactionHistoryByPointTnxIds.values()
                .stream()
                .filter(t -> t.size() > 0)
                .map(t -> t.get(0))
                .sorted(getSortForSearch(Sort.Direction.DESC))
                .collect(Collectors.toList());

        Set<Integer> corporationIds = transactionHistories.stream().map(TransactionHistory::getCorporationId).collect(Collectors.toSet());
        Map<Integer, Corporation> corporationByIds = opsCorporationService.getMapById(corporationIds);

        Set<Integer> chainIds = transactionHistories.stream().map(TransactionHistory::getChainId).collect(Collectors.toSet());
        Map<Integer, Chain> chainByIds = opsChainService.getMapById(chainIds);

        Set<Integer> storeIds = transactionHistories.stream().map(TransactionHistory::getStoreId).collect(Collectors.toSet());
        Map<Integer, Store> storeByIds = opsStoreService.getMapById(storeIds);

        Set<Integer> posIds = transactionHistories.stream().map(TransactionHistory::getPosId).collect(Collectors.toSet());
        Map<Integer, Pos> posByIds = opsTerminalService.getMapById(posIds);

        return new PageImpl<TransactionRes>(
                transactionHistories.stream()
                        .map(each -> {
                            Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistoryByPointTnxIds.get(each.getPointTransactionId()));

                            return TransactionRes.valueOf(
                                    points,
                                    each,
                                    getTransactionType(points),
                                    business,
                                    corporationByIds.get(each.getCorporationId()),
                                    chainByIds.get(each.getChainId()),
                                    storeByIds.get(each.getStoreId()),
                                    posByIds.get(each.getPosId()),
                                    program,
                                    member,
                                    userProfile);
                        })
                        .collect(Collectors.toList()),
                pageable,
                page.getTotalElements());
    }

    private String generateTxnInvoiceNo() {
        return String.format(reverseTransactionInvoiceNoPrefix, new Date().toInstant().toEpochMilli());
    }

    private TransactionRes.TransactionRefunded getTransactionRefunded(TransactionHistory txnH,
                                                                      List<TransactionHistory> txnHistoryRefundeds) {
        return txnHistoryRefundeds.size() != 0 ?
                new TransactionRes.TransactionRefunded(
                        txnHistoryRefundeds.get(0).getInvoiceNo(),
                        txnHistoryRefundeds.get(0).getPointTransactionId(),
                        txnH.getGrossAmount() != null
                                ? txnH.getGrossAmount().subtract(txnHistoryRefundeds.get(0).getGrossAmount())
                                : BigDecimal.ZERO
                ) : null;
    }

    private EOpsTransactionType getTransactionType(Map<ETransactionType, List<BigDecimal>> points) {
        if (points.containsKey(ETransactionType.ADJUSTMENT))
            return EOpsTransactionType.ADJUSTMENT;

        if (points.containsKey(ETransactionType.AWARD) && points.containsKey(ETransactionType.REDEEM)) {
            return EOpsTransactionType.SALE;
        } else if (points.containsKey(ETransactionType.AWARD)) {
            return EOpsTransactionType.EARN;
        } else {
            return EOpsTransactionType.BURN;
        }
    }

    private Map<ETransactionType, List<BigDecimal>> getTransactionPoints(List<TransactionHistory> transactionHistories) {
        return transactionHistories
                .stream()
                .filter(Objects::nonNull)
                .flatMap(entity -> {
                    List<TransactionPointWrapper> wrappers = new ArrayList<>();

                    if (entity.getType().equals(ETransactionType.ADJUSTMENT)) {
                        wrappers.add(TransactionPointWrapper.builder()
                                .type(ETransactionType.AWARD)
                                .awardPoint(entity.getAwardPoint())
                                .redeemPoint(entity.getRedeemPoint())
                                .build());

                        wrappers.add(TransactionPointWrapper.builder()
                                .type(ETransactionType.REDEEM)
                                .awardPoint(entity.getAwardPoint())
                                .redeemPoint(entity.getRedeemPoint())
                                .build());

                        wrappers.add(TransactionPointWrapper.builder()
                                .type(ETransactionType.ADJUSTMENT)
                                .build());
                    } else {
                        wrappers.add(TransactionPointWrapper.builder()
                                .type(entity.getType())
                                .awardPoint(entity.getAwardPoint())
                                .redeemPoint(entity.getRedeemPoint())
                                .build());
                    }

                    return wrappers.stream();
                })
                .collect(Collectors.groupingBy(
                        TransactionPointWrapper::getType, Collectors.mapping(wrapper -> {
                            switch (wrapper.getType()) {
                                case AWARD:
                                    return wrapper.getAwardPoint() != null ? wrapper.getAwardPoint() : BigDecimal.ZERO;

                                case REDEEM:
                                    return wrapper.getRedeemPoint() != null ? wrapper.getRedeemPoint() : BigDecimal.ZERO;

                                default:
                                    return BigDecimal.ZERO;
                            }
                        }, Collectors.toList())));
    }

    private Comparator<TransactionHistory> getSortForSearch(Sort.Direction direction) {
        if (Sort.Direction.ASC.equals(direction))
            return (o1, o2) -> o1.getTransactionTime().compareTo(o2.getTransactionTime());
        return (o1, o2) -> o2.getTransactionTime().compareTo(o1.getTransactionTime());
    }

    private Currency getBaseCurrencyFromTransaction(TransactionHistory transactionHistory, Program program, EOpsTransactionType type) {
        Currency baseCurrency = null;
        List<Currency> baseCurrencies = null;

        if (EOpsTransactionType.BURN.equals(type)) {
            Pool pool = null;
            if (transactionHistory.getPoolId() != null) {
                pool = poolRepository.findById(transactionHistory.getPoolId()).orElse(null);
            }

            Currency currency = currencyService.findActive(pool.getCurrencyId());

            baseCurrencies = Optional.ofNullable(currencyRepository.findBaseCurrencyByCurrencyAndBusiness(currency.getId(), program.getBusinessId(), ECommonStatus.ACTIVE))
                    .orElse(Collections.emptyList());
        } else {
            ETransactionType eTransactionType = type == EOpsTransactionType.ADJUSTMENT ? ETransactionType.ADJUSTMENT : ETransactionType.AWARD;

            baseCurrencies = Optional.ofNullable(this.currencyRepository.findByTransactionTypeAndTransactionId(eTransactionType, transactionHistory.getPointTransactionId()))
                    .orElse(Collections.emptyList());
        }

        if (baseCurrencies.size() > 1) {
            throw new BusinessException(ErrorCode.UNSUPPORTED_MULTIPLE_BASE_CURRENCY_RATES,
                    "Unsupported multiple base currency rates",
                    transactionHistory,
                    new Object[]{transactionHistory.getPointTransactionId()});
        }

        baseCurrency = baseCurrencies.get(0);

        return baseCurrency;
    }

    @SuppressWarnings("unchecked")
    private String convertSourceToResponse(String source, Object payload) {
        Map<String, Object> hashMapPayload = (Map<String, Object>) payload;
        Map<String, Object> transaction = (Map<String, Object>) hashMapPayload.get("transaction");
        if (checkLogSourceConfigPram.SOURCE_API_WRAPPER_VALUE.equals(source)) {
            return checkLogSourceConfigPram.SOURCE_MOBILE_DEFAULT_STORE_CODES.contains(transaction.get("store_id")) ?
                    checkLogSourceConfigPram.SOURCE_MOBILE_VALUE : "GD Online: Store - " + transaction.get("store_id");
        } else {
            return source;
        }
    }

    @Override
    public APIResponse<?> confirmTransaction(TransactionConfirmReq txnConfirmReq) {
        // TODO Auto-generated method stub
        List<TransactionHistory> transactionHistories = transactionHistoryService.getByTnxPointIds(Collections.singletonList(txnConfirmReq.getTransactionId()));

        if (transactionHistories.size() == 0)
            throw new OpsBusinessException(OpsErrorCode.TRANSACTION_NOT_FOUND, "Transaction not found", null);

        TransactionHistory transaction = transactionHistories.get(0);


        Business business = businessRepository.findById(transaction.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));

        if (!business.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, "Business not active", null);

        Program program = programRepository.findById(transaction.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        if (!program.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, "Program not active", null);

        ConfirmTransactionFeignReq req = ConfirmTransactionFeignReq.builder()
                .customerIdentifier(CustomerFeignReq.builder()
                        .id(transaction.getMemberProductAccountCode())
                        .idType(transaction.getMemberProductAccountType())
                        .build())
                .txnRefNo(transaction.getPointTransactionId())
                .programId(program.getCode())
                .businessId(business.getCode())
                .transactionStatus(txnConfirmReq.getTxnStatus())
                .build();
        return oneloyaltyServiceFeignClient.confirmTransaction(req);
    }

    @Override
    public TransactionBatchRequestDetailRes getTransactionBatchRequestDetail(Long id) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(id);
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Program program = programService.findByIdAndBusinessId(batchRequest.getProgramId(), business.getId());

        return TransactionBatchRequestDetailRes
                .builder()
                .batchNo(batchRequest.getId())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .batchRequestType(batchRequest.getType())
                .transactionBatchType(batchRequest.getTransactionBatchType())
                .batchName(batchRequest.getName())
                .campaignCode(batchRequest.getCampaignCode())
                .referenceCode(batchRequest.getReferenceCode())
                .description(batchRequest.getDescription())
                .isReplaceDes(batchRequest.getIsReplaceDes())
                .reason(batchRequest.getRejectReason())
                .smsTemplate(batchRequest.getSmsTemplate())
                .genInvoiceNoMethod(batchRequest.getGenInvoiceNoMethod())
                .genInvoiceNoCharacterSet(batchRequest.getGenInvoiceNoCharacterSet())
                .genInvoiceNoPattern(batchRequest.getGenInvoiceNoPattern())
                .genTransactionTimeMethod(batchRequest.getGenTransactionTimeMethod())
                .genTransactionTimeValue(batchRequest.getGenTransactionTimeValue())
                .generationTime(EBatchRequestProcessStatus.COMPLETED.equals(batchRequest.getProcessStatus())
                        ? batchRequest.getUpdatedAt() : null)
                .createdBy(batchRequest.getCreatedBy())
                .createdAt(batchRequest.getCreatedAt())
                .approvedBy(batchRequest.getApprovedBy())
                .approvedAt(batchRequest.getApprovedAt())
                .approvalStatus(batchRequest.getApprovalStatus())
                .processStatus(batchRequest.getProcessStatus())
                .totalTransaction(batchRequest.getTotalRequests())
                .totalMember(batchRequest.getTotalMember())
                .totalGMV(batchRequest.getTotalGMV())
                .totalGrossAmount(batchRequest.getTotalGrossAmount())
                .totalAwardPoint(batchRequest.getTotalAwardPoint())
                .totalRedeemPoint(batchRequest.getTotalRedeemPoint())
                .madeReason(batchRequest.getMadeReason())
                .requestType(ERequestType.CREATE) // Only create
                .build();
    }

    @Builder
    @Getter
    private static class TransactionInfo {
        private BigDecimal gmv;
        private BigDecimal grossAmount;
        private BigDecimal nettAmount;
        private BigDecimal totalAwardPoint;
        private BigDecimal totalRedeemPoint;
        private BigDecimal balanceBefore;
        private BigDecimal balanceAfter;
        private BigDecimal awardBeforeLimit;
        private Date awardRetentionTime;
        private String schemeCodes;
        private String poolCodes;
    }

    @Override
    public Page<TransactionRequestRes> getInReviewTransactionRequests(Long batchRequestId, ERequestProcessStatus processStatus, Pageable pageable) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(batchRequestId);
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Program program = programService.findByIdAndBusinessId(batchRequest.getProgramId(), business.getId());

        SpecificationBuilder<TransactionRequest> specificationBuilder = new SpecificationBuilder<>();
        specificationBuilder.add(new SearchCriteria("batchRequestId", batchRequestId, SearchOperation.EQUAL));
        if (Objects.nonNull(processStatus)) {
            specificationBuilder.add(new SearchCriteria("processStatus", processStatus, SearchOperation.EQUAL));
        }

        Page<TransactionRequest> transactionRequestPage = transactionRequestService.find(specificationBuilder, pageable);

        // Get memberMap, storeMap, chainMap, corporationMap
        Set<Long> memberIds = new HashSet<>();
        Set<String> terminalCodes = new HashSet<>();
        Set<Integer> storeIds = new HashSet<>();
        Set<Integer> chainIds = new HashSet<>();
        Set<Integer> corporationIds = new HashSet<>();
        List<String> txnRefNos = new ArrayList<>();
        Set<Integer> schemeIdSet = new HashSet<>();
        Set<Integer> poolIdSet = new HashSet<>();

        List<Pos> terminals;
        Map<String, Pos> terminalMapByCode = new HashMap<>();
        Map<Long, String> memberCodeMapById;
        Map<Integer, ShortEntityRes> storeMapById;
        Map<Integer, ShortEntityRes> chainMapById;
        Map<Integer, ShortEntityRes> corporationMapById;

        for (TransactionRequest transactionRequest : transactionRequestPage.getContent()) {
            if (Objects.nonNull(transactionRequest.getMemberId())) {
                memberIds.add(transactionRequest.getMemberId());
            }
            if (Objects.nonNull(transactionRequest.getTerminalCode())) {
                terminalCodes.add(transactionRequest.getTerminalCode());
            }
            if (Objects.nonNull(transactionRequest.getTxnRefNo())) {
                txnRefNos.add(transactionRequest.getTxnRefNo());
            }
        }

        memberCodeMapById = memberService.findByIdIn(memberIds)
                .stream().collect(Collectors.toMap(Member::getId, Member::getMemberCode));

        terminals = posService.findByCodeInAndBusinessId(terminalCodes, batchRequest.getBusinessId());

        for (Pos terminal : terminals) {
            terminalMapByCode.put(terminal.getCode(), terminal);
            storeIds.add(terminal.getStoreId());
            chainIds.add(terminal.getChainId());
            corporationIds.add(terminal.getCorporationId());
        }

        storeMapById = storeService.findByIdIn(storeIds)
                .stream().collect(Collectors.toMap(Store::getId, s -> new ShortEntityRes(s.getId(), s.getName(), s.getCode())));

        chainMapById = chainService.findByIdIn(chainIds)
                .stream().collect(Collectors.toMap(Chain::getId, c -> new ShortEntityRes(c.getId(), c.getName(), c.getCode())));

        corporationMapById = corporationService.findByIdIn(corporationIds)
                .stream().collect(Collectors.toMap(Corporation::getId, c -> new ShortEntityRes(c.getId(), c.getName(), c.getCode())));

        if (ETransactionBatchType.REVERT_FULL.equals(batchRequest.getTransactionBatchType()) || ETransactionBatchType.REVERT_POINT.equals(batchRequest.getTransactionBatchType())) {
            List<TransactionHistory> txnHistories = transactionHistoryService.getByTnxPointIds(txnRefNos);

            txnHistories.forEach(item -> {
                if (Objects.nonNull(item.getSchemeId())) {
                    schemeIdSet.add(item.getSchemeId());
                }
                if (Objects.nonNull(item.getPoolId())) {
                    poolIdSet.add(item.getPoolId());
                }
            });

            Map<Integer, String> schemeCodeMapById = schemeService
                    .findByProgramIdAndListIds(batchRequest.getProgramId(), new ArrayList<>(schemeIdSet))
                    .stream().collect(Collectors.toMap(
                            Scheme::getId,
                            Scheme::getCode
                    ));

            Map<Integer, String> poolCodeMapById = poolService
                    .findByIdIn(new ArrayList<>(poolIdSet))
                    .stream().collect(Collectors.toMap(
                            Pool::getId,
                            Pool::getCode
                    ));

            Map<String, List<TransactionHistory>> txnHistoryMapByTxnRefNo = txnHistories
                    .stream().collect(Collectors.groupingBy(
                                    TransactionHistory::getPointTransactionId,
                                    Collectors.toList()
                            )
                    );

            return transactionRequestPage.map(request -> buildTransactionForRevertFull(
                    batchRequest,
                    request,
                    txnHistoryMapByTxnRefNo,
                    schemeCodeMapById,
                    poolCodeMapById,
                    corporationMapById,
                    chainMapById,
                    storeMapById,
                    terminalMapByCode,
                    memberCodeMapById,
                    program,
                    business

            ));
        } else {
            return transactionRequestPage.map(request -> buildTransactionInReview(
                    batchRequest,
                    request,
                    corporationMapById,
                    chainMapById,
                    storeMapById,
                    terminalMapByCode,
                    memberCodeMapById
            ));
        }
    }

    private TransactionRequestRes buildTransactionInReview(TransactionBatchRequest batchRequest,
                                                           TransactionRequest request,
                                                           Map<Integer, ShortEntityRes> corporationMapById,
                                                           Map<Integer, ShortEntityRes> chainMapById,
                                                           Map<Integer, ShortEntityRes> storeMapById,
                                                           Map<String, Pos> terminalMapByCode,
                                                           Map<Long, String> memberCodeMapById) {
        Pos terminal = terminalMapByCode.get(request.getTerminalCode());
        ShortEntityRes corporation = null;
        ShortEntityRes chain = null;
        ShortEntityRes store = null;
        ShortEntityRes terminalResult = null;

        if (Objects.nonNull(terminal)) {
            corporation = corporationMapById.get(terminal.getCorporationId());
            chain = chainMapById.get(terminal.getChainId());
            store = storeMapById.get(terminal.getStoreId());
            terminalResult = new ShortEntityRes(terminal.getId(), terminal.getName(), terminal.getCode());
        }

        EOpsTransactionType transactionType = getTransactionType(batchRequest.getTransactionBatchType(), Collections.emptyList(), request);

        BigDecimal awardPoint = null;
        BigDecimal redeemPoint = null;
        if (ETransactionBatchType.ADJUST.equals(batchRequest.getTransactionBatchType())) {
            if (EAdjustmentType.AWARD.equals(request.getAdjustType())) {
                awardPoint = request.getAdjustPoint();
            } else {
                redeemPoint = request.getAdjustPoint();
            }
        } else {
            redeemPoint = request.getRedeemPoint();
        }

        return TransactionRequestRes
                .builder()
                .txnRefNo(request.getTxnRefNo())
                .invoiceNo(request.getInvoiceNo())
                .originalInvoiceNo(request.getOriginalInvoiceNo())
                .corporation(corporation)
                .chain(chain)
                .store(store)
                .terminal(terminalResult)
                .memberId(request.getMemberId())
                .memberCode(memberCodeMapById.get(request.getMemberId()))
                .productAccountType(nullSafer(EOpsIdType.lookup(request.getIdType()), EOpsIdType::getValue))
                .productAccountCode(request.getIdNo())
                .transactionTime(request.getTransactionTime())
                .transactionType(nullSafer(transactionType, EOpsTransactionType::getValue))
                .gmv(request.getGmv())
                .grossAmount(request.getGrossAmount())
                .awardPoint(awardPoint)
                .redeemPoint(redeemPoint)
                .poolCode(request.getPoolCode())
                .currencyCode(request.getCurrencyCode())
                .description(request.getDescription())
                .reasonCode(request.getReasonCode())
                .channel(OPSConstant.DEFAULT_TRANSACTION_CHANNEL)
                .serviceCode(OPSConstant.DEFAULT_TRANSACTION_SERVICE_CODE)
                .processStatus(request.getProcessStatus())
                .build();
    }

    private TransactionRequestRes buildTransactionForRevertFull(TransactionBatchRequest batchRequest,
                                                                TransactionRequest request,
                                                                Map<String, List<TransactionHistory>> txnHistoryMapByTxnRefNo,
                                                                Map<Integer, String> schemeCodeMapById,
                                                                Map<Integer, String> poolCodeMapById,
                                                                Map<Integer, ShortEntityRes> corporationMapById,
                                                                Map<Integer, ShortEntityRes> chainMapById,
                                                                Map<Integer, ShortEntityRes> storeMapById,
                                                                Map<String, Pos> terminalMapByCode,
                                                                Map<Long, String> memberCodeMapById,
                                                                Program program,
                                                                Business business) {
        Pos terminal = terminalMapByCode.get(request.getTerminalCode());
        List<TransactionHistory> txnHistories = txnHistoryMapByTxnRefNo.get(request.getTxnRefNo());

        ShortEntityRes corporation = null;
        ShortEntityRes chain = null;
        ShortEntityRes store = null;
        ShortEntityRes terminalResult = null;

        if (Objects.nonNull(terminal)) {
            corporation = corporationMapById.get(terminal.getCorporationId());
            chain = chainMapById.get(terminal.getChainId());
            store = storeMapById.get(terminal.getStoreId());
            terminalResult = new ShortEntityRes(terminal.getId(), terminal.getName(), terminal.getCode());
        }

        EOpsTransactionType transactionType = getTransactionType(batchRequest.getTransactionBatchType(), txnHistories, request);

        TransactionRequestRes res = TransactionRequestRes
                .builder()
                .txnRefNo(request.getTxnRefNo())
                .invoiceNo(request.getInvoiceNo())
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .originalInvoiceNo(request.getOriginalInvoiceNo())
                .corporation(corporation)
                .chain(chain)
                .store(store)
                .terminal(terminalResult)
                .memberId(request.getMemberId())
                .memberCode(memberCodeMapById.get(request.getMemberId()))
                .productAccountType(nullSafer(EOpsIdType.lookup(request.getIdType()), EOpsIdType::getValue))
                .productAccountCode(request.getIdNo())
                .transactionType(nullSafer(transactionType, EOpsTransactionType::getValue))
                .currencyCode(request.getCurrencyCode())
                .processStatus(request.getProcessStatus())
                .transactionTime(request.getTransactionTime())
                .createdAt(request.getCreatedAt())
                .reasonCode(request.getReasonCode())
                .errorCode(request.getErrorCode())
                .errorMessage(request.getErrorMessage())
                .smsErrorCode(request.getSmsErrorCode())
                .smsErrorMessage(request.getSmsErrorMessage())
                .build();

        if (CollectionUtils.isNotEmpty(txnHistories)) {
            TransactionHistory txnHistory = txnHistories.get(0);
            TransactionInfo txnInfo = getTransactionInfo(txnHistories, schemeCodeMapById, poolCodeMapById);

            res.setTransactionTime(txnHistory.getTransactionTime());
            res.setDescription(txnHistory.getDescription());
            res.setAwardPoint(txnInfo.getTotalAwardPoint());
            res.setRedeemPoint(txnInfo.getTotalRedeemPoint());
            res.setGmv(txnInfo.getGmv());
            res.setGrossAmount(txnInfo.getGrossAmount());
            res.setNettAmount(txnInfo.getNettAmount());
            res.setPointBalanceBefore(txnInfo.getBalanceBefore());
            res.setPointBalanceAfter(txnInfo.getBalanceAfter());
            res.setAwardPointBeforeLimit(txnInfo.getAwardBeforeLimit());
            res.setAwardRetentionTime(txnInfo.getAwardRetentionTime());
            res.setSchemeCode(txnInfo.getSchemeCodes());
            res.setPoolCode(txnInfo.getPoolCodes());
            if (Objects.nonNull(txnHistory.getReasonCode())) {
                ReasonCode reasonCode = reasonCodeService.findByCode(business.getId(), program.getId(), txnHistory.getReasonCode());
                res.setReasonCode(txnHistory.getReasonCode());
                res.setReasonName(reasonCode.getName());
            }
            res.setChannel(txnHistory.getChannel());
            res.setServiceCode(txnHistory.getServiceCode());
            res.setCancellation(txnHistory.getCancellation() != null);
            res.setCancellationTime(txnHistory.getCancellationTime());
            res.setCancellationType(txnHistory.getCancellationType());
        }

        return res;
    }

    private TransactionInfo getTransactionInfo(List<TransactionHistory> txnHistories, Map<Integer, String> schemeCodeMapById, Map<Integer, String> poolCodeMapById) {
        Set<Integer> schemeIds = new HashSet<>();
        Set<Integer> poolIds = new HashSet<>();
        Date awardRetentionTime = null;
        BigDecimal totalAwardPoint = BigDecimal.ZERO;
        BigDecimal totalRedeemPoint = BigDecimal.ZERO;
        BigDecimal awardBeforeLimit = BigDecimal.ZERO;
        BigDecimal balanceBefore = BigDecimal.ZERO;
        BigDecimal balanceAfter = BigDecimal.ZERO;
        BigDecimal gmv = BigDecimal.ZERO;
        BigDecimal grossAmount = BigDecimal.ZERO;
        BigDecimal nettAmount = BigDecimal.ZERO;
        String schemeCodes = Strings.EMPTY;
        String poolCodes = Strings.EMPTY;

        if (CollectionUtils.isNotEmpty(txnHistories)) {
            for (TransactionHistory item : txnHistories) {
                if (Objects.nonNull(item.getSchemeId())) {
                    schemeIds.add(item.getSchemeId());
                }
                if (Objects.nonNull(item.getPoolId())) {
                    poolIds.add(item.getPoolId());
                }
                if (Objects.nonNull(item.getAwardRetentionTime())) {
                    awardRetentionTime = item.getAwardRetentionTime();
                }
                if (Objects.nonNull(item.getAwardPointBeforeLimit())) {
                    awardBeforeLimit = awardBeforeLimit.add(item.getAwardPointBeforeLimit());
                }
                if (Objects.nonNull(item.getAwardPoint())) {
                    totalAwardPoint = totalAwardPoint.add(item.getAwardPoint());
                }
                if (Objects.nonNull(item.getRedeemPoint())) {
                    totalRedeemPoint = totalRedeemPoint.add(item.getRedeemPoint());
                }
                if (Objects.nonNull(item.getGmv())) {
                    gmv = item.getGmv();
                }
                if (Objects.nonNull(item.getGrossAmount())) {
                    grossAmount = item.getGrossAmount();
                }
                if (Objects.nonNull(item.getNettAmount())) {
                    nettAmount = item.getNettAmount();
                }
            }
            TransactionHistory max = txnHistories
                    .stream().filter(item -> Objects.nonNull(item.getBalanceAfter()))
                    .max(Comparator.comparing(TransactionHistory::getId))
                    .orElse(null);

            TransactionHistory min = txnHistories
                    .stream().filter(item -> Objects.nonNull(item.getBalanceBefore()))
                    .min(Comparator.comparing(TransactionHistory::getId))
                    .orElse(null);

            balanceBefore = Objects.nonNull(min) ? min.getBalanceBefore() : BigDecimal.ZERO;

            balanceAfter = Objects.nonNull(max) ? max.getBalanceAfter() : BigDecimal.ZERO;

            schemeCodes = schemeIds.stream()
                    .map(schemeCodeMapById::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(", "));

            poolCodes = poolIds.stream()
                    .map(poolCodeMapById::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(", "));
        }

        return TransactionInfo.builder()
                .gmv(gmv)
                .grossAmount(grossAmount)
                .nettAmount(nettAmount)
                .awardBeforeLimit(awardBeforeLimit)
                .awardRetentionTime(awardRetentionTime)
                .totalAwardPoint(totalAwardPoint)
                .totalRedeemPoint(totalRedeemPoint)
                .balanceBefore(balanceBefore)
                .balanceAfter(balanceAfter)
                .schemeCodes(schemeCodes)
                .poolCodes(poolCodes)
                .build();
    }

    @Override
    public Page<TransactionRequestRes> getAvailableTransactionRequests(Long batchRequestId, ERequestProcessStatus processStatus, Pageable pageable) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        TransactionBatchRequest transactionBatchRequest = transactionBatchRequestService.findByIdAndBusinessId(batchRequestId, business.getId());
        Program program = programService.findByIdAndBusinessId(transactionBatchRequest.getProgramId(), business.getId());

        SpecificationBuilder<TransactionRequest> specificationBuilder = new SpecificationBuilder<>();

        specificationBuilder.add(new SearchCriteria("batchRequestId", batchRequestId, SearchOperation.EQUAL));

        if (processStatus != null) {
            specificationBuilder.add(new SearchCriteria("processStatus", processStatus, SearchOperation.EQUAL));
        }

        PageRequest pageRequest = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
                Sort.by(Sort.Direction.ASC, "processStatus"));
        Page<TransactionRequest> transactionRequestPage = transactionRequestService.find(specificationBuilder, pageRequest);

        // Get memberMap, storeMap, chainMap, corporationMap
        Set<Long> memberIds = new HashSet<>();
        Set<String> terminalCodes = new HashSet<>();
        Set<Integer> storeIds = new HashSet<>();
        Set<Integer> chainIds = new HashSet<>();
        Set<Integer> corporationIds = new HashSet<>();
        List<String> txnRefNos = new ArrayList<>();
        Set<Integer> schemeIdSet = new HashSet<>();
        Set<Integer> poolIdSet = new HashSet<>();

        List<Pos> terminals;
        Map<String, Pos> terminalMap = new HashMap<>();
        Map<Long, String> memberCodeMap;
        Map<Integer, ShortEntityRes> storeMap;
        Map<Integer, ShortEntityRes> chainMap;
        Map<Integer, ShortEntityRes> corporationMap;

        for (TransactionRequest transactionRequest : transactionRequestPage.getContent()) {
            memberIds.add(transactionRequest.getMemberId());
            terminalCodes.add(transactionRequest.getTerminalCode());
            txnRefNos.add(transactionRequest.getTxnRefNo());
        }

        memberCodeMap = memberService.findByIdIn(memberIds)
                .stream().collect(Collectors.toMap(Member::getId, Member::getMemberCode));

        terminals = posService.findByCodeInAndBusinessId(terminalCodes, business.getId());

        for (Pos terminal : terminals) {
            terminalMap.put(terminal.getCode(), terminal);
            storeIds.add(terminal.getStoreId());
            chainIds.add(terminal.getChainId());
            corporationIds.add(terminal.getCorporationId());
        }

        storeMap = storeService.findByIdIn(storeIds)
                .stream().collect(Collectors.toMap(Store::getId, s -> new ShortEntityRes(s.getId(), s.getName(), s.getCode())));

        chainMap = chainService.findByIdIn(chainIds)
                .stream().collect(Collectors.toMap(Chain::getId, c -> new ShortEntityRes(c.getId(), c.getName(), c.getCode())));

        corporationMap = corporationService.findByIdIn(corporationIds)
                .stream().collect(Collectors.toMap(Corporation::getId, c -> new ShortEntityRes(c.getId(), c.getName(), c.getCode())));
        List<TransactionHistory> txnHistories = transactionHistoryService.getByTnxPointIds(txnRefNos);

        Map<String, List<TransactionHistory>> transactionHistoryMap = txnHistories.stream().collect(
                Collectors.groupingBy(
                        TransactionHistory::getPointTransactionId,
                        Collectors.toList()
                )
        );

        txnHistories.forEach(item -> {
            if (Objects.nonNull(item.getSchemeId())) {
                schemeIdSet.add(item.getSchemeId());
            }
            if (Objects.nonNull(item.getPoolId())) {
                poolIdSet.add(item.getPoolId());
            }
        });

        Map<Integer, String> schemeCodeMapById = schemeService
                .findByProgramIdAndListIds(transactionBatchRequest.getProgramId(), new ArrayList<>(schemeIdSet))
                .stream().collect(Collectors.toMap(
                        Scheme::getId,
                        Scheme::getCode
                ));

        Map<Integer, String> poolCodeMapById = poolService
                .findByIdIn(new ArrayList<>(poolIdSet))
                .stream().collect(Collectors.toMap(
                        Pool::getId,
                        Pool::getCode
                ));

        if (ETransactionBatchType.REVERT_FULL.equals(transactionBatchRequest.getTransactionBatchType())) {
            return transactionRequestPage.map(request -> buildTransactionForRevertFull(
                            transactionBatchRequest,
                            request,
                            transactionHistoryMap,
                            schemeCodeMapById,
                            poolCodeMapById,
                            corporationMap,
                            chainMap,
                            storeMap,
                            terminalMap,
                            memberCodeMap,
                            program, business
                    )
            );
        } else {
            return transactionRequestPage
                    .map(request -> {
                        Pos terminal = terminalMap.get(request.getTerminalCode());
                        List<TransactionHistory> transactionHistoryList = transactionHistoryMap.get(request.getTxnRefNo());
                        return buildTransactionAvailable(
                                transactionBatchRequest,
                                request,
                                transactionHistoryList,
                                schemeCodeMapById,
                                poolCodeMapById,
                                corporationMap,
                                chainMap,
                                storeMap,
                                terminal,
                                memberCodeMap,
                                program,
                                business
                        );
                    });
        }
    }

    @Override
    public String randomInvoiceNumber(String pattern, ECharacterSet type) {
        boolean letters = true;
        boolean numbers = true;
        switch (type) {
            case NUMBER: {
                letters = false;
                break;
            }
            case ALPHABETICAL: {
                numbers = false;
                break;
            }
        }
        if (pattern.length() > MAX_PATTERN_LENGTH || pattern.length() < MIN_PATTERN_LENGTH) {
            throw new BusinessException(ErrorCode.INVALID_LENGTH_PATTERN, "Invalid length invoice no!", null);
        }

        StringBuilder result = new StringBuilder();
        for (char character : pattern.toCharArray()) {
            if (RANDOM_CHAR == character) {
                result.append(RandomStringUtils.random(1, letters, numbers));
            } else {
                result.append(character);
            }
        }
        return result.toString();
    }

    @Override
    public MemberTransactionRes findMemberTransaction(Integer businessId, Integer programId, EOpsIdType idType, String idNo) {
        Assert.assertTrue(Objects.isNull(idType), ErrorCode.ID_TYPE_NOT_VALID);

        businessService.findActive(businessId);
        programService.findActive(programId);

        CustomerIdentify customerIdentify = new CustomerIdentify(idNo, idType.getMapping());
        Member member = memberService.findActive(customerIdentify, programId);

        MemberStatus memberStatus = memberStatusService.findByProgramIdAndCode(programId, member.getStatus()).orElse(null);

        Assert.assertTrue(Objects.isNull(member), ErrorCode.MEMBER_NOT_FOUND);

        return MemberTransactionRes
                .builder()
                .memberId(member.getId())
                .memberName(member.getFullName())
                .memberCode(member.getMemberCode())
                .phoneNo(member.getPhoneNo())
                .dob(member.getDob())
                .gender(member.getGender())
                .identifyType(nullSafer(member.getIdentifyType(), EIdentifyType::getCode))
                .identifyNo(member.getIdentifyNo())
                .address(member.getAddress())
                .email(member.getEmail())
                .status(Objects.nonNull(memberStatus) ? new DropdownRes(memberStatus.getId(), memberStatus.getCode(), memberStatus.getViName(), memberStatus.getEnName()) : null)
                .build();
    }

    @Override
    public List<MemberTransactionRes> findMemberTransaction(Integer programId, EOpsIdType idType, String idNo) {
        Assert.assertTrue(Objects.isNull(idType), ErrorCode.ID_TYPE_NOT_VALID);

        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        programService.findByIdAndBusinessId(programId, business.getId());

        CustomerIdentify customerIdentify = new CustomerIdentify(idNo, idType.getMapping());
        List<Member> member = Collections.singletonList(memberService.find(customerIdentify, programId));

        Set<String> memberStatusCodes = member.stream().map(Member::getStatus).collect(Collectors.toSet());

        Map<String, MemberStatus> memberStatusMap = memberStatusService.getMapByCodes(programId, memberStatusCodes);

        Assert.assertTrue(CollectionUtils.isEmpty(member), ErrorCode.MEMBER_NOT_FOUND);

        return member.stream().map(m -> {
            MemberStatus memberStatus = memberStatusMap.get(m.getStatus());
            return MemberTransactionRes
                    .builder()
                    .memberId(m.getId())
                    .memberCode(m.getMemberCode())
                    .partnerCustomerId(m.getPartnerCustomerId())
                    .memberName(m.getFullName())
                    .firstName(m.getFirstName())
                    .middleName(m.getMidName())
                    .lastName(m.getLastName())
                    .phoneNo(m.getPhoneNo())
                    .dob(m.getDob())
                    .gender(m.getGender())
                    .identifyType(nullSafer(m.getIdentifyType(), EIdentifyType::getCode))
                    .identifyNo(m.getIdentifyNo())
                    .status(Objects.nonNull(memberStatus) ? new DropdownRes(memberStatus.getId(), memberStatus.getCode(), memberStatus.getViName(), memberStatus.getEnName()) : null)
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Long createTransactionBatchRequestForMember(CreateTransactionMemberRequestReq req) {
        validate(req);

        TransactionBatchRequest batchRequest = buildTransactionBatchRequest(req);

        batchRequest = transactionBatchRequestService.save(batchRequest);

        TransactionRequest transactionRequest = buildTransactionRequest(req, batchRequest);

        transactionRequestService.save(transactionRequest);

        if (req.isSendEmail()) {
            this.sendEmail(batchRequest);
        }
        return batchRequest.getId();
    }

    private void validate(CreateTransactionMemberRequestReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        req.setBusinessId(business.getId());
        programService.findByIdAndBusinessId(req.getProgramId(), business.getId());
        Corporation corporation = corporationService.findActive(business.getId(), req.getCorporationCode());
        Pos terminal = posService.findActiveByCorporationIdAndCode(corporation.getId(), req.getTerminalCode());
        Store store = storeService.findActive(terminal.getStoreId());
        chainService.findActive(terminal.getChainId());

        req.setStoreCode(store.getCode());

        validateMember(req.getIdType().getMapping(), req.getIdNo(), req.getProgramId());

        if (Objects.nonNull(req.getAttributeMap())) {
            validateAttributeMap(req.getAttributeMap(), req.getProgramId());
        }

        if (EGenerationInvoiceNoMethod.CUSTOM.equals(req.getGenInvoiceNoMethod())) {
            validateGenInvoiceNo(req.getGenInvoiceNoPattern());
        }

        Set<String> currencyCodeSet = getCurrencyCodeSetForTransaction(req.getBusinessId());

        String currencyCode = null;

        switch (req.getTransactionBatchType()) {
            case ADJUST:
                poolService.findActive(req.getAdjustData().getPoolCode(), req.getProgramId());
                reasonCodeService.findActive(req.getAdjustData().getReasonCode(), req.getProgramId());
                break;
            case EARN:
                currencyCode = req.getEarnData().getCurrencyCode();
                break;
            case BURN:
                currencyCode = req.getBurnData().getCurrencyCode();
                break;
            case SALE:
                currencyCode = req.getSaleData().getCurrencyCode();
                break;
        }

        Assert.assertTrue(
                Objects.nonNull(currencyCode) && !currencyCodeSet.contains(currencyCode),
                ErrorCode.CURRENCY_NOT_FOUND
        );
    }

    private Set<String> getCurrencyCodeSetForTransaction(Integer businessId) {

        return currencyRepository
                .findByBusinessId(businessId)
                .stream()
                .filter(currency -> ECommonStatus.ACTIVE.equals(currency.getStatus())
                        && Objects.nonNull(currency.getCode()))
                .map(Currency::getCode)
                .collect(Collectors.toSet());
    }

    private void validateMember(EIdType idType, String idNo, Integer programId) {
        List<EIdType> idTypes = programProductService.getActiveIdTypes(programId);

        Assert.assertTrue(!idTypes.contains(idType), ErrorCode.ID_TYPE_NOT_VALID);

        CustomerIdentify identify = new CustomerIdentify(idNo, idType);
        memberService.find(identify, programId);
    }

    private void validateGenInvoiceNo(String genInvoiceNoPattern) {
        char[] chars = genInvoiceNoPattern.toCharArray();
        int numberOfRandomCharacters = 0;

        Assert.assertTrue(
                chars.length < MIN_PATTERN_LENGTH || chars.length > MAX_PATTERN_LENGTH,
                ErrorCode.INVALID_LENGTH_PATTERN
        );

        for (char character : chars) {
            if (character == RANDOM_CHAR) {
                numberOfRandomCharacters++;
            } else {
                Assert.assertTrue(Character.isWhitespace(character), ErrorCode.INVALID_INVOICE_NO_PATTERN);
            }
        }

        Assert.assertTrue(numberOfRandomCharacters == 0, ErrorCode.INVALID_INVOICE_NO_PATTERN);
    }

    private void validateAttributeMap(Map<String, String> attributeMap, Integer programId) {
        Map<String, ProgramTransactionAttribute> tranAttrMap = programTransactionAttributeService
                .listAllByProgramId(programId)
                .stream()
                .filter(item -> Objects.nonNull(item.getAttribute()))
                .collect(Collectors.toMap(
                        ProgramTransactionAttribute::getAttribute,
                        programTransactionAttribute -> programTransactionAttribute,
                        (k1, k2) -> k2));

        attributeMap.entrySet().stream().forEach(entry -> {
            ProgramTransactionAttribute tranAttr = tranAttrMap.get(entry.getKey());
            if (Objects.isNull(tranAttr)) {
                throw new BusinessException(ErrorCode.TRANSACTION_ATTRIBUTE_NOT_FOUND);
            } else {
                if (Objects.isNull(entry.getValue())) {
                    throw new BusinessException(ErrorCode.PROGRAM_TRANSACTION_ATTRIBUTE_NOT_VALID);
                } else {
                    if (ETransactionAttributeDataTypeDisplay.NUMBER.getValue().equals(tranAttr.getDataTypeDisplay())) {
                        Long.valueOf(entry.getValue());
                    } else if (ETransactionAttributeDataTypeDisplay.DATE.getValue().equals(tranAttr.getDataTypeDisplay())) {
                        String format = "dd/MM/yyyy";
                        DateFormat sdf = new SimpleDateFormat(format);
                        try {
                            sdf.parse(entry.getValue());
                        } catch (ParseException e) {
                            throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID);
                        }
                    } else if (ETransactionAttributeDataTypeDisplay.DATE_TIME.getValue().equals(tranAttr.getDataTypeDisplay())) {
                        Long result = Long.parseLong(entry.getValue());
                        entry.setValue(DateTimes.formatDateTime(new Date(result * 1000)));
                    } else if (ETransactionAttributeDataTypeDisplay.TEXT.getValue().equals(tranAttr.getDataTypeDisplay())) {
                        if (StringUtils.isNotBlank(tranAttr.getValueValidationPattern())) {
                            Pattern pattern = Pattern.compile(tranAttr.getValueValidationPattern());
                            if (!pattern.matcher(entry.getValue()).matches()) {
                                throw new BusinessException(
                                        ErrorCode.INVALID_ATTRIBUTE_VALIDATION_PATTERN,
                                        null,
                                        null,
                                        new Object[]{tranAttr.getAttribute(), tranAttr.getValueValidationPattern()}
                                );
                            }
                        }
                    }
                }
            }
            verifyMasterData(tranAttr, entry.getValue());
        });
    }

    private void verifyMasterData(ProgramTransactionAttribute programTransactionAttribute, String value) {
        if ((EBoolean.YES.equals(programTransactionAttribute.getHavingMasterData()) && EBoolean.YES.equals(programTransactionAttribute.getEnableValidateMasterData()))
                || ETransactionAttributeDataTypeDisplay.COMBOBOX.getValue().equals(programTransactionAttribute.getDataTypeDisplay())) {
            if (!attributeMasterDataRepository.existsByAttributeCodeAndValue(programTransactionAttribute.getAttribute(), value)) {
                throw new BusinessException(
                        ErrorCode.INVALID_ATTRIBUTE_MASTER_DATA,
                        null,
                        null,
                        new Object[]{programTransactionAttribute.getAttribute(), programTransactionAttribute.getValueValidationPattern()}
                );
            }
        }
    }

    private TransactionBatchRequest buildTransactionBatchRequest(CreateTransactionMemberRequestReq req) {
        TransactionBatchRequest batchRequest = new TransactionBatchRequest();
        batchRequest.setBusinessId(req.getBusinessId());
        batchRequest.setProgramId(req.getProgramId());
        batchRequest.setType(EBatchRequestType.MEMBER);
        batchRequest.setName(req.getBatchName());
        batchRequest.setCampaignCode(req.getCampaignCode());
        batchRequest.setReferenceCode(req.getReferenceCode());
        batchRequest.setDescription(req.getDescription());
        batchRequest.setIsReplaceDes(req.getIsReplaceDes());
        batchRequest.setGenInvoiceNoMethod(req.getGenInvoiceNoMethod());
        batchRequest.setGenInvoiceNoCharacterSet(req.getGenInvoiceNoCharacterSet());
        batchRequest.setGenInvoiceNoPattern(req.getGenInvoiceNoPattern());
        batchRequest.setGenTransactionTimeMethod(req.getGenTransactionTimeMethod());
        batchRequest.setGenTransactionTimeValue(req.getGenTransactionTimeValue());
        batchRequest.setTransactionBatchType(req.getTransactionBatchType());
        batchRequest.setSmsTemplate(req.getSmsTemplate());
        batchRequest.setTotalRequests(1);
        batchRequest.setPendingRequests(1);
        batchRequest.setTotalMember(1);
        batchRequest.setProcessStatus(EBatchRequestProcessStatus.PENDING);
        batchRequest.setApprovalStatus(EApprovalStatus.PENDING);
        batchRequest.setStatus(ECommonStatus.ACTIVE);
        batchRequest.setAttributeMap(Objects.nonNull(req.getAttributeMap()) ? JsonUtil.writeValueAsString(req.getAttributeMap()) : null);
        batchRequest.setMadeReason(req.getMadeReason());
        return batchRequest;
    }

    private TransactionRequest buildTransactionRequest(CreateTransactionMemberRequestReq req, TransactionBatchRequest batchRequest) {
        BigDecimal totalAwardPoint = BigDecimal.ZERO;
        BigDecimal totalRedeemPoint = BigDecimal.ZERO;

        CustomerIdentify identify = new CustomerIdentify(req.getIdNo(), req.getIdType().getMapping());
        Member member = memberService.find(identify, req.getProgramId());

        TransactionRequest transactionRequest = new TransactionRequest();
        transactionRequest.setBusinessId(req.getBusinessId());
        transactionRequest.setProgramId(req.getProgramId());
        transactionRequest.setBatchRequestId(batchRequest.getId());
        transactionRequest.setMemberId(member.getId());
        transactionRequest.setCorporationCode(req.getCorporationCode());
        transactionRequest.setStoreCode(req.getStoreCode());
        transactionRequest.setTerminalCode(req.getTerminalCode());
        transactionRequest.setIdType(req.getIdType().getMapping());
        transactionRequest.setIdNo(req.getIdNo());
        transactionRequest.setInvoiceNo(
                EGenerationInvoiceNoMethod.MANUAL.equals(req.getGenInvoiceNoMethod())
                        ? req.getInvoiceNo() : null
        );
        transactionRequest.setDescription(req.getDescription());
        transactionRequest.setProcessStatus(ERequestProcessStatus.PENDING);
        transactionRequest.setStatus(ECommonStatus.ACTIVE);

        switch (req.getTransactionBatchType()) {
            case EARN:
                CreateTransactionMemberRequestReq.EarnData earnData = req.getEarnData();
                transactionRequest.setGmv(earnData.getGmv());
                transactionRequest.setGrossAmount(earnData.getGrossAmount());
                transactionRequest.setCurrencyCode(earnData.getCurrencyCode());
                break;
            case BURN:
                CreateTransactionMemberRequestReq.BurnData burnData = req.getBurnData();
                transactionRequest.setRedeemPoint(burnData.getRedeemPoint());
                transactionRequest.setCurrencyCode(burnData.getCurrencyCode());

                totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(transactionRequest.getRedeemPoint())
                        ? transactionRequest.getRedeemPoint() : BigDecimal.ZERO);
                break;
            case SALE:
                CreateTransactionMemberRequestReq.SaleData saleData = req.getSaleData();
                transactionRequest.setGmv(saleData.getGmv());
                transactionRequest.setGrossAmount(saleData.getGrossAmount());
                transactionRequest.setRedeemPoint(saleData.getRedeemPoint());
                transactionRequest.setCurrencyCode(saleData.getCurrencyCode());

                totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(transactionRequest.getRedeemPoint())
                        ? transactionRequest.getRedeemPoint() : BigDecimal.ZERO);
                break;
            case ADJUST:
                CreateTransactionMemberRequestReq.AdjustData adjustData = req.getAdjustData();
                transactionRequest.setAdjustType(adjustData.getAdjustType());
                transactionRequest.setAdjustPoint(adjustData.getAdjustPoint());
                transactionRequest.setPoolCode(adjustData.getPoolCode());
                transactionRequest.setReasonCode(adjustData.getReasonCode());

                if (transactionRequest.getAdjustType() == EAdjustmentType.AWARD) {
                    totalAwardPoint = totalAwardPoint.add(transactionRequest.getAdjustPoint());
                } else {
                    totalRedeemPoint = totalRedeemPoint.add(transactionRequest.getAdjustPoint());
                }
                break;
        }

        batchRequest.setTotalGMV(transactionRequest.getGmv());
        batchRequest.setTotalGrossAmount(transactionRequest.getGrossAmount());
        batchRequest.setTotalAwardPoint(totalAwardPoint);
        batchRequest.setTotalRedeemPoint(totalRedeemPoint);
        transactionBatchRequestService.save(batchRequest);
        return transactionRequest;
    }

    @Override
    public Page<TransactionBatchRequestRes> filterTransaction(Integer programId, Long batchNo, String batchName, EBatchRequestType type,
                                                              EApprovalStatus approvalStatus, String createdBy, Date createdStart, Date createdEnd,
                                                              String approvedBy, Date approvedStart, Date approvedEnd, Pageable pageable) {
        createdEnd = createdEnd != null ? new Date(createdEnd.getTime() + OPSConstant.TIMES_OF_DAY) : null;
        approvedEnd = approvedEnd != null ? new Date(approvedEnd.getTime() + OPSConstant.TIMES_OF_DAY) : null;
        Business businessBatch = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Page<Object[]> result = transactionBatchRequestRepository.filter(pageable, approvalStatus, createdBy, createdStart, createdEnd,
                approvedBy, approvedStart, approvedEnd, businessBatch.getId(), programId, batchNo, batchName, type,
                null, null, null, null, null, false
        );

        List<TransactionBatchRequestRes> transactionBatchRequestRes = result.stream().map(
                batch -> {
                    TransactionBatchRequest transactionBatchRequest = (TransactionBatchRequest) batch[0];
                    Business business = (Business) batch[1];
                    Program program = (Program) batch[2];

                    return TransactionBatchRequestRes.builder()
                            .batchNo(transactionBatchRequest.getId())
                            .batchName(transactionBatchRequest.getName())
                            .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                            .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                            .type(transactionBatchRequest.getType())
                            .createdBy(transactionBatchRequest.getCreatedBy())
                            .createdAt(transactionBatchRequest.getCreatedAt())
                            .approvedBy(transactionBatchRequest.getApprovedBy())
                            .approvedAt(transactionBatchRequest.getApprovedAt())
                            .approvalStatus(transactionBatchRequest.getApprovalStatus())
                            .reason(transactionBatchRequest.getRejectReason())
                            .transactionRequestType(transactionBatchRequest.getTransactionBatchType().getValue())
                            .build();
                }
        ).collect(Collectors.toList());

        return new PageImpl<>(transactionBatchRequestRes, pageable, result.getTotalElements());
    }

    @Override
    public TransactionStatisticRes getTransactionStatisticById(Long requestId) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        TransactionBatchRequest request = transactionBatchRequestRepository.findByIdAndBusinessId(requestId, business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.TRANSACTION_REQUEST_NOT_FOUND));

        TransactionStatisticRes response = new TransactionStatisticRes();

        response.setTotal(request.getTotalRequests());
        response.setTotalSuccess(request.getSuccessRequests());
        response.setTotalFail(request.getFailedRequests());
        response.setTotalPending(request.getPendingRequests());
        response.setAwardSuccessPoint(request.getAwardSuccessPoint());
        response.setRedeemSuccessPoint(request.getRedeemSuccessPoint());
        return response;
    }

    @Override
    public ResourceDTO exportFileInReview(Long batchNo) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(batchNo);
        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findActive(batchRequest.getProgramId());

        List<TransactionRequestRes> requests = new LinkedList<>();

        Pageable pageable = PageRequest.of(0, OPSConstant.SIZE_OF_BATCH, Sort.by("id").ascending());

        Page<TransactionRequestRes> page;

        while (true) {
            page = getInReviewTransactionRequests(batchNo, ERequestProcessStatus.PENDING, pageable);
            requests.addAll(page.getContent());
            if (page.hasNext()) {
                pageable = pageable.next();
            } else {
                break;
            }
        }

        EntryContext context = EntryContext
                .builder()
                .moduleId(OPSConstant.TXN)
                .objectId(OPSConstant.AVAILABLE_TXN)
                .build();

        String ddMMyyyy = DateUtil.formatToStringDDMMYYYY(new Date());

        String fileName = String.format(
                OPSConstant.FILE_NAME_EXPORT_TRANSACTION_BATCH,
                ddMMyyyy,
                batchRequest.getId(),
                business.getCode(),
                program.getCode()
        );

        List<TransactionExportEntry> data = requests
                .stream().map(res -> buildTransactionExportEntry(res, business, program))
                .collect(Collectors.toList());

        return commonExcelService.opsExport(context, fileName, data);
    }

    private TransactionExportEntry buildTransactionExportEntry(TransactionRequestRes res, Business business, Program program) {
        TransactionExportEntry entry = new TransactionExportEntry();
        entry.setTxnRefNo(res.getTxnRefNo());
        entry.setInvoiceNo(res.getInvoiceNo());
        entry.setOriginalInvoiceNo(res.getOriginalInvoiceNo());
        entry.setBusinessCode(business.getCode());
        entry.setProgramCode(program.getCode());
        entry.setCorporationCode(nullSafer(res.getCorporation(), ShortEntityRes::getCode));
        entry.setChainCode(nullSafer(res.getChain(), ShortEntityRes::getCode));
        entry.setStoreCode(nullSafer(res.getStore(), ShortEntityRes::getCode));
        entry.setTerminalCode(nullSafer(res.getTerminal(), ShortEntityRes::getCode));
        entry.setMemberCode(res.getMemberCode());
        entry.setProductAccountType(res.getProductAccountType());
        entry.setProductAccountCode(res.getProductAccountCode());
        entry.setTransactionTime(nullSafer(res.getTransactionTime(), DateUtil::formatDD_MM_yy));
        entry.setTransactionType(res.getTransactionType());
        entry.setGmv(nullSafer(res.getGmv(), BigDecimal::longValue));
        entry.setGrossAmount(nullSafer(res.getGrossAmount(), BigDecimal::longValue));
        entry.setNettAmount(nullSafer(res.getNettAmount(), BigDecimal::longValue));
        entry.setAwardPoint(nullSafer(res.getAwardPoint(), BigDecimal::longValue));
        entry.setRedeemPoint(nullSafer(res.getRedeemPoint(), BigDecimal::longValue));
        entry.setPoolCode(res.getPoolCode());
        entry.setCurrencyCode(res.getCurrencyCode());
        entry.setPointBalanceBefore(nullSafer(res.getPointBalanceBefore(), BigDecimal::longValue));
        entry.setPointBalanceAfter(nullSafer(res.getPointBalanceAfter(), BigDecimal::longValue));
        entry.setAwardPointBeforeLimit(nullSafer(res.getAwardPointBeforeLimit(), BigDecimal::longValue));
        entry.setAwardRetentionTime(nullSafer(res.getAwardRetentionTime(), DateUtil::formatDD_MM_yy));
        entry.setDescription(res.getDescription());
        entry.setReasonCode(res.getReasonCode());
        entry.setSchemeCode(res.getSchemeCode());
        entry.setChannel(res.getChannel());
        entry.setServiceCode(res.getServiceCode());
        entry.setStatus(nullSafer(res.getProcessStatus(), ERequestProcessStatus::getValue));

        return entry;
    }

    private TransactionExportEntry buildTransactionExportEntryAvailable(TransactionRequestRes res, Business business, Program program) {
        TransactionExportEntry entry = this.buildTransactionExportEntry(res, business, program);
        entry.setCancellation(res.isCancellation() ? EBoolean.YES.getValue() : EBoolean.NO.getValue());
        entry.setCancellationTime(nullSafer(res.getCancellationTime(), DateUtil::formatDD_MM_yy));
        entry.setCancellationType(res.getCancellationType());
        entry.setErrorCode(nullSafer(res.getErrorCode(), String::valueOf));
        entry.setErrorMessage(res.getErrorMessage());
        return entry;
    }

    @Override
    public ResourceDTO exportFileAvailable(Long batchNo) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findByIdAndBusinessId(batchNo, business.getId());
        List<TransactionRequestRes> requests = new LinkedList<>();

        Pageable pageable = PageRequest.of(0, OPSConstant.SIZE_OF_BATCH, Sort.by("id").ascending());

        Page<TransactionRequestRes> page;

        while (true) {
            page = getAvailableTransactionRequests(batchNo, null, pageable);
            requests.addAll(page.getContent());
            if (page.hasNext()) {
                pageable = pageable.next();
            } else {
                break;
            }
        }

        EntryContext context = EntryContext.builder()
                .moduleId(OPSConstant.TXN)
                .objectId(OPSConstant.AVAILABLE_TXN)
                .build();

        Program program = programService.findByIdAndBusinessId(batchRequest.getProgramId(), batchRequest.getBusinessId());
        String ddMMyyyy = DateUtil.formatToStringDDMMYYYY(new Date());
        String fileName = String.format(OPSConstant.FILE_NAME_EXPORT_TRANSACTION_BATCH,
                ddMMyyyy, batchRequest.getId(), business.getCode(), program.getCode());

        List<TransactionExportEntry> data = requests
                .stream().map(res -> buildTransactionExportEntryAvailable(res, business, program))
                .collect(Collectors.toList());

        return commonExcelService.opsExport(context, fileName, data);
    }

    private List<? extends ActionTransactionExcelDTO> mappingToExcelDto(ETransactionBatchType type, MultipartFile file) throws Exception {
        return Poiji.fromExcel(file.getInputStream(), PoijiExcelType.XLSX, detectClz(type));
    }

    private Class<? extends ActionTransactionExcelDTO> detectClz(ETransactionBatchType type) {
        switch (type) {
            case ADJUST:
                return AdjustTransactionExcelDTO.class;
            case REVERT_FULL:
                return RevertFullTransactionExcelDTO.class;
            case REVERT_PARTIAL:
                return RevertPartialTransactionExcelDTO.class;
            case REVERT_POINT:
                return RevertPointTransactionExcelDTO.class;
            default: // SALE | EARN | BURN
                return EarnBurnSaleTransactionExcelDTO.class;
        }
    }

    @Transactional
    public Long saveTransaction(CreateTransactionBatchRequestReq req, List<ActionTransactionExcelDTO> dtos, EBatchRequestType type) throws Exception {
        int totalMember = 0;
        BigDecimal totalGMV = BigDecimal.ZERO;
        BigDecimal totalGrossAmount = BigDecimal.ZERO;
        BigDecimal totalAwardPoint = BigDecimal.ZERO;
        BigDecimal totalRedeemPoint = BigDecimal.ZERO;
        Set<Long> uniqueMember = new HashSet<>();

        // Create transaction batch request
        TransactionBatchRequest batchRequest = this.saveTransactionBatchRequest(req, type, dtos);
        // Create transaction request by batch request
        List<TransactionRequest> listRequest = new ArrayList<>();
        for (ActionTransactionExcelDTO dto : dtos) {
            List<TransactionRequest> subListRequest = new ArrayList<>();
            TransactionRequest request = new TransactionRequest();

            BigDecimal requestGMV = BigDecimal.ZERO;
            BigDecimal requestGrossAmount = BigDecimal.ZERO;
            BigDecimal requestRedeemPoint = BigDecimal.ZERO;
            BigDecimal requestAwardPoint = BigDecimal.ZERO;

            switch (req.getTransactionBatchType()) {
                case EARN:
                case BURN:
                case SALE: {
                    EarnBurnSaleTransactionExcelDTO newDto = (EarnBurnSaleTransactionExcelDTO) dto;
                    request.setGmv(newDto.getGmv());
                    request.setGrossAmount(newDto.getGrossAmount());
                    request.setRedeemPoint(newDto.getRedeemPoint());
                    request.setCurrencyCode(newDto.getCurrencyCode());

                    totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(request.getRedeemPoint())
                            ? request.getRedeemPoint() : BigDecimal.ZERO);

                    totalGMV = totalGMV.add(Objects.nonNull(request.getGmv()) ? request.getGmv() : BigDecimal.ZERO);
                    totalGrossAmount = totalGrossAmount.add(Objects.nonNull(request.getGrossAmount())
                            ? request.getGrossAmount() : BigDecimal.ZERO);
                    break;
                }
                case ADJUST: {
                    AdjustTransactionExcelDTO newDto = (AdjustTransactionExcelDTO) dto;
                    request.setAdjustType(EAdjustmentType.of(newDto.getAdjustType()));
                    request.setAdjustPoint(newDto.getAdjustPoint());
                    request.setPoolCode(newDto.getPoolCode());
                    request.setReasonCode(newDto.getReasonCode());

                    if (EAdjustmentType.AWARD.equals(request.getAdjustType())) {
                        totalAwardPoint = totalAwardPoint.add(Objects.nonNull(request.getAdjustPoint())
                                ? request.getAdjustPoint() : BigDecimal.ZERO);
                    } else {
                        totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(request.getAdjustPoint())
                                ? request.getAdjustPoint() : BigDecimal.ZERO);
                    }

                    totalGMV = totalGMV.add(Objects.nonNull(request.getGmv()) ? request.getGmv() : BigDecimal.ZERO);
                    totalGrossAmount = totalGrossAmount.add(Objects.nonNull(request.getGrossAmount())
                            ? request.getGrossAmount() : BigDecimal.ZERO);
                    break;
                }
                case REVERT_POINT: {
                    RevertPointTransactionExcelDTO newDto = (RevertPointTransactionExcelDTO) dto;

                    Map<String, List<TransactionHistory>> groupTxnHistory = transactionHistoryRepository
                            .findByProgramIdAndInvoiceNoAndMemberId(req.getProgramId(), newDto.getInvoiceNo(), newDto.getMemberId())
                            .stream()
                            .filter(transactionHistory -> ETransactionStatus.SUCCESS.equals(transactionHistory.getStatus()))
                            .collect(Collectors.groupingBy(TransactionHistory::getPointTransactionId));
                    for(List<TransactionHistory> transactionHistories : groupTxnHistory.values()){
                        TransactionRequest transactionRequest = mapToTransactionRequest(transactionHistories);
                        transactionRequest.setReasonCode(dto.getReasonCode());

                        requestGMV = transactionRequest.getGmv();
                        requestGrossAmount = transactionRequest.getGrossAmount();

                        totalRedeemPoint = totalRedeemPoint.add(transactionRequest.getRedeemPoint());

                        totalAwardPoint = totalAwardPoint.add(transactionHistories.stream()
                                .map(transactionHistory -> transactionHistory.getAwardPoint() != null ? transactionHistory.getAwardPoint() : BigDecimal.ZERO)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));

                        transactionRequest.setBatchRequestId(batchRequest.getId());
                        transactionRequest.setStatus(ECommonStatus.ACTIVE);
                        transactionRequest.setProcessStatus(ERequestProcessStatus.PENDING);

                        uniqueMember.add(transactionRequest.getMemberId());

                        // Fill description when field IsReplaceDes is YES
                        if (req.getIsReplaceDes() != null && EBoolean.YES.equals(req.getIsReplaceDes()) && StringUtils.isBlank(dto.getDescription())) {
                            transactionRequest.setDescription(req.getDescription());
                        } else {
                            transactionRequest.setDescription(dto.getDescription());
                        }

                        subListRequest.add(transactionRequest);
                    }
                    totalGMV = totalGMV.add(requestGMV);
                    totalGrossAmount = totalGrossAmount.add(requestGrossAmount);
                    listRequest.addAll(subListRequest);
                    break;
                }
                case REVERT_FULL: {
                    RevertFullTransactionExcelDTO newDto = (RevertFullTransactionExcelDTO) dto;
                    convertRevertFull(request, newDto);

                    List<TransactionHistory> transactionHistories = transactionHistoryService
                            .getByTnxPointIds(Collections.singletonList(newDto.getTxnRef()));
                    for (TransactionHistory item : transactionHistories) {
                        totalAwardPoint = totalAwardPoint.add(Objects.nonNull(item.getAwardPoint())
                                ? item.getAwardPoint() : BigDecimal.ZERO);
                        totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(item.getRedeemPoint())
                                ? item.getRedeemPoint() : BigDecimal.ZERO);
                    }

                    TransactionHistory transactionHistory = transactionHistories.get(0);
                    totalGMV = totalGMV.add(Objects.nonNull(transactionHistory.getGmv())
                            ? transactionHistory.getGmv() : BigDecimal.ZERO);
                    totalGrossAmount = totalGrossAmount.add(Objects.nonNull(transactionHistory.getGrossAmount())
                            ? transactionHistory.getGrossAmount() : BigDecimal.ZERO);
                    break;
                }
                case REVERT_PARTIAL: {
                    RevertPartialTransactionExcelDTO newDto = (RevertPartialTransactionExcelDTO) dto;
                    convertRevertFull(request, newDto);
                    request.setRefundAmount(newDto.getRefundAmount());
                    request.setRedeemPoint(newDto.getRedeemPoint());
                    request.setGrossAmount(newDto.getGrossAmount());

                    totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(request.getRedeemPoint())
                            ? request.getRedeemPoint() : BigDecimal.ZERO);
                    totalGMV = totalGMV.add(Objects.nonNull(request.getGmv())
                            ? request.getGmv() : BigDecimal.ZERO);
                    totalGrossAmount = totalGrossAmount.add(Objects.nonNull(request.getGrossAmount())
                            ? request.getGrossAmount() : BigDecimal.ZERO);
                    break;
                }
                default:
                    throw new OpsBusinessException(OpsErrorCode.TRANSACTION_TYPE_NOT_SUPPORT, "Transaction type not support", null);
            }

            if (!ETransactionBatchType.REVERT_POINT.equals(req.getTransactionBatchType())){
                request.setBusinessId(req.getBusinessId());
                request.setProgramId(req.getProgramId());
                request.setIdType(EOpsIdType.lookup(dto.getIdType()).getMapping());
                request.setIdNo(dto.getIdNo());
                request.setInvoiceNo(dto.getInvoiceNo());
                request.setMemberId(dto.getMemberId());
                // Convert transaction time
                Date transactionTime = parseTxnTime(dto.getTransactionTime());
                request.setTransactionTime(transactionTime);

                request.setCorporationCode(dto.getCorporationCode());
                request.setStoreCode(dto.getStoreCode());
                request.setTerminalCode(dto.getTerminalCode());

                request.setBatchRequestId(batchRequest.getId());
                request.setStatus(ECommonStatus.ACTIVE);
                request.setProcessStatus(ERequestProcessStatus.PENDING);

                // Fill description when field IsReplaceDes is YES
                if (req.getIsReplaceDes() != null && EBoolean.YES.equals(req.getIsReplaceDes()) && StringUtils.isBlank(dto.getDescription())) {
                    request.setDescription(req.getDescription());
                } else {
                    request.setDescription(dto.getDescription());
                }

                listRequest.add(request);

                uniqueMember.add(request.getMemberId());
            }
            totalMember = uniqueMember.size();
        }
        transactionRequestService.saveAll(listRequest);

        batchRequest.setTotalMember(totalMember);
        batchRequest.setTotalGMV(totalGMV);
        batchRequest.setTotalGrossAmount(totalGrossAmount);
        batchRequest.setTotalAwardPoint(totalAwardPoint);
        batchRequest.setTotalRedeemPoint(totalRedeemPoint);

        transactionBatchRequestService.save(batchRequest);
        if (req.isSendEmail()) {
            this.sendEmail(batchRequest);
        }
        return batchRequest.getId();
    }

    private TransactionRequest mapToTransactionRequest(List<TransactionHistory> transactionHistories){
        TransactionRequest res = new TransactionRequest();
        TransactionHistory generalTxnHistory = transactionHistories.get(0);
        res.setBusinessId(generalTxnHistory.getBusinessId());
        res.setProgramId(generalTxnHistory.getProgramId());
        res.setIdType(EOpsIdType.lookup(generalTxnHistory.getMemberProductAccountType()).getMapping());
        res.setIdNo(generalTxnHistory.getMemberProductAccountCode());
        res.setInvoiceNo(generalTxnHistory.getInvoiceNo());
        res.setMemberId(generalTxnHistory.getMemberId());

        res.setTransactionTime(generalTxnHistory.getTransactionTime());

        if (generalTxnHistory.getStoreId() != null){
            Store store = storeService.findActive(generalTxnHistory.getStoreId());
            res.setStoreCode(store.getCode());
        }

        if (generalTxnHistory.getPosId() != null){
            Pos terminal = posService.findActive(generalTxnHistory.getPosId());
            res.setTerminalCode(terminal.getCode());
        }

        if (generalTxnHistory.getCorporationId() != null){
            Corporation corporation = corporationService.findActive(generalTxnHistory.getCorporationId());
            res.setCorporationCode(corporation.getCode());
        }

        if (generalTxnHistory.getPoolId() != null){
            Pool pool = poolService.findActive(generalTxnHistory.getPoolId());
            res.setPoolCode(pool.getCode());

            Currency currency = currencyService.findActive(pool.getCurrencyId());
            res.setCurrencyCode(currency.getCode());
        }

        BigDecimal requestRedeemPoint = BigDecimal.ZERO;

        for (TransactionHistory item : transactionHistories) {
            requestRedeemPoint = requestRedeemPoint.add(Objects.nonNull(item.getRedeemPoint())
                    ? item.getRedeemPoint() : BigDecimal.ZERO);
        }

        BigDecimal requestGmv = Objects.nonNull(generalTxnHistory.getGmv())
                ? generalTxnHistory.getGmv() : BigDecimal.ZERO;
        BigDecimal requestGrossAmount = Objects.nonNull(generalTxnHistory.getGrossAmount())
                ? generalTxnHistory.getGrossAmount() : BigDecimal.ZERO;

        res.setRedeemPoint(requestRedeemPoint);
        res.setGmv(requestGmv);
        res.setGrossAmount(requestGrossAmount);

        res.setTxnRefNo(generalTxnHistory.getPointTransactionId());
        res.setDescription(generalTxnHistory.getDescription());
        res.setOriginalInvoiceNo(generalTxnHistory.getOriginalInvoiceNo());

        return res;
    }

    private <V extends RevertPointTransactionExcelDTO> void convertRevertFull(TransactionRequest request, V newDto) {
        request.setOriginalInvoiceNo(newDto.getOriginalInvoiceNo());
        request.setCurrencyCode(newDto.getCurrencyCode());
        request.setStoreCode(newDto.getStoreCode());
        request.setTerminalCode(newDto.getTerminalCode());
        request.setTxnRefNo(newDto.getTxnRef());
        request.setPoolCode(newDto.getPoolCode());
        request.setReasonCode(newDto.getReasonCode());
    }

    private <V extends RevertFullTransactionExcelDTO> void convertRevertFull(TransactionRequest request, V newDto) {
        request.setOriginalInvoiceNo(newDto.getOriginalInvoiceNo());
        request.setCurrencyCode(newDto.getCurrencyCode());
        request.setStoreCode(newDto.getStoreCode());
        request.setTerminalCode(newDto.getTerminalCode());
        request.setTxnRefNo(newDto.getTxnRef());
    }

    public TransactionBatchRequest saveTransactionBatchRequest(CreateTransactionBatchRequestReq req,
                                                               EBatchRequestType type,
                                                               List<ActionTransactionExcelDTO> dtos) {
        TransactionBatchRequest batchRequest = new TransactionBatchRequest();
        batchRequest.setTransactionBatchType(req.getTransactionBatchType());
        batchRequest.setBusinessId(req.getBusinessId());
        batchRequest.setProgramId(req.getProgramId());
        batchRequest.setType(type);
        batchRequest.setName(req.getBatchName());
        batchRequest.setCampaignCode(req.getCampaignCode());
        batchRequest.setReferenceCode(req.getReferenceCode());
        batchRequest.setIsReplaceDes(req.getIsReplaceDes());
        batchRequest.setDescription(req.getDescription());
        batchRequest.setTransactionBatchType(req.getTransactionBatchType());
        batchRequest.setSmsTemplate(req.getSmsTemplate());

        batchRequest.setGenInvoiceNoMethod(req.getGenInvoiceNoMethod());
        batchRequest.setGenInvoiceNoCharacterSet(req.getGenInvoiceNoCharacterSet());
        batchRequest.setGenInvoiceNoPattern(req.getGenInvoiceNoPattern());

        batchRequest.setGenTransactionTimeMethod(req.getGenTransactionTimeMethod());
        batchRequest.setGenTransactionTimeValue(req.getGenTransactionTimeValue());

        batchRequest.setStatus(ECommonStatus.ACTIVE);
        batchRequest.setProcessStatus(EBatchRequestProcessStatus.PENDING);
        batchRequest.setApprovalStatus(EApprovalStatus.PENDING);

        batchRequest.setTotalRequests(dtos.size());
        batchRequest.setPendingRequests(dtos.size());
        batchRequest.setMadeReason(req.getMadeReason());

        return transactionBatchRequestService.save(batchRequest);
    }

    private boolean verifyCreateTransaction(CreateTransactionBatchRequestReq req,
                                            List<ActionTransactionExcelDTO> dtos) {


        if (ETransactionBatchType.REVERT_POINT.equals(req.getTransactionBatchType())){
            if (dtos.size() > this.fileMaxLine2) {
                throw new BusinessException(ErrorCode.FILE_MAX_SIZE);
            }
            if (dtos.isEmpty()) {
                ActionTransactionExcelDTO errorDto = new ActionTransactionExcelDTO();
                errorDto.setIsValid(false);
                errorDto.setStatus(OPSConstant.INVALID);
                errorDto.setErrorMessage("ID_TYPE, ID_NO, INVOICE_NO, REASON_CODE are required");
                dtos.add(errorDto);
                return false;
            }
        }else {
            if (dtos.size() > this.fileMaxLine) {
                throw new BusinessException(ErrorCode.FILE_MAX_SIZE);
            }
            if (dtos.isEmpty()) {
                return false;
            }
        }

        Set<String> storeCodeSet = new HashSet<>();
        Set<String> terminalCodeSet = new HashSet<>();
        Set<Integer> chainIdSet = new HashSet<>();
        for (ActionTransactionExcelDTO dto : dtos) {
            storeCodeSet.add(dto.getStoreCode());
            terminalCodeSet.add(dto.getTerminalCode());
        }
        // Declare map
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        req.setBusinessId(business.getId());
        Program program = programService.findByIdAndBusinessId(req.getProgramId(), business.getId());

        Function function = functionService.findByCode(EOpsFunctionCode.POINT_ADJ.getValue());
        Set<EOpsIdType> idTypeSet = programProductRepository.findByProgramId(req.getProgramId()).stream()
                .map(p -> EOpsIdType.lookup(p.getIdType()))
                .collect(Collectors.toSet());
        Map<String, Integer> reasonCodeMap = reasonCodeService.findByProgramId(req.getProgramId())
                .stream()
                .filter(i -> Objects.nonNull(i.getCode()) && ECommonStatus.ACTIVE.equals(i.getStatus()))
                .collect(Collectors.toMap(ReasonCode::getCode, ReasonCode::getFunctionId, (k1, k2) -> k1));
        Map<String, Integer> poolCodeMap = poolService.findAllByProgramIdAndStatus(req.getProgramId(), ECommonStatus.ACTIVE)
                .stream()
                .collect(Collectors.toMap(Pool::getCode, Pool::getId, (k1, k2) -> k1));
        Map<String, Store> storeMap = storeService.findActiveByProgramIdAndListStoreCode(req.getProgramId(), storeCodeSet)
                .stream()
                .peek(i -> chainIdSet.add(i.getChainId()))
                .collect(Collectors.toMap(Store::getCode, val -> val, (k1, k2) -> k1));
        Set<Integer> programCorporationSet = programCorporationService.findAllByProgramId(req.getProgramId())
                .stream()
                .filter(i -> ECommonStatus.ACTIVE.equals(i.getStatus()))
                .map(ProgramCorporation::getCorporationId)
                .collect(Collectors.toSet());
        Set<Integer> chainIdActiveSet = chainService.findByIdIn(chainIdSet)
                .stream()
                .filter(i -> ECommonStatus.ACTIVE.equals(i.getStatus()))
                .map(Chain::getId)
                .collect(Collectors.toSet());
        Map<String, Integer> posActiveMap = posService.findActiveByProgramIdAndListTerminalCode(req.getProgramId(), terminalCodeSet)
                .stream()
                .filter(i -> ECommonStatus.ACTIVE.equals(i.getStatus()))
                .collect(Collectors.toMap(Pos::getCode, Pos::getStoreId, (k1, k2) -> k1));
        Map<String, Integer> invoiceNoMap = new ConcurrentHashMap<>();
        Map<String, Corporation> corporationMap = corporationService.findByBusinessId(business.getId())
                .stream()
                .collect(Collectors.toMap(Corporation::getCode, corporation -> corporation, (k1, k2) -> k1));
        // Verify data
        Set<String> setCurrencyCode = this.getCurrencyCodeSetForTransaction(req.getBusinessId());
        Set<Boolean> result = dtos
                .parallelStream()
                .map(dto -> {
                            dto.setBusinessCode(business.getCode());
                            dto.setProgramCode(program.getCode());
                            return verify(req, dto, reasonCodeMap, function, poolCodeMap, program,
                                    setCurrencyCode, storeMap, programCorporationSet,
                                    chainIdActiveSet, posActiveMap, invoiceNoMap, corporationMap, idTypeSet);
                        }
                )
                .collect(Collectors.toSet());
        return !result.contains(false);
    }

    private boolean verify(
            CreateTransactionBatchRequestReq req,
            ActionTransactionExcelDTO dto,
            Map<String, Integer> reasonCodeMap,
            Function function,
            Map<String, Integer> poolCodeMap,
            Program program,
            Set<String> setCurrencyCode,
            Map<String, Store> storeMap,
            Set<Integer> programCorporationSet,
            Set<Integer> chainIdActiveSet,
            Map<String, Integer> posActiveMap,
            Map<String, Integer> invoiceNoMap,
            Map<String, Corporation> corporationMap,
            Set<EOpsIdType> idTypeSet
    ) {
        try {
            if (Objects.isNull(dto.getIsValid())) {
                dto.setIsValid(true);
            }
            boolean isRevert = false;
            TransactionHistory transactionHistory = null;
            Assert.assertTrue(StringUtils.isNotBlank(dto.getDescription()) && dto.getDescription().length() > 255,
                    "Description can not exceed 255 characters");
            if (ETransactionBatchType.ADJUST.equals(req.getTransactionBatchType())) {
                verifyDuplicateInvoiceNo(dto, invoiceNoMap);
                verifyBaseActionExcel(req, dto);
                AdjustTransactionExcelDTO newDto = (AdjustTransactionExcelDTO) dto;
                newDto.convertData();
                Assert.assertTrue(newDto.getAdjustPoint() == null || newDto.getAdjustPoint().compareTo(BigDecimal.ZERO) < 0, "Adjust point is invalid");
                Assert.assertTrue(Objects.isNull(reasonCodeMap.get(newDto.getReasonCode())), "Reason Code not found");
                Integer functionId = reasonCodeMap.get(newDto.getReasonCode());
                Assert.assertTrue(!Objects.equals(functionId, function.getId()), "Reason Code is invalid");
                Assert.assertNotNull(poolCodeMap.get(newDto.getPoolCode()), "Pool code not found");
                Assert.assertNotNull(EAdjustmentType.of(newDto.getAdjustType()), "Invalid adjust type");
            } else {
                if (ETransactionBatchType.REVERT_POINT.equals(req.getTransactionBatchType())) {
                    Assert.assertTrue(dto.getIdNo() == null || dto.getIdNo().isBlank(), "Id no not be null");
                    Assert.assertTrue(dto.getIdType() == null || dto.getIdType().isBlank(), "Id type not be null");
                    Assert.assertTrue(dto.getInvoiceNo() == null || dto.getInvoiceNo().isBlank(), "Invoice not be null");
                    Assert.assertTrue(dto.getReasonCode() == null || dto.getReasonCode().isBlank(), "Reason code not be null");
                    Assert.assertTrue(Objects.isNull(reasonCodeMap.get(dto.getReasonCode().trim())), "Reason code invalid");
                    Assert.assertTrue(!idTypeSet.contains(EOpsIdType.lookup(dto.getIdType().trim())), "Product Account not found");
                    dto.setInvoiceNo(dto.getInvoiceNo().trim());
                    dto.setIdType(dto.getIdType().trim());
                    dto.setReasonCode(dto.getReasonCode().trim());
                    dto.setIdNo(dto.getIdNo().trim());
                    transactionHistory = verifyRevertPoint(program, (RevertPointTransactionExcelDTO) dto);
                    isRevert = true;
                } else if (ETransactionBatchType.REVERT_FULL.equals(req.getTransactionBatchType())){
                    transactionHistory = verifyRevertFull(program, (RevertFullTransactionExcelDTO) dto);
                    isRevert = true;
                } else {
                    verifyDuplicateInvoiceNo(dto, invoiceNoMap);
                    if (ETransactionBatchType.REVERT_PARTIAL.equals(req.getTransactionBatchType())) {
                        transactionHistory = verifyRevertPartial(req, program, (RevertPartialTransactionExcelDTO) dto);
                        ((RevertPartialTransactionExcelDTO) dto).setGrossAmount(transactionHistory.getGrossAmount().subtract(((RevertPartialTransactionExcelDTO) dto).getRefundAmount()));
                        isRevert = true;
                    } else {
                        EarnBurnSaleTransactionExcelDTO newDto = (EarnBurnSaleTransactionExcelDTO) dto;
                        newDto.convertData();
                        verifyBaseActionExcel(req, dto);
                        verifyCurrencyCode(newDto, setCurrencyCode);
                        switch (req.getTransactionBatchType()) {
                            case EARN:
                                verifyTransactionByEarn(newDto);
                                break;
                            case BURN:
                                verifyTransactionByBurn(newDto);
                                break;
                            case SALE:
                                verifyTransactionBySale(newDto);
                                break;
                        }
                    }
                }
            }

            if (isRevert) {
                dto.setMemberId(transactionHistory.getMemberId());
                dto.setIdType(nullSafer(EOpsIdType.lookup(transactionHistory.getMemberProductAccountType()), EOpsIdType::getValue));
                dto.setIdNo(transactionHistory.getMemberProductAccountCode());
            } else {
                EOpsIdType idType = EOpsIdType.lookup(dto.getIdType());
                Assert.assertNotNull(idType, "Invalid Identification Type");
                CustomerIdentify identify = new CustomerIdentify();
                identify.setIdType(idType.getMapping());
                identify.setId(dto.getIdNo());
                Member member = memberService.find(identify, req.getProgramId());
                dto.setMemberId(member.getId());

                // Check Corporation
                Corporation corporation = corporationMap.get(dto.getCorporationCode());
                Assert.assertTrue(Objects.isNull(corporation),
                        "Corporation code not found");
                // Check Store Code
                Store store = storeMap.get(dto.getStoreCode());
                Assert.assertNotNull(store,
                        "Store code not found");
                // Check Program Corporation
                Assert.assertTrue(!programCorporationSet.contains(store.getCorporationId()),
                        "Program corporation not found");
                // Check Chain
                Assert.assertTrue(!chainIdActiveSet.contains(store.getChainId()),
                        "Chain not found");
                // Check store
                Assert.assertTrue(!corporation.getId().equals(store.getCorporationId()),
                        "Store code not found");
                // Check Pos
                Assert.assertTrue(Objects.isNull(posActiveMap.get(dto.getTerminalCode())) || !posActiveMap.get(dto.getTerminalCode()).equals(store.getId()),
                        "Terminal (POS) not found");
            }

            parseTxnTime(dto.getTransactionTime());
        } catch (Exception e) {
            dto.setErrorMessage(e.getMessage());
            dto.setIsValid(false);
            dto.setStatus(OPSConstant.INVALID);
        }
        return dto.getIsValid();
    }

    private <V extends ActionTransactionExcelDTO> void verifyBaseActionExcel(CreateTransactionBatchRequestReq req, V dto) {
        verifyMerchant(dto);
        verifyTxnTime(req.getGenTransactionTimeMethod(), dto);
        verifyInvoiceNo(req.getGenInvoiceNoMethod(), dto);
    }

    private void verifyTransactionByEarn(EarnBurnSaleTransactionExcelDTO newDto) {
        Assert.assertTrue(Objects.isNull(newDto.getGmv()) ||
                BigDecimal.ZERO.compareTo(newDto.getGmv()) > 0, "Gmv is invalid");
        Assert.assertTrue(Objects.isNull(newDto.getGrossAmount()) ||
                BigDecimal.ZERO.compareTo(newDto.getGrossAmount()) > 0, "Gross amount is invalid");
        Assert.assertTrue(Objects.nonNull(newDto.getRedeemPoint()), "Redeem point don't need");
    }

    private void verifyTransactionByBurn(EarnBurnSaleTransactionExcelDTO newDto) {
        Assert.assertTrue(Objects.isNull(newDto.getRedeemPoint()) ||
                BigDecimal.ZERO.compareTo(newDto.getRedeemPoint()) > 0, "Redeem point is invalid");
        Assert.assertTrue(Objects.nonNull(newDto.getGmv()), "Gmv don't need");
        Assert.assertTrue(Objects.nonNull(newDto.getGrossAmount()), "Gross amount don't need");
    }

    private void verifyTransactionBySale(EarnBurnSaleTransactionExcelDTO newDto) {
        Assert.assertTrue(Objects.isNull(newDto.getGmv()) ||
                BigDecimal.ZERO.compareTo(newDto.getGmv()) > 0, "Gmv is invalid");
        Assert.assertTrue(Objects.isNull(newDto.getGrossAmount()) ||
                BigDecimal.ZERO.compareTo(newDto.getGrossAmount()) > 0, "Gross amount is invalid");
        Assert.assertTrue(Objects.isNull(newDto.getRedeemPoint()) ||
                BigDecimal.ZERO.compareTo(newDto.getRedeemPoint()) > 0, "Redeem point is invalid");
    }

    private <V extends ActionTransactionExcelDTO> void verifyMerchant(V dto) {
        Assert.assertNotNull(dto.getCorporationCode(), "Corporation code must not be null");
        Assert.assertNotNull(dto.getStoreCode(), "Store code must not be null");
        Assert.assertNotNull(dto.getTerminalCode(), "Terminal code must not be null");
    }

    private <V extends ActionTransactionExcelDTO> void verifyTxnTime(EGenerationTransactionTimeMethod method, V dto) {
        Assert.assertTrue(EGenerationTransactionTimeMethod.MANUAL.equals(method) && StringUtils.isBlank(dto.getTransactionTime()),
                "Invalid transaction time");
    }

    private <V extends ActionTransactionExcelDTO> void verifyInvoiceNo(EGenerationInvoiceNoMethod method, V dto) {
        if (EGenerationInvoiceNoMethod.MANUAL.equals(method)) {
            Assert.assertTrue(StringUtils.isBlank(dto.getInvoiceNo()), "Invalid invoice no");
            Assert.assertTrue(dto.getInvoiceNo().length() > 50, "Invoice no cannot exceed 50 characters");
        }
    }

    private <V extends ActionTransactionExcelDTO> void verifyDuplicateInvoiceNo(V dto, Map<String, Integer> invoiceNoMap) {
        if (StringUtils.isNotBlank(dto.getInvoiceNo())) {
            Integer rowIndex = invoiceNoMap.get(dto.getInvoiceNo());
            Assert.assertTrue(Objects.nonNull(rowIndex), "Duplicate invoice no row " + rowIndex);
            invoiceNoMap.put(dto.getInvoiceNo(), dto.getRowIndex() + 1);
        }
    }

    private <V extends ActionTransactionExcelDTO> void verifyCurrencyCode(EarnBurnSaleTransactionExcelDTO dto, Set<String> setCurrencyCode) {
        Assert.assertTrue(StringUtils.isBlank(dto.getCurrencyCode()), "Invalid currency code");
        String currencyCode = dto.getCurrencyCode().trim();
        Assert.assertTrue(!setCurrencyCode.contains(currencyCode), "Invalid currency code");
        dto.setCurrencyCode(currencyCode);
    }

    private Date parseTxnTime(String txnTime) {
        if (Objects.nonNull(txnTime)) {
            // Check transaction time
            try {
                LocalDateTime localDateTime = LocalDateTime.parse(txnTime, this.FORMATTER_dd_MM_YYYY);
                return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
            } catch (Exception e) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Invalid transaction time", null);
            }
        }
        return null;
    }

    private TransactionHistory verifyRevertPartial(CreateTransactionBatchRequestReq req, Program program, RevertPartialTransactionExcelDTO dto) {
        verifyBaseActionExcel(req, dto);
        Assert.assertNotNull(dto.getRefundAmount(), "Refund amount must not be null");
        Assert.assertNotNull(dto.getRedeemPoint(), "Redeem point must not be null");
        return verifyRevertFull(program, dto);
    }

    private <V extends RevertPointTransactionExcelDTO> TransactionHistory verifyRevertPoint(Program program, V dto){
        CustomerIdentify customerIdentify = new CustomerIdentify(dto.getIdNo(), EIdType.of(dto.getIdType()));
        Member member = memberService.findActive(customerIdentify, program.getId());
        Assert.assertNotNull(member, "Member not found");

        String invoiceNo = dto.getInvoiceNo();
        Integer programId = program.getId();
        Long memberId = member.getId();

        List<TransactionHistory> transactionHistories = transactionHistoryRepository.findByProgramIdAndInvoiceNoAndMemberId(
                programId, invoiceNo, memberId);
        Assert.assertTrue(transactionHistories.isEmpty(), "Transaction not found");

        boolean isFailedTransaction = transactionHistories.stream().noneMatch(transactionHistory -> ETransactionStatus.SUCCESS.equals(transactionHistory.getStatus()));
        Assert.assertTrue(isFailedTransaction, "The transaction failed already");
        transactionHistories = transactionHistories.stream().filter(transactionHistory -> ETransactionStatus.SUCCESS.equals(transactionHistory.getStatus())).collect(Collectors.toList());


        Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistories);
        EOpsTransactionType transactionType = getTransactionType(points);

        for (TransactionHistory transactionHistory : transactionHistories) {
            Assert.assertTrue(EOpsTransactionType.ADJUSTMENT.equals(transactionType), "Could not revert adjust transaction");
            Assert.assertTrue(!Objects.isNull(transactionHistory.getCancellation()), "The transaction was cancelled already");
        }

        TransactionHistory generalTxnHistory = new TransactionHistory();
        generalTxnHistory.setMemberId(memberId);
        generalTxnHistory.setMemberProductAccountCode(dto.getIdNo());
        generalTxnHistory.setMemberProductAccountType(EIdType.of(dto.getIdType()));

        return generalTxnHistory;
    }

    private <V extends RevertFullTransactionExcelDTO> TransactionHistory verifyRevertFull(Program program, V dto) {
        Assert.assertNotNull(dto.getTxnRef(), "Transaction id must not be null");
        Assert.assertNotNull(dto.getOriginalInvoiceNo(), "Original invoice no must not be null");
        List<TransactionHistory> transactionHistories = transactionHistoryService.getByTnxPointIds(Collections.singletonList(dto.getTxnRef()));
        if (transactionHistories.isEmpty()) {
            throw new OpsBusinessException(OpsErrorCode.TRANSACTION_NOT_FOUND, "Transaction not found", null);
        }

        Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistories);

        EOpsTransactionType transactionType = getTransactionType(points);
        if (EOpsTransactionType.ADJUSTMENT.equals(transactionType)) {
            throw new UnsupportedOperationException("Unsupported reverting adjustment transaction type!");
        }

        TransactionHistory transaction = transactionHistories.get(0);
        if (!dto.getOriginalInvoiceNo().equals(transaction.getInvoiceNo())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Original invoice no is invalid", null);
        }

        Store store;
        if (Objects.nonNull(dto.getStoreCode())) {
            store = storeService.findActive(dto.getStoreCode(), transaction.getBusinessId());
        } else {
            store = storeService.findActive(transaction.getStoreId());
        }
        dto.setStoreCode(store.getCode());

        Pos terminal;
        if (Objects.nonNull(dto.getTerminalCode())) {
            terminal = posService.findActive(store.getId(), dto.getTerminalCode());
        } else {
            terminal = posService.findActive(transaction.getPosId());
        }
        dto.setTerminalCode(terminal.getCode());

        Currency baseCurrency = getBaseCurrencyFromTransaction(transaction, program, transactionType);
        if (Objects.nonNull(baseCurrency)) {
            dto.setCurrencyCode(baseCurrency.getCode());
        }

        return transaction;
    }

    private String detectObj(ETransactionBatchType type) {
        switch (type) {
            case ADJUST:
                return OPSConstant.VERIFY_TXN_ADJ;
            case REVERT_FULL:
                return OPSConstant.VERIFY_TXN_REVERT_FULL;
            case REVERT_POINT:
                return OPSConstant.VERIFY_TXN_REVERT_POINT;
            case REVERT_PARTIAL:
                return OPSConstant.VERIFY_TXN_REVERT_PARTIAL;
            default: // SALE | EARN | BURN
                return OPSConstant.VERIFY_TXN_SALE;
        }
    }

    private ResourceDTO exportFileExcel(ETransactionBatchType type, String fileName, List<ActionTransactionExcelDTO> dtos) {
        EntryContext context = EntryContext.builder()
                .moduleId(OPSConstant.TXN)
                .objectId(detectObj(type))
                .build();
        return commonExcelService.opsExport(context, fileName, dtos);
    }

    @Override
    public TransactionBatchRequestDetailRes approveBatchRequest(ApprovalReq req) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(Long.valueOf(req.getId()));
        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findActive(batchRequest.getProgramId());

        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            validateApproved(batchRequest);

            batchRequest.setApprovedBy(opsReqPendingValidator.getCurrentUser());
            batchRequest.setApprovedAt(new Date());
            batchRequest.setApprovalStatus(EApprovalStatus.APPROVED);
            batchRequest.setProcessStatus(EBatchRequestProcessStatus.PROCESSING);
            batchRequest = transactionBatchRequestService.save(batchRequest);

            // Call master worker to do transaction request
            try {
                MasterWorkerTransactionRequestFeignReq feignRequest = MasterWorkerTransactionRequestFeignReq
                        .builder()
                        .batchRequestId(batchRequest.getId())
                        .build();

                APIResponse<?> apiResponse = masterWorkerFeignClient.requestTransactionsV2(feignRequest);

                if (ErrorCode.SUCCESS.getValue() == apiResponse.getMeta().getCode()) {
                    Log.info(LogData.createLogData()
                            .append("msg", "Call to master/worker transaction request successfully")
                            .append("Approve transaction batch request ", batchRequest.getId())
                    );
                } else {
                    Log.error(LogData.createLogData()
                            .append("msg", "Error call to master/worker transaction request")
                            .append("batch request id ", batchRequest.getId())
                            .append("error code ", apiResponse.getMeta().getCode())
                            .append("error message ", apiResponse.getMeta().getMessage())
                    );
                }
            } catch (Exception e) {
                Log.error(LogData.createLogData()
                        .append("msg", "Error call to master/worker transaction request")
                        .append("batch request id ", batchRequest.getId())
                        .append("error message ", e.getMessage())
                );
            }
            batchRequest = transactionBatchRequestService.findActive(Long.valueOf(req.getId()));

        } else if (EApprovalStatus.REJECTED.equals(req.getStatus())) {
            validateRejected(batchRequest);
            batchRequest.setApprovedBy(opsReqPendingValidator.getCurrentUser());
            batchRequest.setApprovedAt(new Date());
            batchRequest.setApprovalStatus(EApprovalStatus.REJECTED);
            batchRequest.setRejectReason(req.getComment());
            batchRequest = transactionBatchRequestService.save(batchRequest);

        } else {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "EApprovalStatus mismatch ", req.getStatus());
        }
        if (req.isSendEmail()) {
            this.sendEmail(batchRequest);
        }
        return TransactionBatchRequestDetailRes
                .builder()
                .batchNo(batchRequest.getId())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .batchRequestType(batchRequest.getType())
                .transactionBatchType(batchRequest.getTransactionBatchType())
                .batchName(batchRequest.getName())
                .campaignCode(batchRequest.getCampaignCode())
                .referenceCode(batchRequest.getReferenceCode())
                .description(batchRequest.getDescription())
                .isReplaceDes(batchRequest.getIsReplaceDes())
                .reason(batchRequest.getRejectReason())
                .smsTemplate(batchRequest.getSmsTemplate())
                .genInvoiceNoMethod(batchRequest.getGenInvoiceNoMethod())
                .genInvoiceNoCharacterSet(batchRequest.getGenInvoiceNoCharacterSet())
                .genInvoiceNoPattern(batchRequest.getGenInvoiceNoPattern())
                .genTransactionTimeMethod(batchRequest.getGenTransactionTimeMethod())
                .genTransactionTimeValue(batchRequest.getGenTransactionTimeValue())
                .createdBy(batchRequest.getCreatedBy())
                .createdAt(batchRequest.getCreatedAt())
                .approvedBy(batchRequest.getApprovedBy())
                .approvedAt(batchRequest.getApprovedAt())
                .approvalStatus(batchRequest.getApprovalStatus())
                .processStatus(batchRequest.getProcessStatus())
                .build();
    }

    private void validateApproved(TransactionBatchRequest batchRequest) {
        if (!EApprovalStatus.PENDING.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_APPROVAL_STATUS_NOT_VALID);
        }
        if (!EBatchRequestProcessStatus.PENDING.equals(batchRequest.getProcessStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_PROCESS_STATUS_NOT_VALID);
        }
    }

    private void validateRejected(TransactionBatchRequest batchRequest) {
        if (!EApprovalStatus.PENDING.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_APPROVAL_STATUS_NOT_VALID);
        }
        if (!EBatchRequestProcessStatus.PENDING.equals(batchRequest.getProcessStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_PROCESS_STATUS_NOT_VALID);
        }
    }

    private <I, V> V nullSafer(I value, org.springframework.cglib.core.internal.Function<I, V> executorFunction) {
        return value != null ? executorFunction.apply(value) : null;
    }

    @Override
    public TransactionBatchRequestDetailRes retryBatchRequest(Long id) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(id);
        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findActive(batchRequest.getProgramId());
        validateRetry(batchRequest);

        MasterWorkerTransactionRequestFeignReq feignRequest = MasterWorkerTransactionRequestFeignReq
                .builder()
                .batchRequestId(batchRequest.getId())
                .build();

        APIResponse<?> apiResponse = masterWorkerFeignClient.requestTransactions(feignRequest);

        if (ErrorCode.SUCCESS.getValue() == apiResponse.getMeta().getCode()) {
            Log.info(LogData.createLogData()
                    .append("msg", "Call to master/worker transaction request successfully")
                    .append("Retry transaction batch request ", batchRequest.getId())
            );
        } else {
            throw new BusinessException(apiResponse.getMeta().getCode(), apiResponse.getMeta().getMessage(), null);
        }
        batchRequest = transactionBatchRequestService.findActive(id);

        return TransactionBatchRequestDetailRes
                .builder()
                .batchNo(batchRequest.getId())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .batchRequestType(batchRequest.getType())
                .transactionBatchType(batchRequest.getTransactionBatchType())
                .batchName(batchRequest.getName())
                .campaignCode(batchRequest.getCampaignCode())
                .referenceCode(batchRequest.getReferenceCode())
                .description(batchRequest.getDescription())
                .isReplaceDes(batchRequest.getIsReplaceDes())
                .reason(batchRequest.getRejectReason())
                .smsTemplate(batchRequest.getSmsTemplate())
                .genInvoiceNoMethod(batchRequest.getGenInvoiceNoMethod())
                .genInvoiceNoCharacterSet(batchRequest.getGenInvoiceNoCharacterSet())
                .genInvoiceNoPattern(batchRequest.getGenInvoiceNoPattern())
                .genTransactionTimeMethod(batchRequest.getGenTransactionTimeMethod())
                .genTransactionTimeValue(batchRequest.getGenTransactionTimeValue())
                .createdBy(batchRequest.getCreatedBy())
                .createdAt(batchRequest.getCreatedAt())
                .approvedBy(batchRequest.getApprovedBy())
                .approvedAt(batchRequest.getApprovedAt())
                .approvalStatus(batchRequest.getApprovalStatus())
                .processStatus(batchRequest.getProcessStatus())
                .build();
    }

    private void validateRetry(TransactionBatchRequest batchRequest) {
        if (!EApprovalStatus.APPROVED.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_APPROVAL_STATUS_NOT_VALID);
        }
        if (batchRequest.getSuccessRequests().equals(batchRequest.getTotalRequests())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_COMPLETELY_FINISHED);
        }
    }

    @Override
    public String createSapSaleOrder(TransactionSapSaleOrderCreateReq req) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService
                .findActive(req.getBatchRequestId());

        Assert.assertTrue(
                !EBatchRequestProcessStatus.COMPLETED.equals(batchRequest.getProcessStatus()),
                ErrorCode.TRANSACTION_REQUEST_PROCESS_STATUS_NOT_VALID
        );

        List<SapSaleOrderCall> sapSaleOrderCalls = sapSaleOrderCallRepository
                .findOrderByCreatedAtDesc(batchRequest.getId(), SAP_CALL_STATUS_SUCCESS);

        Assert.assertTrue(!sapSaleOrderCalls.isEmpty(), ErrorCode.ALREADY_HAVE_SAP_SALE_ORDER);

        BigDecimal point = batchRequest.getAwardSuccessPoint().add(batchRequest.getRedeemSuccessPoint());

        SAPSaleOrderCreateFeignReq feignRequest = SAPSaleOrderCreateFeignReq
                .builder()
                .customer(req.getSapCustomer())
                .postingDate(FORMATTER_yyyyMMdd.format(req.getPostingDate()))
                .remark(req.getRemark())
                .point(point.toString())
                .build();

        SAPSaleOrderCreateFeignRes apiResponse = sapFeignClient.createSaleOrder(feignRequest);

        SapSaleOrderCall sapSaleOrderCall = new SapSaleOrderCall();

        if (Objects.nonNull(apiResponse) && Objects.nonNull(apiResponse.getData())) {
            if (SAP_API_STATUS_SUCCESS.equals(apiResponse.getData().getStatus())) {
                Log.info(LogData.createLogData()
                        .append("msg", "Call SAP to create sale order successfully")
                        .append("Transaction batch request ", req.getBatchRequestId())
                        .append("Sale order ", apiResponse.getData().getSaleOrder())
                );
                sapSaleOrderCall.setBusinessId(batchRequest.getBusinessId());
                sapSaleOrderCall.setProgramId(batchRequest.getProgramId());
                sapSaleOrderCall.setBatchRequestId(batchRequest.getId());
                sapSaleOrderCall.setSaleOrder(apiResponse.getData().getSaleOrder());
                sapSaleOrderCall.setCustomer(req.getSapCustomer());
                sapSaleOrderCall.setPoint(point);
                sapSaleOrderCall.setRemark(req.getRemark());
                sapSaleOrderCall.setPostingDate(req.getPostingDate());
                sapSaleOrderCall.setCallType(SAP_CALL_TYPE_CREATE);
                sapSaleOrderCall.setCallStatus(SAP_CALL_STATUS_SUCCESS);
                sapSaleOrderCall.setCallResponse(new Gson().toJson(apiResponse.getData()));
                sapSaleOrderCall = sapSaleOrderCallRepository.save(sapSaleOrderCall);
            } else {
                throw new BusinessException(
                        ErrorCode.SAP_SALE_ORDER_DATA_NOT_VALID, null, null,
                        new Object[]{apiResponse.getData().getDescription()}
                );
            }
        } else {
            throw new BusinessException(ErrorCode.SYSTEM_CALL_SAP_ERROR);
        }
        return sapSaleOrderCall.getSaleOrder();
    }

    @Override
    public String updateSapSaleOrder(Long batchRequestId, TransactionSapSaleOrderUpdateReq req) {
        final String DEFAULT_TYPE = "01";

        TransactionBatchRequest batchRequest = transactionBatchRequestService
                .findActive(batchRequestId);

        Assert.assertTrue(
                !EBatchRequestProcessStatus.COMPLETED.equals(batchRequest.getProcessStatus()),
                ErrorCode.TRANSACTION_REQUEST_PROCESS_STATUS_NOT_VALID
        );

        List<SapSaleOrderCall> sapSaleOrderCallsOfBatchRequest = sapSaleOrderCallRepository
                .findOrderByCreatedAtDesc(batchRequest.getId(), SAP_CALL_STATUS_SUCCESS);

        Assert.assertTrue(!sapSaleOrderCallsOfBatchRequest.isEmpty(), ErrorCode.ALREADY_HAVE_SAP_SALE_ORDER);

        //Find newestSapSaleOrderCall
        List<SapSaleOrderCall> sapSaleOrderCalls = sapSaleOrderCallRepository
                .findOrderByCreatedAtDesc(req.getSapSaleOrder(), SAP_CALL_STATUS_SUCCESS);

        sapSaleOrderCalls.forEach(item -> Assert.assertTrue(
                batchRequestId.equals(item.getBatchRequestId()),
                ErrorCode.ALREADY_HAVE_SAP_SALE_ORDER)
        );

        Assert.assertTrue(sapSaleOrderCalls.isEmpty(), ErrorCode.SAP_SALE_ORDER_NOT_FOUND);

        SapSaleOrderCall newestSapSaleOrderCall = sapSaleOrderCalls.get(0);

        BigDecimal point = batchRequest.getAwardSuccessPoint().add(batchRequest.getRedeemSuccessPoint());

        SAPSaleOrderUpdateFeignReq feignRequest = SAPSaleOrderUpdateFeignReq
                .builder()
                .saleOrder(req.getSapSaleOrder())
                .customer(newestSapSaleOrderCall.getCustomer())
                .type(DEFAULT_TYPE)
                .point(point.toString())
                .build();

        SAPSaleOrderUpdateFeignRes apiResponse = sapFeignClient.updateSaleOrder(feignRequest.getSaleOrder(), feignRequest);

        if (Objects.nonNull(apiResponse) && Objects.nonNull(apiResponse.getData())) {
            SapSaleOrderCall sapSaleOrderCall = new SapSaleOrderCall();

            if (SAP_API_STATUS_SUCCESS.equals(apiResponse.getData().getStatus())) {
                Log.info(LogData.createLogData()
                        .append("msg", "Call SAP to update sale order successfully")
                        .append("Transaction batch request ", batchRequestId)
                        .append("Sale order ", apiResponse.getData().getSaleOrder())
                );
                sapSaleOrderCall.setBusinessId(batchRequest.getBusinessId());
                sapSaleOrderCall.setProgramId(batchRequest.getProgramId());
                sapSaleOrderCall.setBatchRequestId(batchRequest.getId());
                sapSaleOrderCall.setSaleOrder(apiResponse.getData().getSaleOrder());
                sapSaleOrderCall.setCustomer(newestSapSaleOrderCall.getCustomer());
                sapSaleOrderCall.setPoint(point);
                sapSaleOrderCall.setRemark(newestSapSaleOrderCall.getRemark());
                sapSaleOrderCall.setPostingDate(newestSapSaleOrderCall.getPostingDate());
                sapSaleOrderCall.setCallType(SAP_CALL_TYPE_UPDATE);
                sapSaleOrderCall.setCallStatus(SAP_CALL_STATUS_SUCCESS);
                sapSaleOrderCall.setCallResponse(new Gson().toJson(apiResponse.getData()));
                sapSaleOrderCallRepository.save(sapSaleOrderCall);
            } else {
                throw new BusinessException(
                        ErrorCode.SAP_SALE_ORDER_DATA_NOT_VALID, null, null,
                        new Object[]{apiResponse.getData().getDescription()}
                );
            }
        } else {
            throw new BusinessException(ErrorCode.SYSTEM_CALL_SAP_ERROR);
        }

        return req.getSapSaleOrder();
    }

    @Override
    public List<SapSaleOrderRes> getSapSaleOrders(String sapCustomer) {

        SAPSaleOrderGetFeignRes apiResponse = sapFeignClient.getSaleOrders(sapCustomer);

        if (Objects.nonNull(apiResponse.getData()) && Objects.nonNull(apiResponse.getData().getSaleOrders())) {
            return apiResponse.getData().getSaleOrders()
                    .stream()
                    .map(item -> SapSaleOrderRes
                            .builder()
                            .sapSaleOrder(item.getSaleOrder())
                            .billing(item.getBilling())
                            .remark(item.getRemark())
                            .point(item.getPoint())
                            .build())
                    .collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public SapSaleOrderDetailRes getSapSaleOrder(String sapSaleOrder) {

        List<SapSaleOrderCall> sapSaleOrderCalls = sapSaleOrderCallRepository
                .findOrderByCreatedAtDesc(sapSaleOrder, SAP_CALL_STATUS_SUCCESS);

        Assert.assertTrue(sapSaleOrderCalls.isEmpty(), ErrorCode.SAP_SALE_ORDER_NOT_FOUND);

        SapSaleOrderCall newestSapSaleOrderCall = sapSaleOrderCalls.get(0);

        SapSaleOrderCall oldestSapSaleOrderCall = sapSaleOrderCalls.get(sapSaleOrderCalls.size() - 1);

        SapSaleOrderRes res = getSapSaleOrders(newestSapSaleOrderCall.getCustomer())
                .stream()
                .filter(item -> sapSaleOrder.equals(item.getSapSaleOrder()))
                .findFirst()
                .orElse(null);

        Assert.assertTrue(Objects.isNull(res), ErrorCode.SAP_SALE_ORDER_NOT_FOUND);

        return SapSaleOrderDetailRes
                .builder()
                .sapCustomer(newestSapSaleOrderCall.getCustomer())
                .sapSaleOrder(newestSapSaleOrderCall.getSaleOrder())
                .point(res.getPoint())
                .status(SAP_CALL_STATUS_SUCCESS)
                .postingDate(newestSapSaleOrderCall.getPostingDate())
                .remark(res.getRemark())
                .type(newestSapSaleOrderCall.getCallType())
                .calledAt(newestSapSaleOrderCall.getCreatedAt())
                .response(new Gson().fromJson(newestSapSaleOrderCall.getCallResponse(), SapSaleOrderDetailRes.Response.class))
                .createdAt(oldestSapSaleOrderCall.getCreatedAt())
                .createdBy(oldestSapSaleOrderCall.getCreatedBy())
                .build();
    }

    @Override
    public SapSaleOrderDetailRes getSapSaleOrder(Long batchRequestId) {
        List<SapSaleOrderCall> sapSaleOrderCalls = sapSaleOrderCallRepository.findByBatchRequestId(batchRequestId);

        Assert.assertTrue(sapSaleOrderCalls.isEmpty(), ErrorCode.SAP_SALE_ORDER_NOT_FOUND);

        SapSaleOrderCall sapSaleOrderCall = sapSaleOrderCalls.get(0);

        return getSapSaleOrder(sapSaleOrderCall.getSaleOrder());
    }

    @Override
    public void cancelInReview(CancelReq req) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.find((long) req.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.TRANSACTION_BATCH_REQUEST_NOT_FOUND));
        if (!EApprovalStatus.PENDING.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to cancel",
                    LogData.createLogData().append("id", req.getId()));
        }
        batchRequest.setApprovalStatus(EApprovalStatus.CANCELLED);
        transactionBatchRequestService.save(batchRequest);
        if (req.isNotification()) {
            this.sendEmail(batchRequest);
        }
    }

    @Override
    public Page<PWPTransactionRes> getPWPTransactionList(Integer programId, String invoiceNo, String postPurchaseRef,
                                                         EOpsIdType accountType, String accountCode, Date txnTimeStart,
                                                         Date txnTimeEnd, EPayWithPointStatus status, Date expiredStart,
                                                         Date expiredEnd, Pageable pageable) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Long memberId = null;
        // find member id
        if (Objects.nonNull(accountType) && Objects.nonNull(accountCode)) {
            Member member = null;
            try {
                member = memberService.find(new CustomerIdentify(accountCode, accountType.getMapping()), programId);
            } catch (BusinessException e) {

            }
            if (member == null) {
                return Page.empty(pageable);
            }
            memberId = member.getId();
        }

        Page<PayWithPointTransactionHistory> txns = payWithPointTransactionService.getPWPTransactionList(business.getId(), programId, invoiceNo, postPurchaseRef,
                memberId, txnTimeStart, txnTimeEnd, status, expiredStart, expiredEnd, pageable);

        Set<Long> memberIds = txns.stream().map(PayWithPointTransactionHistory::getMemberId).collect(Collectors.toSet());
        Map<Long, Member> members = memberService.findByIdIn(memberIds).stream()
                .collect(Collectors.toMap(Member::getId, member -> member));

        Set<Integer> schemeIds = txns.stream().map(PayWithPointTransactionHistory::getSchemeId).collect(Collectors.toSet());

        Map<Integer, Scheme> schemes = schemeService.findByIdIn(new ArrayList<>(schemeIds)).stream()
                .collect(Collectors.toMap(Scheme::getId, scheme -> scheme));

        return new PageImpl<>(txns.stream().map(t -> {
                    PoolShortRes poolShort = null;
                    if (Objects.nonNull(t.getPoolId())) {
                        Pool pool = poolService.findActive(t.getPoolId());
                        poolShort = new PoolShortRes(pool.getId(), pool.getCode(), pool.getName(), null, null, null, EPoolType.POINT);
                    } else if (Objects.nonNull(t.getRewardPoolId())) {
                        RewardPool rewardPool = rewardPoolService.findActive(t.getRewardPoolId());
                        poolShort = new PoolShortRes(rewardPool.getId(), rewardPool.getPoolCode(), rewardPool.getName(), null, null, null, rewardPool.getPoolType());
                    }
                    return PWPTransactionRes.valueOfList(
                            t,
                            members.get(t.getMemberId()),
                            schemes.get(t.getSchemeId()),
                            poolShort);
                })
                .collect(Collectors.toList()), pageable, txns.getTotalElements());
    }

    private TransactionRequestRes buildTransactionAvailable(TransactionBatchRequest batchRequest,
                                                            TransactionRequest request,
                                                            List<TransactionHistory> transactionHistoryList,
                                                            Map<Integer, String> schemeCodeMapById,
                                                            Map<Integer, String> poolCodeMapById,
                                                            Map<Integer, ShortEntityRes> corporationMap,
                                                            Map<Integer, ShortEntityRes> chainMap,
                                                            Map<Integer, ShortEntityRes> storeMap,
                                                            Pos terminal,
                                                            Map<Long, String> memberCodeMap, Program program,
                                                            Business business) {
        ShortEntityRes corporation = null;
        ShortEntityRes chain = null;
        ShortEntityRes store = null;
        ShortEntityRes terminalResult = null;

        if (Objects.nonNull(terminal)) {
            corporation = corporationMap.get(terminal.getCorporationId());
            chain = chainMap.get(terminal.getChainId());
            store = storeMap.get(terminal.getStoreId());
            terminalResult = new ShortEntityRes(terminal.getId(), terminal.getName(), terminal.getCode());
        }
        TransactionRequestRes result = TransactionRequestRes.builder().build();
        result.setTxnRefNo(request.getTxnRefNo());
        result.setInvoiceNo(request.getInvoiceNo());
        result.setProgram(new ShortEntityRes(program.getId(), program.getName(), program.getCode()));
        result.setOriginalInvoiceNo(request.getOriginalInvoiceNo());
        result.setCorporation(corporation);
        result.setChain(chain);
        result.setStore(store);
        result.setTerminal(terminalResult);
        result.setMemberId(request.getMemberId());
        result.setMemberCode(memberCodeMap.get(request.getMemberId()));
        result.setProductAccountType(nullSafer(EOpsIdType.lookup(request.getIdType()), EOpsIdType::getValue));
        result.setProductAccountCode(request.getIdNo());
        result.setTransactionTime(request.getTransactionTime());
        result.setCreatedAt(request.getCreatedAt());
        EOpsTransactionType type = getTransactionType(batchRequest.getTransactionBatchType(), transactionHistoryList, request);
        result.setTransactionType(type != null ? type.getValue() : null);
        result.setGmv(request.getGmv());
        result.setGrossAmount(request.getGrossAmount());
        result.setCurrencyCode(request.getCurrencyCode());
        result.setDescription(request.getDescription());
        result.setProcessStatus(request.getProcessStatus());
        result.setSmsErrorCode(request.getSmsErrorCode());
        result.setSmsErrorMessage(request.getSmsErrorMessage());
        result.setErrorCode(request.getErrorCode());
        result.setErrorMessage(request.getErrorMessage());

        if (batchRequest.getTransactionBatchType() == ETransactionBatchType.ADJUST) {
            if (EAdjustmentType.AWARD.equals(request.getAdjustType())) {
                result.setAwardPoint(request.getAdjustPoint());
            } else {
                result.setRedeemPoint(request.getAdjustPoint());
            }
        } else {
            result.setRedeemPoint(request.getRedeemPoint());
        }

        if (CollectionUtils.isNotEmpty(transactionHistoryList)) {
            TransactionHistory history = transactionHistoryList.get(0);
            TransactionInfo txnInfo = getTransactionInfo(transactionHistoryList, schemeCodeMapById, poolCodeMapById);

            result.setPointBalanceBefore(txnInfo.getBalanceBefore());
            result.setPointBalanceAfter(txnInfo.getBalanceAfter());
            result.setNettAmount(txnInfo.getNettAmount());
            result.setAwardPoint(txnInfo.getTotalAwardPoint());
            result.setRedeemPoint(txnInfo.getTotalRedeemPoint());
            result.setAwardPointBeforeLimit(txnInfo.awardBeforeLimit);
            result.setAwardRetentionTime(txnInfo.getAwardRetentionTime());
            result.setCancellation(history.getCancellation() != null);
            result.setCancellationTime(history.getCancellationTime());
            result.setCancellationType(history.getCancellationType());
            if (Objects.nonNull(history.getReasonCode())) {
                ReasonCode reasonCode = reasonCodeService.findByCode(business.getId(), program.getId(), history.getReasonCode());
                result.setReasonCode(history.getReasonCode());
                result.setReasonName(reasonCode.getName());
            }
            result.setSchemeCode(txnInfo.getSchemeCodes());
            result.setPoolCode(txnInfo.getPoolCodes());
            result.setChannel(history.getChannel());
            result.setServiceCode(history.getServiceCode());
            result.setSchemeCode(txnInfo.getSchemeCodes());
            result.setPoolCode(txnInfo.getPoolCodes());

            if (result.getProcessStatus().equals(ERequestProcessStatus.SUCCESS)) {
                if (result.getChannel() == null) {
                    result.setChannel(defaultTransactionChannel);
                }
                if (result.getServiceCode() == null) {
                    result.setServiceCode(defaultTransactionServiceCode);
                }
            }
            Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistoryList);
            result.setTransactionType(nullSafer(getTransactionType(points), EOpsTransactionType::getValue));
        } else {
            if (Objects.nonNull(request.getReasonCode())) {
                ReasonCode reasonCode = reasonCodeService.findByCode(business.getId(), program.getId(), request.getReasonCode());
                result.setReasonCode(request.getReasonCode());
                result.setReasonName(reasonCode != null ? reasonCode.getName() : null);
            }
            result.setPoolCode(request.getPoolCode());
        }

        return result;
    }

    @Override
    public TransactionDetailRes getTransactionInfoDetail(String txnRefNo, String from) {
       ETransferPointFrom fromEnum = null;
       if (from != null && !from.isEmpty()) {
           try {
               fromEnum = ETransferPointFrom.valueOf(from.toUpperCase());
           } catch (IllegalArgumentException e) {
               throw new BusinessException(ErrorCode.BAD_REQUEST, "Invalid from parameter",
                       LogData.createLogData().append("from", from));
           }
       }
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        List<TransactionHistory> transactionHistories = transactionHistoryService.getByTnxPointIds(business.getId(), Collections.singletonList(txnRefNo));

        Assert.assertTrue(transactionHistories.isEmpty(), ErrorCode.TRANSACTION_NOT_FOUND);
        
        TransactionHistory transactionHistory = getTransactionHistory(transactionHistories, fromEnum);

        Program program = nullSafer(transactionHistory.getProgramId(),
                programId -> programService.find(programId).orElse(null));
        Corporation corporation = nullSafer(transactionHistory.getCorporationId(),
                corporationId -> corporationService.find(corporationId).orElse(null));
        Chain chain = nullSafer(transactionHistory.getChainId(),
                chainId -> chainService.find(chainId).orElse(null));
        Store store = nullSafer(transactionHistory.getStoreId(),
                storeId -> storeService.find(storeId));
        Pos terminal = nullSafer(transactionHistory.getPosId(),
                posId -> posService.find(posId).orElse(null));
        Member member = nullSafer(transactionHistory.getMemberId(),
                memberId -> memberService.find(memberId).orElse(null));
        MemberStatus memberStatus = memberStatusService.findByProgramIdAndCode(program.getId(), member.getStatus()).orElse(null);

        ReasonCode reasonCode = Objects.nonNull(transactionHistory.getReasonCode())
                ? reasonCodeService.findByCode(transactionHistory.getBusinessId(), transactionHistory.getProgramId(), transactionHistory.getReasonCode())
                : null;

        String reasonName = nullSafer(reasonCode, ReasonCode::getName);

        TransactionDetailRes.MemberProfile memberProfile = getMemberProfile(member, memberStatus);

        List<TransactionDetailRes.Attribute> attributes = getTransactionAttributes(txnRefNo, transactionHistory.getProgramId());

        EAdjustmentType adjustmentType = getAdjustmentType(transactionHistory);

        // Get original invoice
        TransactionHistory originalTxnHistory = null;
        if (Objects.nonNull(transactionHistory.getOriginalInvoiceNo())) {
            originalTxnHistory = transactionHistoryService
                    .findByInvoiceNo(transactionHistory.getOriginalInvoiceNo(), transactionHistory.getBusinessId(), transactionHistory.getProgramId(), transactionHistory.getStoreId())
                    .stream().findFirst().orElse(null);
        }

        TransactionDetailRes.Invoice originalInvoice = nullSafer(originalTxnHistory, txnHistory -> TransactionDetailRes.Invoice
                .builder()
                .invoiceNo(txnHistory.getInvoiceNo())
                .txnRefNo(txnHistory.getPointTransactionId())
                .build()
        );

        // Get related invoice
//        TransactionHistory relatedTxnHistory = transactionHistoryService
//                .findByOriginalInvoiceNo(transactionHistory.getInvoiceNo(), transactionHistory.getBusinessId(), transactionHistory.getProgramId(), transactionHistory.getStoreId())
//                .stream().findFirst().orElse(null);

        TransactionHistory relatedTxnHistory = null;

        TransactionDetailRes.Invoice relatedInvoice = nullSafer(relatedTxnHistory, txnHistory -> TransactionDetailRes.Invoice
                .builder()
                .invoiceNo(txnHistory.getInvoiceNo())
                .txnRefNo(txnHistory.getPointTransactionId())
                .build()
        );

        // Handle Escrow Point
        if (ETransactionType.REVOKE_HOLDING_POINTS.equals(transactionHistory.getType())) {
            List<Integer> schemeIds = new ArrayList<>();
            List<String> pointTransactionIds = new ArrayList<>();
            List<EscrowPointTransactionHistory> escrowPointTransactionHistories = escrowPointTransactionHistoryService.findTxnRefNoByRevokeTxnRefNo(transactionHistory.getPointTransactionId());

            Map<String, EscrowPointTransactionHistory> escrowPointMap = new HashMap<>();
            escrowPointTransactionHistories.forEach(txnHistory -> {
                schemeIds.add(txnHistory.getSchemeId());
                pointTransactionIds.add(txnHistory.getTxnRefNo());
                escrowPointMap.put(txnHistory.getTxnRefNo(), txnHistory);
            });

            transactionHistories = transactionHistoryService.getByTnxPointIds(pointTransactionIds, schemeIds);
            transactionHistories.forEach(t -> {
                EscrowPointTransactionHistory history = escrowPointMap.get(t.getPointTransactionId());
                t.setBalanceBefore(transactionHistory.getBalanceBefore());
                t.setBalanceAfter(transactionHistory.getBalanceAfter());
                t.setReasonCode(transactionHistory.getReasonCode());
                t.setCoreErrorCode(transactionHistory.getCoreErrorCode());
                t.setCoreErrorMessage(transactionHistory.getCoreErrorMessage());
                t.setStatus(transactionHistory.getStatus());
                t.setAwardPoint(Objects.nonNull(history) ? history.getAwardPoint() : BigDecimal.ZERO);
            });
        }

        // Get poolMap, schemeMap, currencyMap, currencyIdMapByPoolId
        // To get pointAwardDetails and pointRedeemDetails
        List<Integer> schemeIds = new ArrayList<>();
        List<Integer> poolIds = new ArrayList<>();
        List<Integer> currencyIds = new ArrayList<>();
        List<Integer> rewardPoolIds = new ArrayList<>();
        Map<Integer, ShortEntityRes> poolMap = new HashMap<>();
        Map<Integer, Integer> currencyIdMapByPoolId = new HashMap<>();
        Map<Integer, ShortEntityRes> rewardPoolMap = new HashMap<>();
       List<TransactionDetailRes.PointAwardDetail> pointAwardDetails = new ArrayList<>();
       List<TransactionDetailRes.PointRedeemDetail> pointRedeemDetails = new ArrayList<>();
       List<TransactionDetailRes.PointTopUpDetail> pointTopUpDetails = new ArrayList<>();
       List<TransactionDetailRes.VoucherDetail> voucherDetailList = new ArrayList<>();

       if (fromEnum != null && ETransactionType.TRANSFER.equals(transactionHistory.getType())) {
            ETransferPointFrom fromReference = fromEnum.equals(ETransferPointFrom.RECEIVER) ? ETransferPointFrom.SENDER : ETransferPointFrom.RECEIVER;
            TransactionHistory referenceTxnHistory = getTransactionHistory(transactionHistories, fromReference);
            processTransferPoint(transactionHistory, fromEnum, reasonName, pointAwardDetails, pointRedeemDetails, referenceTxnHistory);
       } else {
            transactionHistories.forEach(txnHistory -> {
                schemeIds.add(txnHistory.getSchemeId());
                poolIds.add(txnHistory.getPoolId());
                rewardPoolIds.add(txnHistory.getRewardPoolId());
            });

            Map<Integer, Scheme> schemeMap = schemeService.findByIdIn(schemeIds).stream()
                    .collect(Collectors.toMap(Scheme::getId, Scheme -> Scheme));

            poolService.findByIdIn(poolIds).forEach(pool -> {
                currencyIds.add(pool.getCurrencyId());
                currencyIdMapByPoolId.put(pool.getId(), pool.getCurrencyId());
                poolMap.put(pool.getId(), new ShortEntityRes(pool.getId(), pool.getName(), pool.getCode()));
            });

            rewardPoolService.findByIdIn(rewardPoolIds).forEach(rewardPool ->
                    rewardPoolMap.put(rewardPool.getId(),
                            new ShortEntityRes(rewardPool.getId(), rewardPool.getName(), rewardPool.getPoolCode())));

            Map<Integer, ShortEntityRes> currencyMap = currencyService.findByIdIn(currencyIds).stream().collect(Collectors.toMap(
                    Currency::getId,
                    currency -> new ShortEntityRes(currency.getId(), currency.getName(), currency.getCode()),
                    (k1, k2) -> k2
            ));

            pointAwardDetails = getPointAwardDetails(transactionHistories, poolMap, schemeMap, currencyMap, currencyIdMapByPoolId, reasonName);

            pointRedeemDetails = getPointRedeemDetails(transactionHistories, poolMap, schemeMap, currencyMap, currencyIdMapByPoolId, reasonName);

            pointTopUpDetails = getPointTopUpDetails(transactionHistories, poolMap, schemeMap, currencyMap, currencyIdMapByPoolId, reasonName);

            voucherDetailList = getVoucherDetails(transactionHistories, rewardPoolMap, schemeMap);
       }

        //Get Transaction Batch Request
//        TransactionRequest transactionRequest = transactionRequestService.findByTxnRefNo(txnRefNo);

        TransactionDetailRes.IsSyncElasticRes isSyncElasticRes = new TransactionDetailRes.IsSyncElasticRes();

        try {
            APIResponse<IsSyncElasticlFeignRes> response = oneloyaltyElasticFeignClient.searchListHistory(txnRefNo);
            IsSyncElasticlFeignRes isSyncElasticlFeignRes = response.getData();

            if (isSyncElasticlFeignRes != null) {
                isSyncElasticRes.setOriginalTxn(isSyncElasticlFeignRes.getOriginalTxn());
                isSyncElasticRes.setRefundTxn(isSyncElasticlFeignRes.getRefundTxn());
            }
        } catch (BusinessException e) {
            Log.error(LogData.createLogData()
                    .append("msg", "Exception while call search list history api in oneloyalty elastic")
                    .append("err", e)
            );
        }

        return TransactionDetailRes
                .builder()
                .id(transactionHistory.getId())
                .txnRefNo(transactionHistory.getPointTransactionId())
                .invoiceNo(transactionHistory.getInvoiceNo())
                .originalInvoice(originalInvoice)
                .business(nullSafer(business, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .program(nullSafer(program, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .corporation(nullSafer(corporation, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .chain(nullSafer(chain, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .store(nullSafer(store, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .terminal(nullSafer(terminal, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .status(transactionHistory.getStatus())
                .coreErrorCode(transactionHistory.getCoreErrorCode())
                .coreErrorMessage(transactionHistory.getCoreErrorMessage())
                .transactionTime(transactionHistory.getTransactionTime())
                .productAccountType(EOpsIdType.lookup(transactionHistory.getMemberProductAccountType()))
                .productAccountCode(transactionHistory.getMemberProductAccountCode())
                .memberProfile(memberProfile)
                .transactionType(transactionHistory.getType())
                .adjustmentType(adjustmentType)
                .description(transactionHistory.getDescription())
                .channel(transactionHistory.getChannel())
                .service(transactionHistory.getServiceCode())
                .cancellation(EBoolean.of(transactionHistory.getCancellation()))
                .cancellationType(transactionHistory.getCancellationType())
                .cancelledAt(transactionHistory.getCancellationTime())
                .relatedInvoice(relatedInvoice)
                .gmv(transactionHistory.getGmv())
                .grossAmount(transactionHistory.getGrossAmount())
                .nettAmount(transactionHistory.getNettAmount())
                .retentionTime(transactionHistory.getAwardRetentionTime())
                .reasonCode(transactionHistory.getReasonCode())
                .reasonName(reasonName)
                .createdAt(transactionHistory.getCreatedAt())
                .createdBy(transactionHistory.getCreatedBy())
                .updatedAt(transactionHistory.getUpdatedAt())
                .updatedBy(transactionHistory.getUpdatedBy())
                .approvedAt(transactionHistory.getApprovedAt())
                .approvedBy(transactionHistory.getApprovedBy())
                .errorCode(transactionHistory.getCoreErrorCode())
                .errorMessage(transactionHistory.getCoreErrorMessage())
                .attributes(attributes)
                .pointAwardDetails(pointAwardDetails)
                .pointRedeemDetails(pointRedeemDetails)
                .pointTopUpDetails(pointTopUpDetails)
                .syncWithElastic(getSyncWithElastic(transactionHistories))
                .voucherDetailList(voucherDetailList)
                .isSyncElastic(Objects.nonNull(isSyncElasticRes) ? isSyncElasticRes : null)
                .build();
    }

    private void processTransferPoint(TransactionHistory transactionHistory, ETransferPointFrom fromEnum, String reasonName, List<TransactionDetailRes.PointAwardDetail> pointAwardDetails, List<TransactionDetailRes.PointRedeemDetail> pointRedeemDetails, TransactionHistory referenceTxnHistory) {
        Pool pool = poolService.find(transactionHistory.getPoolId()).orElse(null);
        RewardPool rewardPool = null;
        ShortEntityRes currencyEntity = null;
        Long memberReference = null;
        if(referenceTxnHistory != null){
            memberReference = referenceTxnHistory.getMemberId();
        }
        if (Objects.nonNull(pool)) {
            rewardPool = rewardPoolService.findById(pool.getRefId()).orElse(null);
            Optional<Currency> currency = currencyService.find(pool.getCurrencyId());
            if(currency.isPresent()) {
                currencyEntity = new ShortEntityRes(currency.get().getId(), currency.get().getName(), currency.get().getCode());
            }
        }

        if(fromEnum.equals(ETransferPointFrom.RECEIVER)) {
            TransactionDetailRes.PointAwardDetail pointAwardDetail = TransactionDetailRes.PointAwardDetail
                    .builder()
                    .point(Objects.nonNull(transactionHistory.getAwardPoint()) ? transactionHistory.getAwardPoint() : BigDecimal.ZERO)
                    .balanceBefore(transactionHistory.getBalanceBefore())
                    .balanceAfter(transactionHistory.getBalanceAfter())
                    .pool(new ShortEntityRes(rewardPool.getId(), rewardPool.getName(), rewardPool.getPoolCode()))
                    .currency(currencyEntity)
                    .awardRetentionTime(transactionHistory.getAwardRetentionTime())
                    .reasonCode(transactionHistory.getReasonCode())
                    .reasonName(reasonName)
                    .errorCode(transactionHistory.getCoreErrorCode())
                    .errorMessage(transactionHistory.getCoreErrorMessage())
                    .status(transactionHistory.getStatus())
                    .memberReference(memberReference)
                    .build();
            pointAwardDetails.add(pointAwardDetail);
        }else {
            TransactionDetailRes.PointRedeemDetail pointRedeemDetail = TransactionDetailRes.PointRedeemDetail
                    .builder()
                    .point(Objects.nonNull(transactionHistory.getRedeemPoint()) ? transactionHistory.getRedeemPoint() : BigDecimal.ZERO)
                    .balanceBefore(transactionHistory.getBalanceBefore())
                    .balanceAfter(transactionHistory.getBalanceAfter())
                    .pool(new ShortEntityRes(rewardPool.getId(), rewardPool.getName(), rewardPool.getPoolCode()))
                    .currency(currencyEntity)
                    .reasonCode(transactionHistory.getReasonCode())
                    .reasonName(reasonName)
                    .errorCode(transactionHistory.getCoreErrorCode())
                    .errorMessage(transactionHistory.getCoreErrorMessage())
                    .status(transactionHistory.getStatus())
                    .memberReference(memberReference)
                    .build();
            pointRedeemDetails.add(pointRedeemDetail);
        }
    }

    private TransactionHistory getTransactionHistory(List<TransactionHistory> transactionHistories, ETransferPointFrom from) {
        if (transactionHistories.get(0).getType().equals(ETransactionType.TRANSFER)) {
            if (ETransferPointFrom.RECEIVER.equals(from)) {
                // find the transaction history with award point > 0
                return transactionHistories.stream()
                        .filter(item -> item.getAwardPoint().compareTo(BigDecimal.ZERO) > 0)
                        .findFirst()
                        .orElse(null);
            } else {
                // return the first transaction history have award point = 0
                return transactionHistories.stream()
                        .filter(item -> item.getAwardPoint().equals(BigDecimal.ZERO))
                        .findFirst()
                        .orElse(null);
            }
        } else {
            return transactionHistories.get(0);
        }
    }

    private TransactionDetailRes.MemberProfile getMemberProfile(Member member, MemberStatus memberStatus) {
        return TransactionDetailRes.MemberProfile
                .builder()
                .memberId(member.getId())
                .memberCode(member.getMemberCode())
                .memberName(member.getFullName())
                .status(Objects.nonNull(memberStatus) ? new DropdownRes(memberStatus.getId(), memberStatus.getCode(), memberStatus.getViName(), memberStatus.getEnName()) : null)
                .phoneNo(member.getPhoneNo())
                .dob(member.getDob())
                .gender(member.getGender())
                .email(member.getEmail())
                .identifyType(member.getIdentifyType())
                .identifyNo(member.getIdentifyNo())
                .address(member.getAddress())
                .partnerCustomerId(member.getPartnerCustomerId())
                .build();
    }

    private EAdjustmentType getAdjustmentType(TransactionHistory transactionHistory) {
        if (ETransactionType.ADJUSTMENT.equals(transactionHistory.getType())) {
            return Objects.nonNull(transactionHistory.getAwardPoint())
                    && transactionHistory.getAwardPoint().compareTo(BigDecimal.ZERO) > 0
                    ? EAdjustmentType.AWARD : EAdjustmentType.REDEEM;
        } else {
            return null;
        }
    }

    private List<TransactionDetailRes.Attribute> getTransactionAttributes(String txnRefNo, Integer programId) {
        List<TransactionHistoryAttribute> txnHistoryAttributes =
                transactionHistoryAttributeRepository.findAllByTransactionRef(txnRefNo);

        List<String> attributes = txnHistoryAttributes
                .stream()
                .filter(item -> Objects.nonNull(item.getCode()))
                .map(TransactionHistoryAttribute::getCode)
                .collect(Collectors.toList());

        Map<String, ProgramTransactionAttribute> attributeMap =
                programTransactionAttributeService.findByProgramIdAndAttributeIn(programId, attributes)
                        .stream().collect(Collectors.toMap(
                                ProgramTransactionAttribute::getAttribute,
                                item -> item,
                                (k1, k2) -> k2
                        ));

        return txnHistoryAttributes
                .stream()
                .filter(item -> Objects.nonNull(item.getCode()))
                .map(item -> TransactionDetailRes.Attribute
                        .builder()
                        .attribute(item.getCode())
                        .value(item.getValue())
                        .dataType(nullSafer(attributeMap.get(item.getCode()), ProgramTransactionAttribute::getDataType))
                        .dataTypeDisplay(nullSafer(attributeMap.get(item.getCode()), ProgramTransactionAttribute::getDataTypeDisplay))
                        .status(ECommonStatus.ACTIVE)
                        .build())
                .collect(Collectors.toList());
    }

    private List<PWPTransactionRes.Attribute> getPWPTransactionAttributes(String txnRefNo, Integer programId) {
        List<TransactionHistoryAttribute> txnHistoryAttributes =
                transactionHistoryAttributeRepository.findAllByTransactionRef(txnRefNo);

        List<String> attributes = txnHistoryAttributes
                .stream()
                .filter(item -> Objects.nonNull(item.getCode()))
                .map(TransactionHistoryAttribute::getCode)
                .collect(Collectors.toList());

        Map<String, ProgramTransactionAttribute> attributeMap =
                programTransactionAttributeService.findByProgramIdAndAttributeIn(programId, attributes)
                        .stream().collect(Collectors.toMap(
                                ProgramTransactionAttribute::getAttribute,
                                item -> item,
                                (k1, k2) -> k2
                        ));

        return txnHistoryAttributes
                .stream()
                .filter(item -> Objects.nonNull(item.getCode()))
                .map(item -> PWPTransactionRes.Attribute
                        .builder()
                        .attribute(item.getCode())
                        .value(item.getValue())
                        .dataType(nullSafer(attributeMap.get(item.getCode()), ProgramTransactionAttribute::getDataType))
                        .dataTypeDisplay(nullSafer(attributeMap.get(item.getCode()), ProgramTransactionAttribute::getDataTypeDisplay))
                        .status(ECommonStatus.ACTIVE)
                        .build())
                .collect(Collectors.toList());
    }

    private List<TransactionDetailRes.PointAwardDetail> getPointAwardDetails(
            List<TransactionHistory> transactionHistories,
            Map<Integer, ShortEntityRes> poolMap,
            Map<Integer, Scheme> schemeMap,
            Map<Integer, ShortEntityRes> currencyMap,
            Map<Integer, Integer> currencyIdMapByPoolId,
            String reasonName) {
        return transactionHistories.stream()
                .filter(item -> (ETransactionType.ADJUSTMENT.equals(item.getType()) && BigDecimal.ZERO.compareTo(item.getAwardPoint()) < 0 && Objects.nonNull(item.getPoolId()))
                        || (!ETransactionType.ADJUSTMENT.equals(item.getType()) && Objects.nonNull(item.getSchemeId()) && item.getSchemeId() != 0 && ESchemeType.AWARD.equals(schemeMap.get(item.getSchemeId()).getSchemeType()) && Objects.nonNull(item.getPoolId())))
                .map(item -> {
                            Scheme scheme = schemeMap.get(item.getSchemeId());
                            SchemeRes schemeRes = null;
                            if (Objects.nonNull(scheme)) {
                                schemeRes = SchemeRes.builder()
                                        .id(scheme.getId())
                                        .code(scheme.getCode())
                                        .name(scheme.getName())
                                        .description(scheme.getDescription())
                                        .startDate(scheme.getStartDate())
                                        .endDate(scheme.getEndDate())
                                        .build();
                            }

                            Pool pool = poolService.find(poolMap.get(item.getPoolId()).getId()).orElse(null);
                            RewardPool rewardPool = null;
                            if (Objects.nonNull(pool)) {
                                rewardPool = rewardPoolService.findById(pool.getRefId()).orElse(null);
                            }

                            return TransactionDetailRes.PointAwardDetail
                                    .builder()
                                    .point(Objects.nonNull(item.getAwardPoint()) ? item.getAwardPoint() : BigDecimal.ZERO)
                                    .balanceBefore(item.getBalanceBefore())
                                    .balanceAfter(item.getBalanceAfter())
                                    .pool(new ShortEntityRes(rewardPool.getId(), rewardPool.getName(), rewardPool.getPoolCode()))
                                    .scheme(schemeRes)
                                    .currency(currencyMap.get(currencyIdMapByPoolId.get(item.getPoolId())))
                                    .awardRetentionTime(item.getAwardRetentionTime())
                                    .reasonCode(item.getReasonCode())
                                    .reasonName(reasonName)
                                    .errorCode(item.getCoreErrorCode())
                                    .errorMessage(item.getCoreErrorMessage())
                                    .status(item.getStatus())
                                    .build();
                        }
                ).collect(Collectors.toList());
    }

    private List<TransactionDetailRes.VoucherDetail> getVoucherDetails(
            List<TransactionHistory> transactionHistories,
            Map<Integer, ShortEntityRes> rewardPoolMap,
            Map<Integer, Scheme> schemeMap
    ) {
        return transactionHistories.stream()
                .filter(item -> (Objects.isNull(item.getPoolId()) && !StringUtils.isEmpty(item.getVoucherCode())))
                .map(item -> {
                            Scheme scheme = schemeMap.get(item.getSchemeId());

                            ShortEntityRes schemeRes = Objects.nonNull(scheme) ? new ShortEntityRes(scheme.getId(), scheme.getName(), scheme.getCode()) : null;

                            return TransactionDetailRes.VoucherDetail
                                    .builder()
                                    .scheme(schemeRes)
                                    .pool(rewardPoolMap.get(item.getRewardPoolId()))
                                    .errorCode(item.getCoreErrorCode())
                                    .errorMessage(item.getCoreErrorMessage())
                                    .code(item.getVoucherCode())
                                    .quantity(item.getVoucherQuantity())
                                    .status(item.getStatus())
                                    .revokeVoucherStatus(item.getRevokeVoucherStatus())
                                    .build();
                        }
                ).collect(Collectors.toList());
    }

    private List<TransactionDetailRes.PointTopUpDetail> getPointTopUpDetails(
            List<TransactionHistory> transactionHistories,
            Map<Integer, ShortEntityRes> poolMap,
            Map<Integer, Scheme> schemeMap,
            Map<Integer, ShortEntityRes> currencyMap,
            Map<Integer, Integer> currencyIdMapByPoolId,
            String reasonName) {
        return transactionHistories.stream()
                .filter(item -> Objects.nonNull(item.getSchemeId()) && item.getSchemeId() != 0
                        && ESchemeType.TOPUP.equals(schemeMap.get(item.getSchemeId()).getSchemeType())
                        && Objects.nonNull(item.getPoolId())
                )
                .map(item -> {
                            Scheme scheme = schemeMap.get(item.getSchemeId());
                            SchemeRes schemeRes = null;
                            if (Objects.nonNull(scheme)) {
                                schemeRes = SchemeRes.builder()
                                        .id(scheme.getId())
                                        .code(scheme.getCode())
                                        .name(scheme.getName())
                                        .description(scheme.getDescription())
                                        .startDate(scheme.getStartDate())
                                        .endDate(scheme.getEndDate())
                                        .build();
                            }

                            Pool pool = poolService.find(poolMap.get(item.getPoolId()).getId()).orElse(null);
                            RewardPool rewardPool = null;
                            if (Objects.nonNull(pool)) {
                                rewardPool = rewardPoolService.findById(pool.getRefId()).orElse(null);
                            }

                            return TransactionDetailRes.PointTopUpDetail
                                    .builder()
                                    .point(Objects.nonNull(item.getAwardPoint()) ? item.getAwardPoint() : BigDecimal.ZERO)
                                    .balanceBefore(item.getBalanceBefore())
                                    .balanceAfter(item.getBalanceAfter())
                                    .pool(new ShortEntityRes(rewardPool.getId(), rewardPool.getName(), rewardPool.getPoolCode()))
                                    .scheme(schemeRes)
                                    .currency(currencyMap.get(currencyIdMapByPoolId.get(item.getPoolId())))
                                    .awardRetentionTime(item.getAwardRetentionTime())
                                    .reasonCode(item.getReasonCode())
                                    .reasonName(reasonName)
                                    .errorCode(item.getCoreErrorCode())
                                    .errorMessage(item.getCoreErrorMessage())
                                    .status(item.getStatus())
                                    .build();
                        }
                ).collect(Collectors.toList());
    }

    private List<TransactionDetailRes.PointRedeemDetail> getPointRedeemDetails(
            List<TransactionHistory> transactionHistories,
            Map<Integer, ShortEntityRes> poolMap,
            Map<Integer, Scheme> schemeMap,
            Map<Integer, ShortEntityRes> currencyMap,
            Map<Integer, Integer> currencyIdMapByPoolId,
            String reasonName) {
        return transactionHistories.stream()
                .filter(item -> (ETransactionType.ADJUSTMENT.equals(item.getType()) && BigDecimal.ZERO.compareTo(item.getRedeemPoint()) < 0 && Objects.nonNull(item.getPoolId()))
                        || (Objects.nonNull(item.getSchemeId()) && item.getSchemeId() != 0 && ESchemeType.REDEEM.equals(schemeMap.get(item.getSchemeId()).getSchemeType()) && Objects.nonNull(item.getPoolId())))
                .map(item -> {
                            Scheme scheme = schemeMap.get(item.getSchemeId());
                            SchemeRes schemeRes = null;
                            if (Objects.nonNull(scheme)) {
                                schemeRes = SchemeRes.builder()
                                        .id(scheme.getId())
                                        .code(scheme.getCode())
                                        .name(scheme.getName())
                                        .description(scheme.getDescription())
                                        .startDate(scheme.getStartDate())
                                        .endDate(scheme.getEndDate())
                                        .build();
                            }

                            Pool pool = poolService.find(poolMap.get(item.getPoolId()).getId()).orElse(null);
                            RewardPool rewardPool = null;
                            if (Objects.nonNull(pool)) {
                                rewardPool = rewardPoolService.findById(pool.getRefId()).orElse(null);
                            }

                            return TransactionDetailRes.PointRedeemDetail
                                    .builder()
                                    .point(Objects.nonNull(item.getRedeemPoint()) ? item.getRedeemPoint() : BigDecimal.ZERO)
                                    .balanceBefore(item.getBalanceBefore())
                                    .balanceAfter(item.getBalanceAfter())
                                    .pool(new ShortEntityRes(rewardPool.getId(), rewardPool.getName(), rewardPool.getPoolCode()))
                                    .scheme(schemeRes)
                                    .currency(currencyMap.get(currencyIdMapByPoolId.get(item.getPoolId())))
                                    .reasonCode(item.getReasonCode())
                                    .reasonName(reasonName)
                                    .errorCode(item.getCoreErrorCode())
                                    .errorMessage(item.getCoreErrorMessage())
                                    .status(item.getStatus())
                                    .build();
                        }
                ).collect(Collectors.toList());
    }

    private EOpsTransactionType getTransactionType(ETransactionBatchType
                                                           transactionBatchType, List<TransactionHistory> transactionHistories, TransactionRequest transactionRequest) {
        switch (transactionBatchType) {
            case EARN:
                return EOpsTransactionType.EARN;
            case BURN:
                return EOpsTransactionType.BURN;
            case SALE:
                return EOpsTransactionType.SALE;
            case ADJUST:
                return EOpsTransactionType.ADJUSTMENT;
            case REVERT_FULL:
                return getTransactionType(getTransactionPoints(transactionHistories));
            case REVERT_PARTIAL: {
                int check = transactionRequest.getRedeemPoint().compareTo(BigDecimal.ZERO);
                if (check == 1) {
                    return EOpsTransactionType.SALE;
                } else if (check == 0) {
                    return EOpsTransactionType.EARN;
                }
            }
            case REVERT_POINT:
                return getTransactionType(getTransactionPoints(transactionHistories));
        }
        return null;
    }

    private EBoolean getSyncWithElastic(List<TransactionHistory> txnHistories) {
        for (TransactionHistory txnHistory : txnHistories) {
            if (Objects.isNull(txnHistory.getSyncWithElastic())
                    || !ESyncWithElastic.YES.equals(txnHistory.getSyncWithElastic())) {
                return EBoolean.NO;
            }
        }
        return EBoolean.YES;
    }

    private void sendEmail(TransactionBatchRequest request) {
        MakerCheckerInternalSendEmailReq req = MakerCheckerInternalSendEmailReq.builder()
                .id(String.valueOf(request.getId()))
                .status(request.getApprovalStatus().name())
                .madeReason(request.getMadeReason())
                .madeByUserName(request.getCreatedBy())
                .createdAt(request.getCreatedAt())
                .build();

        makerCheckerInternalFeignClient.sendEmailDefault(EMakerCheckerType.TRANSACTION_REQUEST, req);
    }

    @Override
    public PWPTransactionRes getPWPTransactionInfoDetail(String postPurchaseRefNo) {
        PayWithPointTransactionHistory payWithPointTransactionHistory = payWithPointTransactionService.findByPostPurchaseRefNo(postPurchaseRefNo).orElseThrow(() -> new BusinessException(ErrorCode.PAY_WITH_POINT_TRANSACTION_NOT_FOUND, null, null));

        Program program = nullSafer(payWithPointTransactionHistory.getProgramId(),
                programId -> programService.find(programId).orElse(null));

        Corporation corporation = nullSafer(payWithPointTransactionHistory.getCorporationId(),
                corporationId -> corporationService.find(corporationId).orElse(null));

        Chain chain = nullSafer(payWithPointTransactionHistory.getChainId(),
                chainId -> chainService.find(chainId).orElse(null));

        Store store = nullSafer(payWithPointTransactionHistory.getStoreId(),
                storeId -> storeService.find(storeId));

        Pos terminal = nullSafer(payWithPointTransactionHistory.getPosId(),
                posId -> posService.find(posId).orElse(null));

        Member member = nullSafer(payWithPointTransactionHistory.getMemberId(),
                memberId -> memberService.find(memberId).orElse(null));

        Scheme scheme = nullSafer(payWithPointTransactionHistory.getSchemeId(),
                schemeId -> schemeService.find(schemeId).orElse(null));

        Pool pool = nullSafer(payWithPointTransactionHistory.getPoolId(),
                poolId -> poolService.find(poolId).orElse(null));

        MemberStatus memberStatus = memberStatusService.findByProgramIdAndCode(program.getId(), member.getStatus()).orElse(null);

        Currency currency;
        if (Objects.nonNull(pool)) {
            currency = nullSafer(pool.getCurrencyId(),
                    currencyId -> currencyService.find(currencyId).orElse(null));
        } else {
            currency = null;
        }

        List<PWPTransactionRes.Attribute> attributes = getPWPTransactionAttributes(payWithPointTransactionHistory.getTransactionRef(), payWithPointTransactionHistory.getProgramId());

        return PWPTransactionRes.valueOfDetail(
                payWithPointTransactionHistory,
                member,
                memberStatus,
                scheme,
                pool,
                program,
                chain,
                store,
                terminal,
                corporation,
                currency,
                attributes
        );
    }

    @Override
    public List<RefundTransactionHistoryRes> getRefundTransactionHistory(String txnRefNo) {
        List<Object[]> refundTransactionHistories = refundTransactionHistoryService.findByTransactionRef(txnRefNo);
        return refundTransactionHistories.stream().map(i -> {
            RefundTransactionHistory refundTransactionHistory = (RefundTransactionHistory) i[0];
            Pool pool = (Pool) i[1];
            return RefundTransactionHistoryRes.builder()
                    .transactionRef(refundTransactionHistory.getTransactionRef())
                    .pool(new DropdownRes(pool.getRefId(), pool.getCode(), pool.getName(), null))
                    .refundPoint(refundTransactionHistory.getRefundPoint())
                    .actualRefundPoint(refundTransactionHistory.getActualRefundPoint())
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public Page<TransferPointTransactionRes> getTransferPointTransactionList(Integer programId, String invoiceNo, String txnRefNo,
                                                       EOpsIdType accountType, String accountCode, Date txnTimeStart,
                                                       Date txnTimeEnd, ETransactionStatus status, Pageable pageable) {
        // Build the transaction search request
        TransactionSearchReq searchReq = TransactionSearchReq.builder()
                .programId(programId)
                .invoiceNumber(invoiceNo)
                .tnxRefNo(txnRefNo)
                .accountType(accountType)
                .accountCode(accountCode)
                .transactionType(ETransactionType.TRANSFER)
                .status(status)
                .build();

        // Set date range if provided
        if (txnTimeStart != null) {
            searchReq.setTransactionFrom(txnTimeStart.getTime()/ 1000);
        } else {
            searchReq.setTransactionFrom(0L);
        }

        if (txnTimeEnd != null) {
            searchReq.setTransactionTo(txnTimeEnd.getTime()/ 1000);
        } else {
            searchReq.setTransactionTo(System.currentTimeMillis());
        }
        Pageable pageRequest = new OffsetBasedPageRequest((int) pageable.getOffset(), pageable.getPageSize(), null);
        // Search for transfer transactions using the existing search method
        Page<TransactionRes> transactionResPage = search(searchReq, pageRequest, null);

        // Convert the list of TransactionRes to TransferPointTransactionRes
        return transactionResPage.map(this::convertToTransferPointTransactionRes);
    }

    private TransferPointTransactionRes convertToTransferPointTransactionRes(TransactionRes transactionRes) {
        TransferPointTransactionRes result = new TransferPointTransactionRes();
        
        result.setInvoiceNo(transactionRes.getInvoiceNo());
        result.setTxnRefNo(transactionRes.getTransactionRef());
        result.setMemberId(transactionRes.getMemberId());
        result.setMemberCode(transactionRes.getMemberCode());
        result.setAccountCode(transactionRes.getAccountCode());
        result.setAccountType(transactionRes.getAccountType());
        result.setTransactionTime(transactionRes.getTransactionTime());
        result.setRedeemPoint(transactionRes.getRedeemPoint() != null ? new BigDecimal(transactionRes.getRedeemPoint()) : null);
        result.setAwardPoint(transactionRes.getAwardPoint() != null ? new BigDecimal(transactionRes.getAwardPoint()) : null);
        result.setStatus(transactionRes.getStatus());
        result.setCoreErrorMessage(transactionRes.getCoreErrorMessage());

        // Determine if this is a sender or receiver transaction
        ETransferPointFrom transferPointFrom = null;
        if(result.getAwardPoint().compareTo(BigDecimal.ZERO) > 0) {
            result.setFrom(ETransferPointFrom.RECEIVER);
            transferPointFrom = ETransferPointFrom.RECEIVER;
        } else if(result.getRedeemPoint().compareTo(BigDecimal.ZERO) > 0) {
            result.setFrom(ETransferPointFrom.SENDER);
            transferPointFrom = ETransferPointFrom.SENDER;
        }
        
        // Set program info
        if (transactionRes.getProgram() != null) {
            result.setProgram(transactionRes.getProgram());
        }
        
        // Set pool info if available
        if (transactionRes.getPoolShort() != null) {
            result.setPoolShort(transactionRes.getPoolShort());
            result.setPool(new ShortEntityRes(
                    transactionRes.getPoolShort().getId(),
                    transactionRes.getPoolShort().getCode(),
                    transactionRes.getPoolShort().getName()));
        }
        
        // Create pointTransferDetails based on transaction type
        if (transferPointFrom != null) {
            List<TransferPointTransactionRes.PointTransferDetail> pointTransferDetails = new ArrayList<>();
            String txnRefNo = transactionRes.getTransactionRef();

            // First, add the current transaction's details
            TransferPointTransactionRes.PointTransferDetail currentPointTransferDetail = TransferPointTransactionRes.PointTransferDetail.builder()
                .from(transferPointFrom)
                .memberCode(transactionRes.getMemberCode())
                .memberId(transactionRes.getMemberId())
                .point(transferPointFrom == ETransferPointFrom.SENDER ?
                       (result.getRedeemPoint() != null ? result.getRedeemPoint() : BigDecimal.ZERO) :
                       (result.getAwardPoint() != null ? result.getAwardPoint() : BigDecimal.ZERO))
                .transactionTime(transactionRes.getTransactionTime())
                .build();

            pointTransferDetails.add(currentPointTransferDetail);

            // Then get the other side of the transaction
            if(transactionRes.getStatus().equals(ETransactionStatus.SUCCESS.getValue())) {
                try {
                    ETransferPointFrom otherSide = (transferPointFrom == ETransferPointFrom.SENDER) ?
                            ETransferPointFrom.RECEIVER : ETransferPointFrom.SENDER;

                    TransactionDetailRes otherSideDetail = getTransactionInfoDetail(txnRefNo, otherSide.getValue());

                    if (otherSideDetail != null) {
                        TransferPointTransactionRes.PointTransferDetail otherPointTransferDetail = null;

                        // Based on the other side, extract the appropriate details
                        if (otherSide == ETransferPointFrom.RECEIVER && otherSideDetail.getPointAwardDetails() != null
                                && !otherSideDetail.getPointAwardDetails().isEmpty()) {
                            // Get receiver details from point award
                            TransactionDetailRes.PointAwardDetail awardDetail = otherSideDetail.getPointAwardDetails().get(0);

                            otherPointTransferDetail = TransferPointTransactionRes.PointTransferDetail.builder()
                                    .from(ETransferPointFrom.RECEIVER)
                                    .memberCode(otherSideDetail.getMemberProfile() != null ?
                                            otherSideDetail.getMemberProfile().getMemberCode() : null)
                                    .memberId(otherSideDetail.getMemberProfile() != null ?
                                            otherSideDetail.getMemberProfile().getMemberId() : null)
                                    .point(awardDetail.getPoint())
                                    .balanceBefore(awardDetail.getBalanceBefore())
                                    .balanceAfter(awardDetail.getBalanceAfter())
                                    .pool(awardDetail.getPool())
                                    .transactionTime(otherSideDetail.getTransactionTime())
                                    .status(otherSideDetail.getStatus())
                                    .build();
                        } else if (otherSide == ETransferPointFrom.SENDER && otherSideDetail.getPointRedeemDetails() != null
                                && !otherSideDetail.getPointRedeemDetails().isEmpty()) {
                            // Get sender details from point redeem
                            TransactionDetailRes.PointRedeemDetail redeemDetail = otherSideDetail.getPointRedeemDetails().get(0);

                            otherPointTransferDetail = TransferPointTransactionRes.PointTransferDetail.builder()
                                    .from(ETransferPointFrom.SENDER)
                                    .memberCode(otherSideDetail.getMemberProfile() != null ?
                                            otherSideDetail.getMemberProfile().getMemberCode() : null)
                                    .memberId(otherSideDetail.getMemberProfile() != null ?
                                            otherSideDetail.getMemberProfile().getMemberId() : null)
                                    .point(redeemDetail.getPoint())
                                    .balanceBefore(redeemDetail.getBalanceBefore())
                                    .balanceAfter(redeemDetail.getBalanceAfter())
                                    .pool(redeemDetail.getPool())
                                    .transactionTime(otherSideDetail.getTransactionTime())
                                    .status(otherSideDetail.getStatus())
                                    .build();
                        }

                        if (otherPointTransferDetail != null) {
                            pointTransferDetails.add(otherPointTransferDetail);
                        }
                    }
                } catch (Exception e) {
                    throw new BusinessException(ErrorCode.SERVER_ERROR, e.getMessage(), null);
                }
            }

            result.setPointTransferDetails(pointTransferDetails);
        }

        return result;
    }

    @Override
    public TransferPointTransactionRes getTransferPointTransactionInfoDetail(String txnRefNo) {
        // Convert the transaction detail to transfer point transaction detail using "SENDER" as the from parameter
        TransactionDetailRes transactionDetailSenderRes = getTransactionInfoDetail(txnRefNo, ETransferPointFrom.SENDER.getValue());
        
        // Create and populate a new TransferPointTransactionRes object from the transaction detail
        TransferPointTransactionRes result = new TransferPointTransactionRes();
        
        // Set basic transaction information
        result.setTxnRefNo(transactionDetailSenderRes.getTxnRefNo());
        result.setInvoiceNo(transactionDetailSenderRes.getInvoiceNo());
        result.setTransactionTime(transactionDetailSenderRes.getTransactionTime());
        result.setCoreErrorMessage(transactionDetailSenderRes.getCoreErrorMessage());
        
        // Set program information
        if (transactionDetailSenderRes.getProgram() != null) {
            result.setProgram(transactionDetailSenderRes.getProgram());
        }
        
        // Set member information if available
        if (transactionDetailSenderRes.getMemberProfile() != null) {
            result.setMemberId(transactionDetailSenderRes.getMemberProfile().getMemberId());
            result.setMemberCode(transactionDetailSenderRes.getMemberProfile().getMemberCode());
            result.setAccountType(transactionDetailSenderRes.getProductAccountType());
            result.setAccountCode(transactionDetailSenderRes.getProductAccountCode());
        }
        
        // Set status information
        if (transactionDetailSenderRes.getStatus() != null) {
            // Convert ETransactionStatus to String status
            result.setStatus(transactionDetailSenderRes.getStatus().name());
        }
        
        // Set creation/update information
        result.setCreatedAt(transactionDetailSenderRes.getCreatedAt());
        result.setCreatedBy(transactionDetailSenderRes.getCreatedBy());
        result.setUpdatedAt(transactionDetailSenderRes.getUpdatedAt());
        result.setUpdatedBy(transactionDetailSenderRes.getUpdatedBy());
        result.setApprovedAt(transactionDetailSenderRes.getApprovedAt());
        result.setApprovedBy(transactionDetailSenderRes.getApprovedBy());

        List<TransferPointTransactionRes.PointTransferDetail> pointTransferDetails = new ArrayList<>();
        
        // Add sender information to pointTransferDetails from pointRedeemDetails
        if (transactionDetailSenderRes.getPointRedeemDetails() != null && !transactionDetailSenderRes.getPointRedeemDetails().isEmpty()) {
            TransactionDetailRes.PointRedeemDetail redeemDetail = transactionDetailSenderRes.getPointRedeemDetails().get(0);
            
            // Create sender point transfer detail
            TransferPointTransactionRes.PointTransferDetail senderPointTransferDetail = TransferPointTransactionRes.PointTransferDetail.builder()
                .from(ETransferPointFrom.SENDER)
                .txnRefNo(transactionDetailSenderRes.getTxnRefNo())
                .memberCode(transactionDetailSenderRes.getMemberProfile() != null ? 
                    transactionDetailSenderRes.getMemberProfile().getMemberCode() : null)
                .memberId(transactionDetailSenderRes.getMemberProfile() != null ?
                        transactionDetailSenderRes.getMemberProfile().getMemberId() : null)
                .point(redeemDetail.getPoint()  )
                .balanceBefore(redeemDetail.getBalanceBefore())
                .balanceAfter(redeemDetail.getBalanceAfter())
                .pool(redeemDetail.getPool())
                .transactionTime(transactionDetailSenderRes.getTransactionTime())
                .status(transactionDetailSenderRes.getStatus())
                .coreErrorMessage(transactionDetailSenderRes.getCoreErrorMessage())
                .build();
            
            pointTransferDetails.add(senderPointTransferDetail);
            
            // Set pool information if available
            if (redeemDetail.getPool() != null) {
                result.setPoolShort(new PoolShortRes(
                    redeemDetail.getPool().getId(),
                    redeemDetail.getPool().getCode(),
                    redeemDetail.getPool().getName(),
                    null, null, null, null));
                
                result.setPool(redeemDetail.getPool());
            }
            
            // If sender's transaction status is success, get receiver information
            if (transactionDetailSenderRes.getStatus() != null && 
                transactionDetailSenderRes.getStatus().equals(ETransactionStatus.SUCCESS)) {
                
                // Get receiver information
                TransactionDetailRes transactionDetailReceiverRes = getTransactionInfoDetail(txnRefNo, 
                    ETransferPointFrom.RECEIVER.getValue());
                
                // Add receiver information to pointTransferDetails from pointAwardDetails
                if (transactionDetailReceiverRes != null && 
                    transactionDetailReceiverRes.getPointAwardDetails() != null && 
                    !transactionDetailReceiverRes.getPointAwardDetails().isEmpty()) {
                    
                    TransactionDetailRes.PointAwardDetail awardDetail = transactionDetailReceiverRes.getPointAwardDetails().get(0);
                    
                    // Create receiver point transfer detail
                    TransferPointTransactionRes.PointTransferDetail receiverPointTransferDetail = TransferPointTransactionRes.PointTransferDetail.builder()
                        .from(ETransferPointFrom.RECEIVER)
                        .txnRefNo(transactionDetailReceiverRes.getTxnRefNo())
                        .memberCode(transactionDetailReceiverRes.getMemberProfile() != null ? 
                            transactionDetailReceiverRes.getMemberProfile().getMemberCode() : null)
                        .memberId(transactionDetailSenderRes.getMemberProfile() != null ?
                                transactionDetailSenderRes.getMemberProfile().getMemberId() : null)
                        .point(awardDetail.getPoint())
                        .balanceBefore(awardDetail.getBalanceBefore())
                        .balanceAfter(awardDetail.getBalanceAfter())
                        .pool(awardDetail.getPool())
                        .transactionTime(transactionDetailReceiverRes.getTransactionTime())
                        .status(transactionDetailReceiverRes.getStatus())
                        .coreErrorMessage(transactionDetailReceiverRes.getCoreErrorMessage())
                        .build();
                    
                    pointTransferDetails.add(receiverPointTransferDetail);
                }
            }
        }
        
        // Set the pointTransferDetails in the result
        result.setPointTransferDetails(pointTransferDetails);
        
        return result;
    }
}