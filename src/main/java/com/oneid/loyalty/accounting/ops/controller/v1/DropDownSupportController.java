package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.loyalty.accounting.ops.constant.EOpsFunctionCode;
import com.oneid.loyalty.accounting.ops.constant.EStatus;
import com.oneid.loyalty.accounting.ops.model.req.DropdownSearchMultipleParentReq;
import com.oneid.loyalty.accounting.ops.model.req.MerchantDetailReq;
import com.oneid.loyalty.accounting.ops.model.res.DropdownRes;
import com.oneid.loyalty.accounting.ops.service.OpsDropDownService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterLevelType;
import com.oneid.oneloyalty.common.constant.ECounterRuleType;
import com.oneid.oneloyalty.common.constant.EMerchantServiceType;
import com.oneid.oneloyalty.common.constant.EMerchantType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Base64;
import java.util.List;

@RestController
@RequestMapping("v1/list-info")
public class DropDownSupportController extends BaseController {

    @Autowired
    private OpsDropDownService dropDownService;

    @GetMapping("business")
    public ResponseEntity<?> getAll() {
        return success(dropDownService.business(ECommonStatus.ACTIVE));
    }

    @GetMapping("corporation/business/{business_id}")
    public ResponseEntity<?> getAllCorporation(@PathVariable(value = "business_id") Integer businessId) {
        return success(dropDownService.corporation(businessId, ECommonStatus.ACTIVE));
    }

    @GetMapping("corporation/program-id/{program_id}")
    public ResponseEntity<?> getActiveCorporations(
            @PathVariable(value = "program_id") Integer programId
    ) {
        return success(dropDownService.getActiveCorporationsByProgramId(programId));
    }

    @GetMapping("transaction-code/program-id/{program_id}/type/{type}")
    public ResponseEntity<?> getActiveTransactionCodes(
            @PathVariable(value = "program_id") Integer programId,
            @PathVariable(value = "type", required = false) String type,
            @RequestParam(value = "status", required = false) ECommonStatus status
    ) {
        return success(dropDownService.getActiveTransactionCodesByProgramId(programId, type, status));
    }

    @GetMapping("chain/business/{business_code}/corporation/{corporation_code}")
    public ResponseEntity<?> getAllChain(
            @PathVariable(value = "business_code") String businessCode,
            @PathVariable(value = "corporation_code") String corporationCode) {
        return success(dropDownService.getActivatedChainsByCorporationCode(businessCode, corporationCode, ECommonStatus.ACTIVE));
    }

    @GetMapping("chain/corporation/{corporation_id}")
    public ResponseEntity<?> getAllChain(
            @PathVariable(value = "corporation_id") Integer corporationId,
            @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(dropDownService.getActivatedChainsByCorporationId(corporationId, status));
    }

    @GetMapping("store")
    public ResponseEntity<?> getActiveStores(
            @RequestParam(value = "chain_id", required = false) Integer chainId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "code_prefix", required = false) String codePrefix
    ) {
        return success(dropDownService.getActiveStoresByChainAndProgram(chainId, programId, codePrefix));
    }

    @GetMapping("store/chain/{chain_id}")
    public ResponseEntity<?> getAllStore(@PathVariable(value = "chain_id") Integer chainId,
                                         @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(dropDownService.store(chainId, status));
    }

    @GetMapping("terminal/store/{store_id}")
    public ResponseEntity<?> getAllTerminal(@PathVariable(value = "store_id") Integer storeId,
                                            @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(dropDownService.terminal(storeId, status));
    }

    @GetMapping("card-policy/business/{business_id}/card-policy-type/{card_policy_type}")
    public ResponseEntity<?> getAllCardPolicy(@PathVariable(value = "business_id") Integer businessId,
                                              @PathVariable(value = "card_policy_type") ECardPolicyType cardPolicyType) {
        return success(dropDownService.cardPolicy(businessId, cardPolicyType));
    }

    @GetMapping("program/business/{business_id}")
    public ResponseEntity<?> getAllProgramByBusiness(@PathVariable(value = "business_id") Integer businessId) {
        return success(dropDownService.program(businessId));
    }

    @GetMapping("programs/{program_id}/corporations")
    public ResponseEntity<?> getCorporationsByProgram(@PathVariable(value = "program_id") Integer programId,
                                                      @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(dropDownService.corporationByProgram(programId, status));
    }

    @GetMapping("gift-card-bin")
    public ResponseEntity<?> getAllGCBinByBusiness(
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId) {
        return success(dropDownService.gcBinByBusinessAndProgram(businessId, programId));
    }

    @GetMapping("gift-card-type")
    public ResponseEntity<?> getAllGCTypeByBusiness(
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId) {
        return success(dropDownService.gcTypeByBusinessAndProgram(businessId, programId));
    }

    @GetMapping("card-type/business/{business_id}/program/{program_id}")
    public ResponseEntity<?> getCardTypes(
            @PathVariable(value = "business_id") Integer businessId,
            @PathVariable(value = "program_id") Integer programId) {
        return success(dropDownService.getCardTypesByBusinessAndProgramId(businessId, programId));
    }

    @GetMapping("currency-for-transaction")
    public ResponseEntity<?> getCurrencyForTransaction(@RequestParam(value = "business_id", required = true) Integer businessId) {
        return success(dropDownService.currencyForTransaction(businessId));
    }

    @GetMapping("base-currency-for-transaction")
    public ResponseEntity<?> getBaseCurrencyForTransaction(@RequestParam(value = "business_id", required = true) Integer businessId) {
        return success(dropDownService.baseCurrencyForTransaction(businessId));
    }

    @GetMapping("/tier/program/{program_id}")
    public ResponseEntity<?> getTiers(@PathVariable(value = "program_id") Integer programId,
                                      @RequestParam(value = "direction", required = false, defaultValue = "ASC") Direction direction
    ) {
        Sort sort = Sort.by(direction, "rankNo");
        return success(dropDownService.getActivatedTiers(programId, sort));
    }

    @GetMapping("/reason-code/business/{businessId}/program/{programId}")
    public ResponseEntity<?> getActiveReasonCodes(
            @PathVariable(value = "businessId") Integer businessId,
            @PathVariable(value = "programId") Integer programId,
            @RequestParam(value = "function_code", required = false) EOpsFunctionCode functionCode) {
        return success(dropDownService.getActiveReasonCodes(businessId, programId, functionCode));
    }

    @GetMapping("/reason-code/program/{programId}")
    public ResponseEntity<?> getAllReasonCodes(
            @PathVariable(value = "programId") Integer programId,
            @RequestParam(value = "function_code", required = false) EOpsFunctionCode functionCode,
            @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(dropDownService.getAllReasonCodes(programId, functionCode, status));
    }

    @PostMapping("chains/search")
    public ResponseEntity<?> searchChain(
            @RequestBody DropdownSearchMultipleParentReq req) {
        return success(dropDownService.searchChains(req.getParentIds(), req.getNameOrCode()));
    }

    @PostMapping("stores/search")
    public ResponseEntity<?> searchStore(
            @RequestBody DropdownSearchMultipleParentReq req) {
        return success(dropDownService.searchStores(req.getParentIds(), req.getNameOrCode()));
    }

    @PostMapping("terminals/search")
    public ResponseEntity<?> searchTerminal(
            @RequestBody DropdownSearchMultipleParentReq req) {
        return success(dropDownService.searchTerminals(req.getParentIds(), req.getNameOrCode()));
    }

    @GetMapping("/programs/business/{businessId}")
    public ResponseEntity<?> getActivePrograms(
            @PathVariable(value = "businessId") Integer businessId,
            @RequestParam(value = "exclude_program_id", required = false) List<Integer> excludeProgramIds) {
        return success(dropDownService.getActivePrograms(businessId, excludeProgramIds));
    }

    @GetMapping("/tier-policy/business/{businessId}/program/{programId}")
    public ResponseEntity<?> getActiveTierPolicies(
            @PathVariable(value = "businessId") Integer businessId,
            @PathVariable(value = "programId") Integer programId) {
        return success(dropDownService.getActiveTierPolicies(businessId, programId));
    }

    @GetMapping("/events")
    ResponseEntity<?> getMessageEvents() {
        return success(dropDownService.getMessageEvent());
    }

    @GetMapping("/partner-supplier/business/{businessId}")
    public ResponseEntity<?> getActivatedPartnerSuppliers(@PathVariable(value = "businessId") Integer businessId) {
        return success(dropDownService.getActivatedPartnerSuppliers(businessId));
    }

    @GetMapping("/currencies/business/{business_code}")
    public ResponseEntity<?> getActivatedCurrenciesByBusinessCode(@PathVariable(value = "business_code") String businessCode) {
        return success(dropDownService.getActivatedCurrenciesByBusinessCode(businessCode));
    }

    @GetMapping("/currencies")
    public ResponseEntity<?> getActivatedCurrenciesByBusinessCode() {
        return success(dropDownService.getActivatedCurrenciesByBusinessCode(null));
    }

    @GetMapping("/activated-vouchers")
    public ResponseEntity<?> getActivatedVouchers(
            @RequestParam(name = "keyword", defaultValue = "", required = false) String keyword,
            @RequestParam(name = "page", defaultValue = "0") Integer page,
            @RequestParam(name = "size", defaultValue = "100") Integer limit,
            @RequestParam(name = "status", defaultValue = "ACTIVE") String status,
            @RequestParam(name = "program_id") Integer programId
    ) {
        return success(dropDownService.getActivatedVouchers(programId, status, keyword, page, limit));
    }

    @GetMapping("/voucher-sale")
    public ResponseEntity<?> getVoucher(@RequestParam(value = "offset", defaultValue = "0") Integer offset,
                                        @RequestParam(value = "limit", defaultValue = "20") Integer limit,
                                        @RequestParam(value = "keyword", required = false) String keyword) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<AttributeCombobox> result = dropDownService.getSaleIntegration(keyword, pageRequest);
        return success(result.getContent(), offset, limit, (int) result.getTotalElements());
    }

    @GetMapping("store/corporation/{id}")
    public ResponseEntity<?> getActivatedStoresByCorporationId(@PathVariable(value = "id") Integer corporationId) {
        return success(dropDownService.geActivatedtStoresByCorporationId(corporationId));
    }

    @PostMapping("/merchant-detail")
    public ResponseEntity<?> getDetail(@RequestBody MerchantDetailReq req) {
        return success(dropDownService.getMerchantDetail(req));
    }

    @GetMapping("campaign/program-id/{program_id}")
    public ResponseEntity<?> getAllCampaign(@PathVariable(value = "program_id") Integer programId,
                                            @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(dropDownService.getAllCampaign(programId, status));
    }

    @GetMapping("member-status/program-id/{program_id}")
    public ResponseEntity<?> getAllMemberStatus(@PathVariable(value = "program_id") Integer programId,
                                                @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(dropDownService.getAllMemberStatus(programId, status));
    }

    @GetMapping("/partner")
    public ResponseEntity<?> getPartners(
            @RequestParam(value = "merchant_service_type", required = false) EMerchantServiceType merchantServiceType,
            @RequestParam(value = "merchant_type", required = false) EMerchantType merchantType,
            @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(dropDownService.getPartner(merchantServiceType, merchantType, status));
    }

    @GetMapping("/counter")
    public ResponseEntity<?> getCounters(
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "service_type", required = false) EServiceType serviceType,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "counter_rule_type", required = false) ECounterRuleType counterRuleType
    ) {
        return success(dropDownService.getCounters(programId, serviceType, status, counterRuleType));
    }

    @GetMapping("/counter/level")
    public ResponseEntity<?> getCounterLevels(
            @RequestParam(value = "program_id") Integer programId,
            @RequestParam(value = "counter_level_type") ECounterLevelType counterLevelType,
            @RequestParam(value = "status") ECommonStatus status) {
        return success(dropDownService.getCounterLevels(programId, counterLevelType, status));
    }

    @GetMapping("/cpm-transactions/service-code")
    public ResponseEntity<?> getServiceCodeConfig(
            @RequestParam(value = "program") String program,
            @RequestParam(value = "mapping_program") String mappingProgram
    ) {
        return success(dropDownService.getServiceCodeConfig(program, mappingProgram));
    }
}