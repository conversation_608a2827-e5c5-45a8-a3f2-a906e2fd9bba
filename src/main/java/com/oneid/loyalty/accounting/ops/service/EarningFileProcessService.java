package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.res.EarningFileProcessRes;
import com.oneid.oneloyalty.common.constant.EDataFileType;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import org.springframework.data.domain.Page;

import java.util.Date;

public interface EarningFileProcessService {

    Page<EarningFileProcessRes> getListAvailable(String name,
                                                 EProcessingStatus status,
                                                 EFileType fileType,
                                                 Date createStartDate,
                                                 Date createEndDate,
                                                 Date updateStartDate,
                                                 Date updateEndDate,
                                                 Integer offset,
                                                 Integer limit);

    Page<EarningFileProcessRes> getListDetail(EProcessingStatus status,
                                              EDataFileType fileType,
                                              Boolean totalFailedRecord,
                                              Integer offset,
                                              Integer limit,
                                              Boolean sortStatus,
                                              Long id);
}
