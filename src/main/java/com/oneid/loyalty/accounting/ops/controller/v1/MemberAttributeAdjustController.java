package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberAttributeAdjustReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifyMemberAttributeAdjustReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberAttributeAdjustDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberAttributeAdjustRes;
import com.oneid.loyalty.accounting.ops.service.MemberAttributeAdjustService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.RequestPojo;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ERequestProcessStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.beans.PropertyEditorSupport;
import java.io.IOException;
import java.util.Date;
import java.util.Objects;

@RestController
@RequestMapping("v1/member-attribute-adjust")
@Validated
public class MemberAttributeAdjustController extends BaseController {

    @Autowired
    OpsCommonExcelService opsCommonExcelService;

    @Autowired
    MemberAttributeAdjustService memberAttributeAdjustService;

    /**
     * Custom property editor to handle EBoolean enum conversion
     * This allows Spring to convert string values like "N" and "Y" to EBoolean enum values
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.registerCustomEditor(EBoolean.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) throws IllegalArgumentException {
                if (text == null || text.trim().isEmpty()) {
                    setValue(null);
                } else {
                    EBoolean value = EBoolean.of(text.trim());
                    if (value == null) {
                        throw new IllegalArgumentException("Invalid EBoolean value: " + text);
                    }
                    setValue(value);
                }
            }
        });
    }

    @GetMapping("/template")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_ADJUST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> getTemplateRequest(@RequestParam(name = "is_multiple_attribute") EBoolean isMultipleAttribute) throws IOException {
        if (EBoolean.YES.equals(isMultipleAttribute)) {
            return opsCommonExcelService.getTemplate(OPSConstant.ATTRIBUTE, "member-attribute-adjust-n-template");
        } else {
            return opsCommonExcelService.getTemplate(OPSConstant.ATTRIBUTE, "member-attribute-adjust-1-template");
        }
    }

    @PostMapping("/verify")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_ADJUST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> verifyExcel(
            @RequestPojo(name = "content", errorCode = ErrorCode.PAYLOAD_MUST_NOT_BE_NULL) @Valid VerifyMemberAttributeAdjustReq request,
            @RequestPart(name = "file") MultipartFile multipartFile
    ) throws Exception {
        ResourceDTO dto = memberAttributeAdjustService.verifyExcel(request, multipartFile);

        return dto != null ? new ResponseEntity<>(
                dto.getResource(),
                OpsCommonExcelService.setHeaderForFile(multipartFile.getOriginalFilename()),
                HttpStatus.BAD_REQUEST
        ) : success(null);
    }

    @PostMapping("/create")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_ADJUST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> create(
            @RequestPojo(name = "content", errorCode = ErrorCode.PAYLOAD_MUST_NOT_BE_NULL) @Valid CreateMemberAttributeAdjustReq req,
            @RequestPart(name = "file") MultipartFile multipartFile
    ) throws Exception {
        ResourceDTO dto = memberAttributeAdjustService.create(req, multipartFile);
        return Objects.nonNull(dto.getResource()) ? new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(multipartFile.getOriginalFilename()), HttpStatus.BAD_REQUEST)
                : success(Integer.parseInt(dto.getFilename()));
    }

    @GetMapping("/attributes")
    public ResponseEntity<?> getAttributes(@RequestParam("program_id") Integer programId) {
        return success(memberAttributeAdjustService.getAttributeByProgramId(programId));
    }

    @GetMapping(value = "in-review")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_ADJUST, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviews(
            @RequestParam(value = "program_id") Integer programId,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "created_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date createdStart,
            @RequestParam(value = "created_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date createdEnd,
            @RequestParam(value = "approved_by", required = false) String approvedBy,
            @RequestParam(value = "approved_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date approvedStart,
            @RequestParam(value = "approved_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date approvedEnd,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @MakerCheckerOffsetPageable Pageable pageable) {
        Page<MemberAttributeAdjustRes> page = memberAttributeAdjustService.getInReviews(
                programId,
                name,
                approvalStatus,
                createdBy,
                createdStart,
                createdEnd,
                approvedBy,
                approvedStart,
                approvedEnd,
                pageable
        );

        return success(
                page.getContent(),
                (int) pageable.getOffset(),
                pageable.getPageSize(),
                (int) page.getTotalElements()
        );
    }

    @GetMapping(value = "/requests/{code}")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_ADJUST, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewDetail(@PathVariable String code) {
        return success(memberAttributeAdjustService.getDetail(code));
    }

    @GetMapping(value = "/requests")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_ADJUST, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewDetails(
            @RequestParam(value = "code") String code,
            @RequestParam(value = "process_status", required = false) ERequestProcessStatus processStatus,
            @RequestParam(value = "offset", defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = "limit", defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit) {

        Page<MemberAttributeAdjustDetailRes> page = memberAttributeAdjustService
                .getDetails(code, processStatus, new OffsetBasedPageRequest(offset, limit, Sort.by("id").ascending()));

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_ADJUST, permissions = {AccessPermission.APPROVE_OR_REJECT})
    })
    public ResponseEntity<?> approveBatchRequest(@Valid @RequestBody ApprovalReq req) {
        memberAttributeAdjustService.approveBatchRequest(req);
        return success(null);
    }

    @PostMapping("/requests/{id}/cancel")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_ADJUST, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> cancelRequest(@PathVariable("id") String id,
                                           @RequestBody CancelReq req) {
        req.setId(id);
        memberAttributeAdjustService.cancelInReview(req);
        return success(null);
    }

    @GetMapping("/{code}/export")
    public ResponseEntity<?> exportFile(@PathVariable(name = "code") String code) {
        ResourceDTO dto = memberAttributeAdjustService.exportFile(code);
        return new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(dto.getFilename()), HttpStatus.OK);
    }

    @GetMapping("/requests/available")
    public ResponseEntity<?> getListAvailable(@RequestParam(name = "program_id") Integer programId,
                                              @RequestParam(name = "name", required = false) String name,
                                              @RequestParam(name = "status", required = false) EBatchRequestProcessStatus status,
                                              @RequestParam(value = "created_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date createdStart,
                                              @RequestParam(value = "created_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date createdEnd,
                                              @RequestParam(name = "offset", defaultValue = "0") Integer offset,
                                              @RequestParam(name = "limit", defaultValue = "10") Integer limit) {
        Page<MemberAttributeAdjustRes> listAvailable = memberAttributeAdjustService.getListAvailable(programId, name, status, createdStart, createdEnd, offset, limit);

        return success(listAvailable.getContent(), offset, limit, (int) listAvailable.getTotalElements());
    }
}