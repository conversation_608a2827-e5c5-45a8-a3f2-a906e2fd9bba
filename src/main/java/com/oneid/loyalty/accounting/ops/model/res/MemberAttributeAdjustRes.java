package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.entity.MemberAttributeUpdate;
import lombok.*;

import java.util.Date;


@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberAttributeAdjustRes {

    private Long id;

    private String code;

    private String name;

    private String createdBy;

    private Date createdAt;

    private String approvedBy;

    private Date approvedAt;

    private EBatchRequestProcessStatus status;

    private EApprovalStatus approvalStatus;

    public static MemberAttributeAdjustRes valueOf(MemberAttributeUpdate memberAttributeUpdate) {
        MemberAttributeAdjustRes memberAttributeAdjustRes = new MemberAttributeAdjustRes();

        memberAttributeAdjustRes.setId(memberAttributeUpdate.getId());
        memberAttributeAdjustRes.setCode(memberAttributeUpdate.getCode());
        memberAttributeAdjustRes.setName(memberAttributeUpdate.getName());
        memberAttributeAdjustRes.setStatus(memberAttributeUpdate.getProcessStatus());
        memberAttributeAdjustRes.setCreatedAt(memberAttributeUpdate.getCreatedAt());

        return memberAttributeAdjustRes;
    }
}