package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerStatus;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyRuleFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CounterFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalSearchReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes.ChangeRecordFeginRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.kafka.event.ResetRuleEvent;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateCounterReq;
import com.oneid.loyalty.accounting.ops.model.req.EditCounterReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleReq;
import com.oneid.loyalty.accounting.ops.model.res.CounterRes;
import com.oneid.loyalty.accounting.ops.model.res.CounterServicesRes;
import com.oneid.loyalty.accounting.ops.model.res.CounterStatisticRes;
import com.oneid.loyalty.accounting.ops.model.res.DropdownRes;
import com.oneid.loyalty.accounting.ops.model.res.LinkedServiceRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.VersionRes;
import com.oneid.loyalty.accounting.ops.service.OpsCounterService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleRequestService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.service.VersioningService;
import com.oneid.loyalty.accounting.ops.util.AuditorAwareUtil;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.MongoSearchUtil;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonActionType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterAttribute;
import com.oneid.oneloyalty.common.constant.ECounterLevel;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import com.oneid.oneloyalty.common.constant.ECounterRuleType;
import com.oneid.oneloyalty.common.constant.ECounterType;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Counter;
import com.oneid.oneloyalty.common.entity.CounterHistory;
import com.oneid.oneloyalty.common.entity.CounterLevelObject;
import com.oneid.oneloyalty.common.entity.CounterRequest;
import com.oneid.oneloyalty.common.entity.CounterVoucher;
import com.oneid.oneloyalty.common.entity.Limitation;
import com.oneid.oneloyalty.common.entity.LimitationRequest;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramAttributeServiceType;
import com.oneid.oneloyalty.common.entity.ProgramTierPolicy;
import com.oneid.oneloyalty.common.entity.ProgramTransactionAttribute;
import com.oneid.oneloyalty.common.entity.Rule;
import com.oneid.oneloyalty.common.entity.RuleCondition;
import com.oneid.oneloyalty.common.entity.ServiceCounterRequest;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CounterRepository;
import com.oneid.oneloyalty.common.repository.CounterRequestRepository;
import com.oneid.oneloyalty.common.repository.LimitationRequestRepository;
import com.oneid.oneloyalty.common.repository.ProgramAttributeServiceTypeRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.RuleConditionRepository;
import com.oneid.oneloyalty.common.repository.ServiceCounterRequestRepository;
import com.oneid.oneloyalty.common.repository.TierPolicyRequestRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CounterHistoryService;
import com.oneid.oneloyalty.common.service.CounterLevelObjectService;
import com.oneid.oneloyalty.common.service.CounterService;
import com.oneid.oneloyalty.common.service.CounterVoucherService;
import com.oneid.oneloyalty.common.service.LimitationService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTierPolicyService;
import com.oneid.oneloyalty.common.service.ProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.service.RuleService;
import com.oneid.oneloyalty.common.util.LogData;
import com.oneid.oneloyalty.common.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OpsCounterServiceImpl implements OpsCounterService {
    @Value("${maker-checker.module.counter_seq}")
    private String moduleId;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private ProgramRepository programRepository;

    @Autowired
    private CounterRequestRepository counterRequestRepository;

    private TransactionTemplate transactionTemplate;

    @Autowired
    private OpsRuleRequestService opsRuleRequestService;

    @Autowired
    private CounterRepository counterRepository;

    @Autowired
    private CounterService counterService;

    @Autowired
    private CounterVoucherService counterVoucherService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private ProgramTierPolicyService programTierPolicyService;

    @Autowired
    private LimitationService limitationService;

    @Autowired
    private LimitationRequestRepository limitationRequestRepository;

    @Autowired
    private TierPolicyRequestRepository tierPolicyRequestRepository;

    @Autowired
    private ServiceCounterRequestRepository serviceCounterRequestRepository;

    @Autowired
    private OpsRuleService opsRuleService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private CounterHistoryService counterHistoryService;

    @Autowired
    private VersioningService versioningService;

    @Autowired
    private AuditorAwareUtil auditorAwareUtil;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private RuleService ruleService;

    @Autowired
    RuleConditionRepository ruleConditionRepository;

    @Autowired
    private ProgramAttributeServiceTypeRepository programAttributeServiceTypeRepository;

    @Autowired
    private OneloyaltyRuleFeignClient oneloyaltyRuleFeignClient;

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private ProgramTransactionAttributeService programTransactionAttributeService;

    @Autowired
    private CounterLevelObjectService counterLevelObjectService;

    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }

    @Override
    public Integer requestCreatingCounterRequest(CreateCounterReq req) {
        CounterRequest counterRequest = transactionTemplate.execute(status -> {
            CounterRequest entity = createRequest(req);

            opsRuleRequestService.createRuleRequest(entity.getId(), entity.getProgramId(), EServiceType.COUNTER, req.getRules());

            return entity;
        });

        ChangeRequestFeignReq feignReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(counterRequest.getId().toString())
                .payload(CounterFeignReq.builder()
                        .counterRequestId(counterRequest.getId())
                        .build())
                .build();

        makerCheckerServiceClient.changes(feignReq);

        return counterRequest.getId();
    }

    @Override
    public Page<CounterRes> getAvailableCounterRequests(
            Integer programId,
            String code,
            String name,
            ECommonStatus counterStatus,
            Pageable pageable,
            ECounterRuleType counterRuleType
    ) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());

        if (Objects.nonNull(counterRuleType) && ECounterRuleType.NONE.equals(counterRuleType)) {
            throw new BusinessException(ErrorCode.COUNTER_RULE_TYPE_INVALID);
        }

        SpecificationBuilder specification = new SpecificationBuilder();

        specification.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));

        if (counterStatus != null)
            specification.add(new SearchCriteria("status", counterStatus, SearchOperation.EQUAL));

        if (programId != null)
            specification.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        if (code != null)
            specification.add(new SearchCriteria("code", code, SearchOperation.EQUAL));

        if (name != null)
            specification.add(new SearchCriteria("name", name, SearchOperation.MATCH));

        if (counterRuleType != null) {
            specification.add(new SearchCriteria("counterRuleType", counterRuleType, SearchOperation.EQUAL));
        }

        Page<Counter> page = counterService.find(specification, pageable);
        List<CounterRes> content = new ArrayList<>();
        for (Counter counter : page.getContent()) {
            Optional<Business> businessOpt = businessService.find(counter.getBusinessId());
            Optional<Program> programOpt = programService.find(counter.getProgramId());

            CounterLevelObject counterLevelObject = null;

            if (Objects.nonNull(counter.getLevel())) {
                counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(programId, counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
            }
            ProgramTransactionAttribute programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(programId, counter.getCounterLevelAttribute()).orElse(null);

            CounterRes counterRes = CounterRes.builder()
                    .counterStatus(counter.getStatus())
                    .counterId(counter.getId())
                    .name(counter.getName())
                    .code(counter.getCode())
                    .startDate(counter.getStartDate())
                    .endDate(counter.getEndDate())
                    .serviceType(counter.getServiceType())
                    .counterAttribute(counter.getCounterAttribute())
                    .counterLevel(Objects.nonNull(counterLevelObject)
                            ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                    .period(counter.getPeriod())
                    .periodDisplay(counter.getPeriod().getDisplayName())
                    .description(counter.getDescription())
                    .enableRevert(counter.getEnableRevert())
                    .counterType(counter.getType())
                    .counterTypeDisplay(Objects.nonNull(counter.getType()) ? counter.getType().getDisplayName() : null)
                    .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                            ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                            programTransactionAttribute.getName()) : null)
                    .counterLevelType(counter.getLevelType())
                    .counterRuleType(counter.getCounterRuleType())
                    .build();
            if (programOpt.isPresent()) {
                Program program = programOpt.get();
                counterRes.setProgram(new ShortEntityRes(program.getId(), program.getName(), program.getCode()));
            }
            content.add(counterRes);
        }

        return new PageImpl<CounterRes>(content, pageable, page.getTotalElements());
    }

    @Override
    public Page<CounterRes> getInReviewCounterRequests(Integer programId,
                                                       String code,
                                                       String name,
                                                       ERequestType requestType,
                                                       Long fromStartDate,
                                                       Long toStartDate,
                                                       String status,
                                                       ECounterRuleType counterRuleType,
                                                       Integer offset,
                                                       Integer limit,
                                                       MakerCheckerInternalPreviewReq req) {

        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Map<String, Object> searchPayload = (new MongoSearchUtil())
                .setFilter("payload.program_id", programId, false, false, false)
                .setFilter("payload.code", code, false, false, false)
                .setFilter("payload.name", name, false, true, false, MongoSearchUtil.OPTION_IGNORE_CASE_INSENSITIVITY)
                .setFilter("payload.request_type", requestType, false, false, false)
                .setFilter("payload.counter_status", status, false, false, false)
                .setFilter("payload.start_date", Objects.nonNull(fromStartDate) && Objects.nonNull(toStartDate) ? MakerCheckerInternalPreviewReq.RangeDateReq.builder().fromDate(fromStartDate).toDate(toStartDate).build() : null, false, false, false)
                .setFilter("payload.counter_rule_type", counterRuleType, false, false, false)
                .build();

        req.setBusinessId(business.getId());
        req.setProperties(searchPayload);
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(EMakerCheckerType.COUNTER, req, offset, limit, null);
        List<CounterRes> res = previewRes.getData().stream()
                .map(this::convertPreview)
                .collect(Collectors.toList());

        return new PageImpl<>(res, new OffsetBasedPageRequest(offset, limit), previewRes.getMeta().getTotal());
    }

    @Override
    public Page<VersionRes> getVersionList(
            Integer counterId,
            List<EApprovalStatus> approvalStatus,
            String fromCreatedAt,
            String toCreatedAt,
            String fromReviewedAt,
            String toReviewedAt,
            String createdBy,
            String checkedBy,
            Integer offset,
            Integer limit
    ) {
        Counter counter = counterService.findById(counterId).
                orElseThrow(() ->
                        new BusinessException(ErrorCode.COUNTER_NOT_FOUND, null, null)
                );
        if (Objects.nonNull(counter.getRequestCode())) {
            return versioningService.getVersions(
                    counter.getRequestCode(),
                    counter.getVersion(),
                    approvalStatus,
                    fromCreatedAt,
                    toCreatedAt,
                    fromReviewedAt,
                    toReviewedAt,
                    createdBy,
                    checkedBy,
                    offset,
                    limit
            );
        }
        return new PageImpl<VersionRes>(Collections.EMPTY_LIST, Pageable.unpaged(), 0);

    }

    @Override
    public CounterRes getInReviewCounterRequestById(String reviewId) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(EMakerCheckerType.COUNTER.getType(), reviewId);
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Counter - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.COUNTER.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Counter - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }
        CreateCounterReq payload = this.objectMapper.convertValue(previewDetailRes.getData().getPayload(), CreateCounterReq.class);
        Program program = programService.find(payload.getProgramId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
        );
        Business business = businessService.find(payload.getBusinessId()).orElseThrow(
                () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND)
        );

        CounterLevelObject counterLevelObject = null;

        if (Objects.nonNull(payload.getCounterLevel())) {
            counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(program.getId(), payload.getCounterLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
        }
        ProgramTransactionAttribute programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(program.getId(), payload.getCounterLevelAttribute()).orElse(null);

        List<RuleRes> ruleInReview = null;

        if (Objects.nonNull(payload.getRules())) {
            ruleInReview = opsRuleService.getRuleInReview(payload.getProgramId(), payload.getRules(), payload.getCode());
        }

        List<CounterServicesRes> lCounterServicesRes = new ArrayList<>();
        if (Objects.nonNull(payload.getId())) {
            EServiceType serviceType = payload.getServiceType();
            if (serviceType != null) {
                switch (serviceType) {
                    case TIER:
                        lCounterServicesRes = getLinkedProgramTierPolicies(payload.getId());
                        break;
                    case LIMITATION:
                        lCounterServicesRes = getLinkedLimitations(payload.getId(), payload.getProgramId());
                        break;
                    default:
                }
            }
        }
        return CounterRes.builder()
                .requestId(payload.getId())
                .businessId(business.getId())
                .businessName(business.getName())
                .businessCode(business.getCode())
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .period(payload.getPeriod())
                .periodDisplay(payload.getPeriod().getDisplayName())
                .restoredFromVersion(previewDetailRes.getData().getRestoredFromVersion())
                .restoredFromId(previewDetailRes.getData().getRestoredFromId())
                .version(previewDetailRes.getData().getVersion())
                .endDate(DateTimes.toDate(payload.getEndDate()))
                .counterType(payload.getCounterType())
                .counterTypeDisplay(payload.getCounterType().getDisplayName())
                .counterLevel(Objects.nonNull(counterLevelObject)
                        ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                .counterAttribute(payload.getCounterAttribute())
                .counterStatus(ECommonStatus.of(payload.getCounterStatus()))
                .name(payload.getName())
                .isAwardAllVoucher(payload.getIsAwardAllVoucher())
                .voucherCodes(payload.getVoucherCodes())
                .description(payload.getDescription())
                .enableRevert(payload.getEnableRevert())
                .code(payload.getCode())
                .startDate(DateTimes.toDate(payload.getStartDate()))
                .serviceType(payload.getServiceType())
                .createdAt(Objects.nonNull(previewDetailRes.getData().getMadeDate()) ? Date.from(ZonedDateTime.parse(previewDetailRes.getData().getMadeDate()).toInstant()) : null)
                .createdBy(previewDetailRes.getData().getMadeByUserName())
                .approvedAt(Objects.nonNull(previewDetailRes.getData().getCheckedDate()) ? Date.from(ZonedDateTime.parse(previewDetailRes.getData().getCheckedDate()).toInstant()) : null)
                .approvedBy(previewDetailRes.getData().getCheckedByUserName())
                .approvalStatus(EApprovalStatus.valueOf(previewDetailRes.getData().getStatus()))
                .reason(previewDetailRes.getData().getComment())
                .serviceType(payload.getServiceType())
                .enableRevert(payload.getEnableRevert())
                .counterServicesRes(lCounterServicesRes)
                .resetValue(payload.getResetValue())
                .rules(ruleInReview)
                .requestType(payload.getRequestType())
                .madeReason(previewDetailRes.getData().getMadeReason())
                .counterLevelType(payload.getCounterLevelType())
                .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                        ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                        programTransactionAttribute.getName()) : null)
                .counterRuleType(payload.getCounterRuleType())
                .build();
    }

    @Override
    public CounterRes getAvailableCounterRequestById(Integer requestId) {
        return getAvailableCounterById(requestId);
    }

    private CounterRes getAvailableCounterById(Integer counterId) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Counter counter = counterService.findByIdAndBusinessId(counterId, business.getId());

        Program program = programRepository.findByIdAndBusinessId(counter.getProgramId(), business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        CounterLevelObject counterLevelObject = null;

        if (Objects.nonNull(counter.getLevel())) {
            counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(program.getId(), counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
        }
        ProgramTransactionAttribute programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(program.getId(), counter.getCounterLevelAttribute()).orElse(null);

        EServiceType serviceType = counter.getServiceType();
        List<RuleRes> rules = opsRuleService.getRule(counter.getCode(), program.getId(), EServiceType.COUNTER);

        List<CounterServicesRes> lCounterServicesRes = null;
        if (serviceType != null) {
            switch (serviceType) {
                case TIER:
                    lCounterServicesRes = getLinkedProgramTierPolicies(counterId);
                    break;
                case LIMITATION:
                    lCounterServicesRes = getLinkedLimitations(counterId, business.getId());
                    break;
                default:
            }
        }
        List<String> voucherCodes = counterVoucherService.findVoucherCodeByCounterIdAndStatus(counterId, ECommonStatus.ACTIVE);
        return CounterRes.builder()
                .requestId(counter.getId())
                .counterStatusHeader(getCounterActiveStatus(counter))
                .businessId(business.getId())
                .businessName(business.getName())
                .businessCode(business.getCode())
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .endDate(counter.getEndDate())
                .period(counter.getPeriod())
                .periodDisplay(counter.getPeriod().getDisplayName())
                .counterType(counter.getType())
                .counterTypeDisplay(counter.getType().getDisplayName())
                .counterLevel(Objects.nonNull(counterLevelObject)
                        ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                .counterAttribute(counter.getCounterAttribute())
                .counterStatus(counter.getStatus())
                .name(counter.getName())
                .description(counter.getDescription())
                .enableRevert(counter.getEnableRevert())
                .code(counter.getCode())
                .startDate(counter.getStartDate())
                .serviceType(counter.getServiceType())
                .updateAt(counter.getUpdatedAt())
                .updatedAt(counter.getUpdatedAt())
                .createdAt(counter.getCreatedAt())
                .updateBy(counter.getUpdatedBy())
                .updatedBy(counter.getUpdatedBy())
                .createdBy(counter.getCreatedBy())
                .approvedAt(counter.getApprovedAt())
                .approvedBy(counter.getApprovedBy())
                .serviceType(counter.getServiceType())
                .enableRevert(counter.getEnableRevert())
                .resetValue(counter.getResetValue())
                .counterServicesRes(lCounterServicesRes)
                .rules(rules)
                .isAwardAllVoucher(EBoolean.YES.equals(counter.getIsAwardVoucherAll()))
                .voucherCodes(voucherCodes)
                .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                        ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                        programTransactionAttribute.getName()) : null)
                .counterLevelType(counter.getLevelType())
                .counterRuleType(counter.getCounterRuleType())
                .build();
    }

    private List<CounterServicesRes> getLinkedProgramTierPolicies(Integer counterId) {
        List<ProgramTierPolicy> programTierPolicy =
                programTierPolicyService.findLikeCounterIds(counterId.toString());
        return programTierPolicy
                .stream()
                .map(tier -> CounterServicesRes.valueOf(tier, null))
                .collect(Collectors.toList());
    }

    private List<CounterServicesRes> getLinkedLimitations(Integer counterId, Integer businessId) {

        List<Limitation> limitations =
                limitationService.findAllByCounterId(counterId);

        return limitations
                .stream()
                .map(limitation -> {
                    CounterServicesRes counterServicesRes = CounterServicesRes.valueOf(null, limitation);
                    Optional<LimitationRequest> effectedVersion = limitationRequestRepository.findEffectedVersion(businessId, limitation.getProgramId(), counterServicesRes.getCode());
                    effectedVersion.ifPresent(limitationRequest -> counterServicesRes.setId(limitationRequest.getId()));
                    return counterServicesRes;
                })
                .collect(Collectors.toList());
    }

    @Override
    public CounterRes getEditCounterRequestSetting(Integer requestId) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Counter counter = counterService.findById(requestId).orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));
        Program program = programService.findByIdAndBusinessId(counter.getProgramId(), business.getId());
        List<RuleRes> rules = opsRuleService.getRule(counter.getCode(), program.getId(), EServiceType.COUNTER);
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.COUNTER.getType(), counter.getRequestCode());
        String editKey = opsReqPendingValidator.generateEditKey(counter.getRequestCode(), counter.getVersion());

        CounterLevelObject counterLevelObject = null;

        if (Objects.nonNull(counter.getLevel())) {
            counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(program.getId(), counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
        }
        ProgramTransactionAttribute programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(program.getId(), counter.getCounterLevelAttribute()).orElse(null);

        EServiceType serviceType = counter.getServiceType();
        List<CounterServicesRes> lCounterServicesRes = null;
        if (serviceType != null) {
            switch (serviceType) {
                case TIER:
                    lCounterServicesRes = getLinkedProgramTierPolicies(requestId);
                    break;
                case LIMITATION:
                    lCounterServicesRes = getLinkedLimitations(requestId, business.getId());
                    break;
                default:
            }
        }
        List<String> voucherCodes = counterVoucherService.findVoucherCodeByCounterIdAndStatus(requestId, ECommonStatus.ACTIVE);
        return CounterRes.builder()
                .requestId(counter.getId())
                .businessId(counter.getBusinessId())
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .nextVersion(counter.getVersion() + 1)
                .period(counter.getPeriod())
                .periodDisplay(counter.getPeriod().getDisplayName())
                .description(counter.getDescription())
                .enableRevert(counter.getEnableRevert())
                .counterStatus(counter.getStatus())
                .name(counter.getName())
                .code(counter.getCode())
                .startDate(counter.getStartDate())
                .serviceType(counter.getServiceType())
                .endDate(counter.getEndDate())
                .counterType(counter.getType())
                .counterTypeDisplay(counter.getType().getDisplayName())
                .counterLevel(Objects.nonNull(counterLevelObject)
                        ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                .counterAttribute(counter.getCounterAttribute())
                .counterServicesRes(lCounterServicesRes)
                .rules(rules)
                .isAwardAllVoucher(EBoolean.YES.equals(counter.getIsAwardVoucherAll()))
                .voucherCodes(voucherCodes)
                .editKey(editKey)
                .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                        ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                        programTransactionAttribute.getName()) : null)
                .counterLevelType(counter.getLevelType())
                .counterRuleType(counter.getCounterRuleType())
                .build();
    }

    @Override
    public MakerCheckerInternalMakerRes requestEditingCounterRequest(Integer requestId, EditCounterReq req) {
        Counter counter = counterService.findById(requestId).orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));
        if (!DateTimes.toDate(req.getEndDate()).after(counter.getStartDate())) {
            throw new BusinessException(ErrorCode.COUNTER_END_DATE_INVALID, null, null);
        }
        req.setServiceType(counter.getServiceType());
        if (ECounterRuleType.SCHEME.equals(counter.getCounterRuleType())) {
            req.setRules(null);
        } else if (ECounterRuleType.RULE_CONDITION.equals(counter.getCounterRuleType())) {
            if (CollectionUtils.isEmpty(req.getRules())) {
                throw new BusinessException(ErrorCode.COUNTER_RULE_TYPE_INVALID, null, null);
            }
        }

        opsReqPendingValidator.verifyEditKey(req.getEditKey(), counter.getRequestCode(), counter.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.COUNTER.getType(), counter.getRequestCode());

        if (CollectionUtils.isNotEmpty(req.getRules())) {
            opsRuleService.validateRules(counter.getProgramId(), EServiceType.COUNTER, req.getRules(), true);

            if (!isEffectiveTimeValid(counter.getStartDate().getTime(), req.getEndDate(), req.getRules())) {
                throw new BusinessException(ErrorCode.COUNTER_RULE_EFFECTIVE_DATE_INVALID, null, null);
            }
        }

        List<String> counterCodes = counterVoucherService.findVoucherCodeByCounterIdAndStatus(requestId, ECommonStatus.ACTIVE);

        CreateCounterReq payload = CreateCounterReq.mapperToCreate(requestId, req, counter, counterCodes);
        return makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.COUNTER,
                Objects.nonNull(counter.getRequestCode()) ? counter.getRequestCode() : UUID.randomUUID().toString(),
                payload, payload.getMadeReason(), payload.isSendEmail());
    }

    @Override
    public Page<LinkedServiceRes> getLinkedServiceTypes(Integer requestId, Pageable pageable) {
        Counter counter = counterService.findById(requestId).orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));
        EServiceType serviceType = counter.getServiceType();
        if (serviceType != null) {
            switch (serviceType) {
                case TIER:
                    return getLinkedTierPolicies(counter, pageable);

                case LIMITATION:
                    return getLinkedLimitations(counter, pageable);
                default:
            }
        }
        return new PageImpl<>(Collections.emptyList(), pageable, 0);
    }

    private CounterRes convertPreview(MakerCheckerInternalDataDetailRes data) {
        CreateCounterReq createCounterReq = this.jsonMapper.convertValue(data.getPayload(), CreateCounterReq.class);
        Program program = programService.find(createCounterReq.getProgramId()).orElse(null);

        CounterLevelObject counterLevelObject = null;
        ProgramTransactionAttribute programTransactionAttribute = null;
        if (Objects.nonNull(program)) {
            if (Objects.nonNull(createCounterReq.getCounterLevel())) {
                counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(program.getId(), createCounterReq.getCounterLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
            }
            programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(program.getId(), createCounterReq.getCounterLevelAttribute()).orElse(null);
        }
        return CounterRes
                .builder()
                .id(data.getId())
                .program(Objects.nonNull(program) ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .code(createCounterReq.getCode())
                .name(createCounterReq.getName())
                .startDate(DateTimes.toDate(createCounterReq.getStartDate()))
                .endDate(DateTimes.toDate(createCounterReq.getEndDate()))
                .approvalStatus(EApprovalStatus.valueOf(data.getStatus()))
                .counterStatus(ECommonStatus.of(createCounterReq.getCounterStatus()))
                .serviceType(createCounterReq.getServiceType())
                .counterAttribute(createCounterReq.getCounterAttribute())
                .counterLevel(Objects.nonNull(counterLevelObject)
                        ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                .period(createCounterReq.getPeriod())
                .enableRevert(createCounterReq.getEnableRevert())
                .counterType(createCounterReq.getCounterType())
                .counterTypeDisplay(createCounterReq.getCounterType().getDisplayName())
                .createdBy(data.getMadeByUserName())
                .createdAt(data.getMadeDateToDate())
                .approvedBy(data.getCheckedByUserName())
                .approvedAt(data.getCheckedDateToDate())
                .requestType(createCounterReq.getRequestType())
                .counterLevelType(createCounterReq.getCounterLevelType())
                .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                        ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                        programTransactionAttribute.getName()) : null)
                .counterRuleType(createCounterReq.getCounterRuleType())
                .build();
    }

    private Page<LinkedServiceRes> getLinkedLimitations(Counter counter, Pageable pageable) {
        Page<Limitation> limitations = limitationService.find(counter.getId(), pageable);
        List<LinkedServiceRes> content = limitations.getContent()
                .stream()
                .map(request -> LinkedServiceRes.builder()
                        .requestId(request.getId())
                        .serviceCode(request.getCode())
                        .serviceName(request.getName())
                        .status(request.getStatus())
                        .serviceType(EServiceType.LIMITATION)
                        .startDate(request.getStartDate())
                        .endDate(request.getEndDate())
                        .build())
                .collect(Collectors.toList());
        return new PageImpl<>(content, pageable, limitations.getTotalElements());
    }

    private CounterRequest editCounterRequest(Integer requestId, EditCounterReq req) {
        CounterRequest availableRequest = counterRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));

        EApprovalStatus approvalStatus = availableRequest.getApprovalStatus();

        if (approvalStatus.equals(EApprovalStatus.PENDING))
            throw new BusinessException(ErrorCode.COUNTER_REQUEST_IS_ALREADY_REQUESTED, null, null);

        if (approvalStatus.equals(EApprovalStatus.REJECTED))
            throw new BusinessException(ErrorCode.COUNTER_REQUEST_CAN_NOT_BE_EDITED, null, null);

        if (!availableRequest.getRequestStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.COUNTER_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED, null, null);

        counterRequestRepository.findPendingVersion(availableRequest.getBusinessId(), availableRequest.getProgramId(), availableRequest.getCode())
                .ifPresent(entity -> {
                    throw new BusinessException(ErrorCode.COUNTER_REQUEST_IS_ALREADY_REQUESTED, null, null);
                });

        int totalLinkedServiceTypes = countLinkedServiceTypes(availableRequest);

        if (req.getServiceType() != null
            && availableRequest.getServiceType() != null
            && !availableRequest.getServiceType().equals(req.getServiceType())
            && totalLinkedServiceTypes > 0)
            throw new BusinessException(ErrorCode.COUNTER_REQUEST_SERVICE_TYPE_CAN_NOT_BE_EDITED, null, null);

        if (req.getCounterStatus().equals(ECommonStatus.INACTIVE) && totalLinkedServiceTypes > 0)
            throw new BusinessException(ErrorCode.CAN_NOT_INACTIVATED_COUNTER_STATUS_LINKED_SERVICE_TYPES, null, null);

        CounterRequest newRequest = new CounterRequest();

        newRequest.setName(req.getName());
        newRequest.setDescription(req.getDescription());
        newRequest.setEndDate(DateTimes.toDate(req.getEndDate()));
        newRequest.setStatus(ECommonStatus.of(req.getCounterStatus()));

        newRequest.setServiceType(req.getServiceType());
        newRequest.setVersion(availableRequest.getVersion() + 1);
        newRequest.setBusinessId(availableRequest.getBusinessId());
        newRequest.setProgramId(availableRequest.getProgramId());
        newRequest.setCode(availableRequest.getCode());
        newRequest.setStartDate(availableRequest.getStartDate());
        newRequest.setPeriod(availableRequest.getPeriod());
        newRequest.setLevel(availableRequest.getLevel());
        newRequest.setType(availableRequest.getType());
        newRequest.setRequestStatus(ECommonStatus.PENDING);
        newRequest.setApprovalStatus(EApprovalStatus.PENDING);
        newRequest.setCounterAttribute(availableRequest.getCounterAttribute());
        newRequest.setEnableRevert(EBoolean.NO);

        newRequest = counterRequestRepository.save(newRequest);

        Integer counterRequestId = newRequest.getId();

        Collection<ServiceCounterRequest> serviceCounterRequests = serviceCounterRequestRepository
                .findByCounterRequestIdAndActionTypeIn(requestId, List.of(ECommonActionType.Create, ECommonActionType.Update));

        Collection<ServiceCounterRequest> serviceCounterRequestUpdates = serviceCounterRequests.stream().map(
                serviceCounterRequest -> {
                    ServiceCounterRequest newServiceCounterRequest = new ServiceCounterRequest();
                    newServiceCounterRequest.setCounterRequestId(counterRequestId);
                    newServiceCounterRequest.setServiceRequestId(serviceCounterRequest.getServiceRequestId());
                    newServiceCounterRequest.setServiceType(serviceCounterRequest.getServiceType());
                    newServiceCounterRequest.setStatus(serviceCounterRequest.getStatus());
                    newServiceCounterRequest.setActionType(ECommonActionType.Update);
                    return newServiceCounterRequest;
                }
        ).collect(Collectors.toList());

        serviceCounterRequestRepository.saveAll(serviceCounterRequestUpdates);

        return newRequest;
    }

    private CounterRequest createRequest(CreateCounterReq req) {
        Date currentTime = new Date();

        Business business = businessRepository.findById(req.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        if (business.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, null, null);

        Program program = programRepository.findByIdAndBusinessId(req.getProgramId(), business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        if (program.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, null, null);

        counterRequestRepository.findPendingVersion(business.getId(), program.getId(), req.getCode())
                .ifPresent(entity -> {
                    throw new BusinessException(ErrorCode.COUNTER_CODE_IS_BEING_USED, null, null, new Object[]{req.getCode()});
                });

        counterRequestRepository.findEffectedVersion(business.getId(), program.getId(), req.getCode())
                .ifPresent(entity -> {
                    throw new BusinessException(ErrorCode.COUNTER_CODE_IS_BEING_USED, null, null, new Object[]{req.getCode()});
                });


        if (req.getServiceType() != null) {
            EServiceType serviceType = req.getServiceType();

            if (!EServiceType.TIER.equals(serviceType) && !EServiceType.LIMITATION.equals(serviceType))
                throw new BusinessException(ErrorCode.COUNTER_SERVICE_TYPE_IS_INVALID, null, null, new Object[]{req.getServiceType()});

            if (serviceType.equals(EServiceType.TIER) && !req.getCounterLevel().equals(ECounterLevel.MEMBER))
                throw new BusinessException(ErrorCode.COUNTER_LEVEL_IS_NOT_AVAILABLE_FOR_SEVICE_TYPE, null, null, new Object[]{req.getCounterLevel(), req.getServiceType()});
        }

        if (DateTimes.toDate(req.getStartDate()).before(currentTime))
            throw new BusinessException(ErrorCode.COUNTER_START_DATE_INVALID, null, null);

        if (DateTimes.toDate(req.getEndDate()).before(currentTime) ||
            DateTimes.toDate(req.getEndDate()).before(DateTimes.toDate(req.getStartDate())))
            throw new BusinessException(ErrorCode.COUNTER_END_DATE_INVALID, null, null);

        CounterRequest request = new CounterRequest();

        request.setVersion(1);
        request.setBusinessId(business.getId());
        request.setProgramId(program.getId());
        request.setCode(req.getCode());
        request.setName(req.getName());
        request.setDescription(req.getDescription());
        request.setStartDate(DateTimes.toDate(req.getStartDate()));
        request.setEndDate(DateTimes.toDate(req.getEndDate()));
        request.setPeriod(req.getPeriod());
        request.setLevel(req.getCounterLevel());
        request.setType(req.getCounterType());
        request.setStatus(ECommonStatus.of(req.getCounterStatus()));
        request.setEnableRevert(EBoolean.NO);
        request.setServiceType(req.getServiceType());
        request.setRequestStatus(ECommonStatus.PENDING);
        request.setApprovalStatus(EApprovalStatus.PENDING);

        if (req.getCounterType().equals(ECounterType.VALUE)) {
            request.setCounterAttribute(req.getCounterAttribute());
        }

        return counterRequestRepository.save(request);
    }

    private List<CounterRes> transform(List<Object[]> entities, Map<Integer, ChangeRecordFeginRes> changeRecordFeginResMap) {
        List<CounterRes> content = new ArrayList<CounterRes>();

        Business business = null;
        Program program = null;
        Counter counterRequest = null;
        Integer reviewId = null;

        for (Object[] each : entities) {
            business = Business.class.cast(each[0]);
            program = Program.class.cast(each[1]);
            counterRequest = Counter.class.cast(each[2]);

            reviewId = changeRecordFeginResMap.containsKey(counterRequest.getId()) ? changeRecordFeginResMap.get(counterRequest.getId()).getChangeRequestId() : null;

            content.add(CounterRes.builder()
                    .reviewId(reviewId)
                    .requestId(counterRequest.getId())
                    .businessName(business.getName())
                    .program(Objects.nonNull(program) ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                    .counterStatus(counterRequest.getStatus())
                    .name(counterRequest.getName())
                    .code(counterRequest.getCode())
                    .startDate(counterRequest.getStartDate())
                    .endDate(counterRequest.getEndDate())
                    .serviceType(counterRequest.getServiceType())
                    .build());
        }

        return content.stream().sorted((o1, o2) -> o2.getRequestId().compareTo(o1.getRequestId()))
                .collect(Collectors.toList()); // order by id desc
    }

    private int countLinkedServiceTypes(CounterRequest availableRequest) {
        int total = 0;

        if (availableRequest.getServiceType() != null) {
            switch (availableRequest.getServiceType()) {
                case TIER:
                    total = serviceCounterRequestRepository.countTotalLinkedTierPolicies(availableRequest.getId(), EServiceType.TIER);
                    break;

                case LIMITATION:
                    Counter counter = counterRepository.findByProgramIdAndCode(availableRequest.getProgramId(), availableRequest.getCode());

                    total = limitationRequestRepository.countByCounterIdAndProgramIdAndBusinessId(counter.getId(), availableRequest.getProgramId(), availableRequest.getBusinessId());

                    break;

                default:
                    break;
            }
        }

        return total;
    }

    private Page<LinkedServiceRes> getLinkedTierPolicies(Counter counter, Pageable pageable) {
        List<ProgramTierPolicy> programTierPolicy =
                programTierPolicyService.findLikeCounterIds(counter.getId().toString());
        List<LinkedServiceRes> content = programTierPolicy
                .stream()
                .map(request -> LinkedServiceRes.builder()
                        .requestId(request.getId())
                        .serviceCode(request.getCode())
                        .serviceName(request.getName())
                        .serviceType(EServiceType.TIER)
                        .status(request.getStatus())
                        .approvalStatus(EApprovalStatus.APPROVED)
                        .startDate(request.getStartDate())
                        .endDate(request.getEndDate())
                        .build())
                .collect(Collectors.toList());
        return new PageImpl<>(content, pageable, content.size());
    }

    private Page<LinkedServiceRes> getLinkedLimitations(Integer counterRequestId, Pageable pageable) {
        CounterRequest counterRequest = counterRequestRepository.findById(counterRequestId)
                .orElse(null);

        if (counterRequest == null)
            return new PageImpl<LinkedServiceRes>(Collections.emptyList(), pageable, 0);

        Counter counter = counterRepository.findByProgramIdAndCode(counterRequest.getProgramId(), counterRequest.getCode());

        if (counter == null)
            return new PageImpl<LinkedServiceRes>(Collections.emptyList(), pageable, 0);

        Page<LimitationRequest> page = limitationRequestRepository.findByCounterIdAndBusinessId(counter.getId(), counterRequest.getBusinessId(), pageable);

        if (page.getContent() == null)
            return new PageImpl<LinkedServiceRes>(Collections.emptyList(), pageable, 0);

        List<LinkedServiceRes> content = page.getContent()
                .stream()
                .map(request -> {
                    return LinkedServiceRes.builder()
                            .requestId(request.getId())
                            .serviceCode(request.getCode())
                            .serviceName(request.getName())
                            .serviceType(EServiceType.LIMITATION)
                            .startDate(request.getStartDate())
                            .endDate(request.getEndDate())
                            .status(request.getLimitationStatus())
                            .approvalStatus(request.getApprovalStatus())
                            .build();
                })
                .collect(Collectors.toList());

        return new PageImpl<LinkedServiceRes>(content, pageable, page.getTotalElements());
    }

    @Override
    public MakerCheckerInternalMakerRes create(CreateCounterReq req) {
        req.setId(null);
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        programService.findByIdAndBusinessId(req.getProgramId(), business.getId());
        req.setBusinessId(business.getId());

        validateCodeExistInAvailable(req.getProgramId(), req.getCode());
        validateCodeExistInOtherReqPending(req.getBusinessId(), req.getProgramId(), req.getCode());
        validateServiceType(req);
        validateStartDateAndEndDate(req);
        validateCounterRuleType(req);
        if (Objects.nonNull(req.getCounterLevelAttribute())) {
            validateAttribute(req);
        }
        validateRule(req);
        if (CollectionUtils.isNotEmpty(req.getRules())) {
            if (!isEffectiveTimeValid(req.getStartDate(), req.getEndDate(), req.getRules())) {
                throw new BusinessException(ErrorCode.COUNTER_RULE_EFFECTIVE_DATE_INVALID, null, null);
            }

            opsRuleService.validateRules(req.getProgramId(), EServiceType.COUNTER, req.getRules(), true);
        }
        req.setRequestType(ERequestType.CREATE);
        return makerCheckerInternalFeignClient
                .makerDefault(EMakerCheckerType.COUNTER, UUID.randomUUID().toString(),
                        req, req.getMadeReason(), req.isSendEmail());
    }

    private void validateCodeExistInAvailable(Integer programId, String code) {
        List<Counter> counters = counterService.findByProgramIdAndCode(programId, code);
        if (!counters.isEmpty()) {
            throw new BusinessException(ErrorCode.COUNTER_CODE_IS_BEING_USED, null, null, new Object[]{code});
        }
    }

    private void validateServiceType(CreateCounterReq req) {
        if (Objects.nonNull(req.getServiceType())) {
            EServiceType serviceType = req.getServiceType();
            if (!EServiceType.LIMITATION.equals(serviceType)
                    && !EServiceType.SCHEME.equals(serviceType)) {
                throw new BusinessException(ErrorCode.COUNTER_SERVICE_TYPE_IS_INVALID, null, null, new Object[]{req.getServiceType()});
            }
        }
    }

    private void validateStartDateAndEndDate(CreateCounterReq req) {
        if (req.getStartDate() <= DateTimes.currentTimeSeconds())
            throw new BusinessException(
                    ErrorCode.COUNTER_START_DATE_INVALID,
                    null,
                    null
            );

        if (req.getEndDate() <= req.getStartDate())
            throw new BusinessException(
                    ErrorCode.COUNTER_END_DATE_INVALID,
                    null,
                    null
            );
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> response = makerCheckerInternalFeignClient.previewDetail(EMakerCheckerType.COUNTER.getType(), req.getId());
        validateRequest(response, req);
        CreateCounterReq payload = objectMapper.convertValue(response.getData().getPayload(), CreateCounterReq.class);
        String createdBy = null;
        String updatedBy;
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        if (req.getStatus() == EApprovalStatus.APPROVED) {
            if (ECounterRuleType.NONE.equals(payload.getCounterRuleType())) {
                throw new BusinessException(ErrorCode.COUNTER_RULE_TYPE_INVALID, null, null);
            }
            Counter counter;
            programService.findActive(payload.getProgramId());
            businessService.findActive(payload.getBusinessId());
            String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(response.getData().getMadeByUserName());
            if (payload.getId() != null) {
                counter = counterService.findById(payload.getId())
                        .orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));
                if (EBoolean.NO.equals(counter.getIsAwardVoucherAll()) && Boolean.TRUE.equals(payload.getIsAwardAllVoucher())) {
                    List<CounterVoucher> counterVouchers = counterVoucherService.findAllByCounter(counter.getId()).stream().peek(ele -> {
                        ele.setStatus(ECommonStatus.INACTIVE);
                        opsReqPendingValidator.updateInfoChecker(ele, response.getData().getMadeDate(), null, response.getData().getMadeByUserName(), approvedBy);
                    }).collect(Collectors.toList());
                    counterVoucherService.saveALl(counterVouchers);
                }
                convertCounter(counter, payload);
                if (CollectionUtils.isNotEmpty(payload.getRules())) {
                    resetRuleReqs = opsRuleService.editRule(counter, payload.getRules(), response.getData(), approvedBy);
                }
                updatedBy = response.getData().getMadeByUserName();

                resetRuleReqs.addAll(syncLimit(payload, counter));
            } else {
                counter = new Counter();
                if (!DateTimes.toDate(payload.getStartDate()).after(response.getData().getMadeDateToDate())) {
                    throw new BusinessException(ErrorCode.COUNTER_START_DATE_INVALID, null, null);
                }
                convertCounter(counter, payload);
                counter.setRequestCode(response.getData().getRequestCode());
                if (CollectionUtils.isNotEmpty(payload.getRules())) {
                    resetRuleReqs = opsRuleService.createRule(counter, payload.getRules(), response.getData(), approvedBy);
                }
                updatedBy = createdBy = response.getData().getMadeByUserName();
            }
            if (!DateTimes.toDate(payload.getEndDate()).after(counter.getStartDate())) {
                throw new BusinessException(ErrorCode.COUNTER_END_DATE_INVALID, null, null);
            }
            counter.setRequestCode(response.getData().getRequestCode());
            counter.setVersion(response.getData().getVersion());

            opsReqPendingValidator.updateInfoChecker(counter, response.getData().getMadeDate(), createdBy, updatedBy, approvedBy);
            counterRepository.save(counter);

            // Save counter voucher
            if (Boolean.FALSE.equals(payload.getIsAwardAllVoucher())) {
                saveCounterVoucher(response, payload, updatedBy, counter, approvedBy);
            }

            // Check reset rule
            if (CollectionUtils.isNotEmpty(resetRuleReqs)) {
                APIFeignInternalResponse<?> ruleRes = oneloyaltyRuleFeignClient.checkReset(resetRuleReqs);
                if (ruleRes.getMeta().getCode() != 200) {
                    throw new BusinessException(ErrorCode.RESET_RULE_FAILED);
                }
            }
        }

        // Call to MakerChecker
        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> plAprroved = makerCheckerInternalFeignClient
                .checkerDefault(EMakerCheckerType.COUNTER, req, null);
        if (plAprroved.getMeta().getCode() != HttpStatus.OK.value()) {
            throw new BusinessException(
                    ErrorCode.MAKER_CHECKER_CANNOT_CHANGE_STATUS,
                    "Checker fail",
                    LogData.createLogData()
                            .append("id", req.getId()));

        }
        if (EApprovalStatus.APPROVED == req.getStatus() && CollectionUtils.isNotEmpty(resetRuleReqs)) {
            ResetRuleEvent<?> ruleEvent = ResetRuleEvent.builder()
                    .id(UUID.randomUUID().toString())
                    .eventType(ResetRuleEvent.RESET_RULE_EVENT_TYPE)
                    .timeStamp(System.currentTimeMillis())
                    .payload(resetRuleReqs)
                    .build();
            applicationEventPublisher.publishEvent(ruleEvent);
        }
    }

    private void saveCounterVoucher(APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> response, CreateCounterReq payload, String updatedBy, Counter counter, String approvedBy) {
        Set<String> activeIds = new HashSet<>();
        Map<String, CounterVoucher> mapProgramCorporation = counterVoucherService
                .findAllByCounter(counter.getId())
                .stream()
                .peek(ele -> {
                    if (ECommonStatus.ACTIVE.equals(ele.getStatus())) {
                        activeIds.add(ele.getVoucherCode());
                    }
                })
                .collect(Collectors.toMap(CounterVoucher::getVoucherCode, Function.identity(), (first, last) -> last));

        // Handle create a counter voucher
        List<CounterVoucher> counterVouchers = new ArrayList<>();
        if (activeIds.isEmpty()) {
            counterVouchers = Optional.ofNullable(payload.getVoucherCodes())
                    .orElse(Collections.emptyList())
                    .stream().map(ele -> {
                        CounterVoucher counterVoucher = new CounterVoucher();
                        counterVoucher.setProgramId(payload.getProgramId());
                        counterVoucher.setCounterId(counter.getId());
                        counterVoucher.setVoucherCode(ele);
                        counterVoucher.setStatus(ECommonStatus.ACTIVE);
                        opsReqPendingValidator.updateInfoChecker(counterVoucher, response.getData().getMadeDate(), response.getData().getMadeByUserName(), updatedBy, approvedBy);
                        return counterVoucher;
                    }).collect(Collectors.toList());
        } else {
            // Handle edit a counter voucher
            // Exp: | create: 1,2,3 | edit: 2,3,4 | we must create a voucher with code is
            // 4 and remove a voucher 1
            ArrayList<String> createIds = new ArrayList<String>(
                    CollectionUtils.subtract(payload.getVoucherCodes(), activeIds)); // 4
            ArrayList<String> deleteIds = new ArrayList<String>(
                    CollectionUtils.subtract(activeIds, payload.getVoucherCodes())); // 1
            ArrayList<String> holdIds = new ArrayList<>(payload.getVoucherCodes()); // 2, 3
            holdIds.retainAll(activeIds);
            if (!createIds.isEmpty()) {
                for (String voucherCode : createIds) {
                    CounterVoucher counterVoucher;
                    // Set the active status for obsolete record
                    if (mapProgramCorporation.containsKey(voucherCode)) {
                        counterVoucher = mapProgramCorporation.get(voucherCode);
                        counterVoucher.setStatus(ECommonStatus.ACTIVE);
                        opsReqPendingValidator.updateInfoChecker(counterVoucher, response.getData().getMadeDate(), null, updatedBy, approvedBy);
                    } else {
                        // New record
                        counterVoucher = prepareSaveCounterVoucher(response.getData(), payload,
                                counter.getId(), voucherCode, approvedBy);
                    }
                    counterVouchers.add(counterVoucher);
                }
            }
            if (!deleteIds.isEmpty()) {
                for (String deleteId : deleteIds) {
                    CounterVoucher counterVoucher = mapProgramCorporation.get(deleteId);
                    counterVoucher.setStatus(ECommonStatus.INACTIVE);
                    opsReqPendingValidator.updateInfoChecker(counterVoucher, response.getData().getMadeDate(), null, updatedBy, approvedBy);
                    counterVouchers.add(counterVoucher);
                }
            }
            if (!holdIds.isEmpty()) {
                for (String holdId : holdIds) {
                    CounterVoucher counterVoucher = mapProgramCorporation.get(holdId);
                    opsReqPendingValidator.updateInfoChecker(counterVoucher, response.getData().getMadeDate(), null, updatedBy, approvedBy);
                    counterVouchers.add(counterVoucher);
                }
            }
        }
        counterVoucherService.saveALl(counterVouchers);
    }

    private CounterVoucher prepareSaveCounterVoucher(
            MakerCheckerInternalDataDetailRes detailResData,
            CreateCounterReq payload,
            Integer counterId, String voucherCode, String approvedBy) {
        String createdBy;
        String updatedBy;
        CounterVoucher counterVoucher = new CounterVoucher();
        counterVoucher.setProgramId(payload.getProgramId());
        counterVoucher.setCounterId(counterId);
        counterVoucher.setVoucherCode(voucherCode);
        counterVoucher.setStatus(ECommonStatus.ACTIVE);
        updatedBy = createdBy = detailResData.getMadeByUserName();
        opsReqPendingValidator.updateInfoChecker(counterVoucher, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
        return counterVoucher;
    }

    private List<ResetRuleReq> syncLimit(CreateCounterReq payload, Counter counter) {
        Optional<Limitation> opLimit = limitationService.findAllByCounterIdAndProgramId(counter.getId(), payload.getProgramId()).stream().filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus())).findFirst();
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        if (opLimit.isPresent()) {
            Limitation limitation = opLimit.get();
            List<Rule> rulesLimit = ruleService.findByProgramIdAndServiceTypeAndServiceCode(payload.getProgramId(), EServiceType.LIMITATION, limitation.getCode());
            for (Rule rule : rulesLimit) {
                if (rule.getStatus().equals(ECommonStatus.ACTIVE)) {
                    rule.setStatus(ECommonStatus.INACTIVE);
                    List<RuleCondition> conditions = ruleConditionRepository.findByRuleId(rule.getId());
                    List<ResetRuleReq.ConditionReq> conditionReqs = new ArrayList<>();
                    for (RuleCondition condition : conditions) {
                        if (condition.getStatus().equals(ECommonStatus.ACTIVE)) {
                            condition.setStatus(ECommonStatus.INACTIVE);
                            RuleCondition ruleCondition = ruleConditionRepository.save(condition);
                            conditionReqs.add(ResetRuleReq.buildConditionReq(ruleCondition));
                        }
                    }
                    Rule ruleSaved = ruleService.save(rule);
                    ResetRuleReq resetRuleReq = ResetRuleReq.buildRuleReq(ruleSaved);
                    resetRuleReq.setConditions(conditionReqs);
                    resetRuleReqs.add(resetRuleReq);
                }
            }
            List<Rule> rulesCounter = ruleService.findByProgramIdAndServiceTypeAndServiceCode(payload.getProgramId(), EServiceType.COUNTER, counter.getCode());
            for (Rule rule : rulesCounter) {
                if (rule.getStatus().equals(ECommonStatus.ACTIVE)) {
                    Rule limitRule = createLimitRule(limitation.getCode(), rule);
                    List<RuleCondition> conditions = ruleConditionRepository.findByRuleId(rule.getId());
                    List<ResetRuleReq.ConditionReq> conditionReqs = new ArrayList<>();
                    for (RuleCondition condition : conditions) {
                        if (condition.getStatus().equals(ECommonStatus.ACTIVE)) {
                            List<ProgramAttributeServiceType> isExistConfig = programAttributeServiceTypeRepository.findByProgramIdAndServiceTypeAndAttributeIn(payload.getProgramId(),
                                    EServiceType.LIMITATION, Collections.singletonList(condition.getAttribute()));
                            if (isExistConfig.isEmpty()) {
                                throw new OpsBusinessException(OpsErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_NOT_FOUND,
                                        "Program attribute service type not found", null, new Object[]{condition.getAttribute()});
                            }
                            RuleCondition ruleCondition = new RuleCondition();
                            ruleCondition.setRuleId(limitRule.getId());
                            ruleCondition.setAttribute(condition.getAttribute());
                            ruleCondition.setOperator(condition.getOperator());
                            ruleCondition.setValue(condition.getValue());
                            ruleCondition.setDataType(condition.getDataType());
                            ruleCondition.setStatus(condition.getStatus());
                            RuleCondition ruleConditionSaved = ruleConditionRepository.save(ruleCondition);
                            conditionReqs.add(ResetRuleReq.buildConditionReq(ruleConditionSaved));
                        }
                    }
                    ResetRuleReq resetRuleReq = ResetRuleReq.buildRuleReq(limitRule);
                    resetRuleReq.setConditions(conditionReqs);
                    resetRuleReqs.add(resetRuleReq);
                }
            }
        }
        return resetRuleReqs;
    }

    private Rule createLimitRule(String limitCode, Rule rule) {
        Rule limitRule = new Rule();
        limitRule.setProgramId(rule.getProgramId());
        limitRule.setCode(String.format("%s_%s", EServiceType.LIMITATION, UUID.randomUUID()));
        limitRule.setName(rule.getName());
        limitRule.setDescription(rule.getDescription());
        limitRule.setRuleLogic(rule.getRuleLogic());
        limitRule.setServiceType(EServiceType.LIMITATION);
        limitRule.setServiceCode(limitCode);
        limitRule.setStatus(rule.getStatus());
        limitRule.setStartDate(rule.getStartDate());
        limitRule.setEndDate(rule.getEndDate());
        ruleService.save(limitRule);
        return limitRule;
    }

    @Override
    public void cancelInReview(CancelReq req) {
        String createdBy = opsReqPendingValidator.getCurrentUser();
        MakerCheckerInternalDataDetailRes detailRes = makerCheckerInternalFeignClient
                .previewDetailDefault(EMakerCheckerType.COUNTER, String.valueOf(req.getId()));
        if (!createdBy.equals(detailRes.getMadeByUserName())){
            throw new OpsBusinessException(OpsErrorCode.ACCESS_DENIED, "Access denied", req);
        }
        if (!EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue().equals(detailRes.getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to cancel",
                    LogData.createLogData().append("id", String.valueOf(req.getId())));
        }
        makerCheckerInternalFeignClient.cancelDefault(EMakerCheckerType.COUNTER, req);
    }

    private void validateRequest(APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> response, ApprovalReq req) {
        if (!EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue().equals(response.getData().getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to reject or approve",
                    LogData.createLogData()
                            .append("id", req.getId())
                            .append("approve_status", req.getStatus()));
        }
        if (response.getMeta().getCode() != HttpStatus.OK.value()) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Checker id not found",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
    }

    private void convertCounter(Counter counter, CreateCounterReq payload) {
        counter.setId(payload.getId());
        counter.setBusinessId(payload.getBusinessId());
        counter.setCode(payload.getCode());
        counter.setProgramId(payload.getProgramId());
        counter.setName(payload.getName());
        counter.setIsAwardVoucherAll(Boolean.TRUE.equals(payload.getIsAwardAllVoucher()) ? EBoolean.YES : EBoolean.NO);
        counter.setDescription(payload.getDescription());
        counter.setServiceType(payload.getServiceType());
        counter.setPeriod(payload.getPeriod());
        counter.setLevel(Objects.nonNull(payload.getCounterLevel()) ? payload.getCounterLevel() : null);
        counter.setStatus(ECommonStatus.of(payload.getCounterStatus()));
        counter.setStartDate(DateTimes.toDate(payload.getStartDate()));
        counter.setEndDate(DateTimes.toDate(payload.getEndDate()));
        counter.setType(payload.getCounterType());
        counter.setEnableRevert(payload.getEnableRevert());
        if (payload.getCounterType().equals(ECounterType.VALUE)) {
            counter.setCounterAttribute(payload.getCounterAttribute());
        }
        counter.setCounterLevelAttribute(Objects.nonNull(payload.getCounterLevelAttribute()) ? payload.getCounterLevelAttribute() : null);
        counter.setLevelType(payload.getCounterLevelType());
        counter.setCounterRuleType(payload.getCounterRuleType());
    }

    /**
     * Whether counter code is used
     *
     * @param businessId the business id
     * @param programId  the program id
     * @param code       the counter code
     */
    private void validateCodeExistInOtherReqPending(Integer businessId, Integer programId, String code) {
        MakerCheckerInternalPreviewReq filter = MakerCheckerInternalPreviewReq.builder()
                .status(EMakerCheckerStatus.PENDING.getValue())
                .properties(new HashMap<>())
                .build();
        filter.getProperties().put("payload.code", code);
        MakerCheckerInternalSearchReq searchReq = MakerCheckerInternalSearchReq.builder()
                .filter(filter)
                .build();
        List<MakerCheckerInternalDataDetailRes> res = makerCheckerInternalFeignClient.search(
                EMakerCheckerType.COUNTER.getType(), searchReq).getData();
        if (CollectionUtils.isNotEmpty(res)) {
            throw new BusinessException(ErrorCode.COUNTER_CODE_IS_BEING_USED,
                    "[VALIDATION COUNTER CODE] counter code existed in other requests pending", code, new Object[]{code});
        }
    }

    @Override
    public CounterStatisticRes counterStatistic(Integer counterId, Date startedAt, Date endedAt, String counterLevel, String code, ECommonStatus status, Pageable pageable) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Counter counter = counterService.findByIdAndBusinessId(counterId, business.getId());

        SpecificationBuilder specification = new SpecificationBuilder();

        specification.add(new SearchCriteria("counterId", counterId, SearchOperation.EQUAL));

        if (Objects.nonNull(ECounterLevel.of(counterLevel))) {
            specification.add(new SearchCriteria("levelCode", ECounterLevel.of(counterLevel), SearchOperation.EQUAL));
        } else {
            List<ProgramAttributeServiceType> listProgramAttributeServiceTypes = programAttributeServiceTypeRepository
                    .findByProgramIdAndServiceTypeAndEnableCounterLevel(counter.getProgramId(), EServiceType.COUNTER, EBoolean.YES);

            Map<String, List<String>> map = listProgramAttributeServiceTypes.stream()
                    .collect(Collectors.toMap(ProgramAttributeServiceType::getAttribute, e -> Objects.nonNull(e.getOperators()) ? e.getOperators() : new ArrayList<>(), (k1, k2) -> k2));

            List<String> counterLevelList = programTransactionAttributeService.findByProgramOrderByAttributeAsc(counter.getProgramId())
                    .stream()
                    .filter(attr -> Objects.nonNull(map.get(attr.getAttribute())))
                    .map(ProgramTransactionAttribute::getAttribute)
                    .collect(Collectors.toList());

            if (!counterLevelList.contains(counterLevel)) {
                throw new BusinessException(ErrorCode.COUNTER_LEVEL_ATTRIBUTE_INVALID);
            }

            if (!counter.getCounterLevelAttribute().equals(counterLevel)) {
                throw new BusinessException(ErrorCode.COUNTER_LEVEL_ATTRIBUTE_INVALID);
            }
        }

        if (StringUtil.isNotEmpty(code))
            specification.add(new SearchCriteria("code", code, SearchOperation.EQUAL));

        if (!ObjectUtils.isEmpty(status))
            specification.add(new SearchCriteria("status", status, SearchOperation.EQUAL));

        if (!ECounterPeriod.NONE.equals(counter.getPeriod())) {
            if (startedAt == null || endedAt == null) {
                throw new BusinessException(
                        ErrorCode.COUNTER_STATISTIC_REQUIRED_FILTER, null, null,
                        new Object[]{String.format("started_at and ended_at for %s period", counter.getPeriod().getDisplayName().toLowerCase())}
                );
            }

            specification.add(new SearchCriteria("startDate", startedAt, SearchOperation.GREATER_THAN_EQUAL_DATE));
            specification.add(new SearchCriteria("endDate", endedAt, SearchOperation.LESS_THAN_EQUAL_DATE));
        }

        List<CounterHistory> counterHistories = counterHistoryService.find(specification);

        Page<CounterHistory> counterHistoryPage = counterHistoryService.find(specification, pageable);

        BigDecimal sumCounting = BigDecimal.ZERO;
        Set<String> setCode = new HashSet<>();
        Date startDate = new Date();
        Date endDate = counterHistories.isEmpty() ? new Date() : counterHistories.get(0).getEndDate();

        for (CounterHistory c : counterHistories) {
            sumCounting = sumCounting.add(c.getCounting());
            setCode.add(c.getCode());
            if (c.getStartDate() != null && c.getStartDate().before(startDate))
                startDate = c.getStartDate();
            if (c.getEndDate() != null && c.getEndDate().after(endDate))
                endDate = c.getEndDate();
        }

        List<CounterStatisticRes.CounterHistory> histories = counterHistoryPage.stream()
                .map(c -> CounterStatisticRes.CounterHistory.builder()
                        .code(c.getCode())
                        .createdAt(c.getCreatedAt())
                        .level(c.getLevelCode())
                        .startedAt(c.getStartDate())
                        .endedAt(c.getEndDate())
                        .totalCounted(c.getCounting())
                        .unit(counter.getCounterAttribute() != null ? counter.getCounterAttribute().getValue() : null)
                        .status(c.getStatus()).build()
                ).collect(Collectors.toList());

        CounterStatisticRes.Statistic statistic = new CounterStatisticRes().new Statistic();
        statistic.setTypePeriod(counter.getPeriod().getValue());
        statistic.setTypeCounted(counter.getCounterAttribute() != null ? counter.getCounterAttribute().getValue() : null);
        statistic.setTypeLevel(counter.getLevelType().getValue());
        if (Objects.nonNull(counter.getLevel())) {
            statistic.setTypeLevelValue(counter.getLevel().getValue());
        } else {
            statistic.setTypeLevelValue(counter.getCounterLevelAttribute());
        }
        statistic.setTotalLevel(setCode.size());
        statistic.setTotalCounted(sumCounting);
        statistic.setTotalPeriod(mapPeriodToNumber(counter.getPeriod(), startDate, endDate));

        CounterStatisticRes counterStatisticRes = new CounterStatisticRes();
        counterStatisticRes.setCounterHistories(histories);
        counterStatisticRes.setStatistic(statistic);
        counterStatisticRes.setCounterStatusHeader(getCounterActiveStatus(counter));
        counterStatisticRes.setTotalHistories((int) counterHistoryPage.getTotalElements());

        return counterStatisticRes;
    }

    @Override
    public int getCounterActiveStatus(Counter counter) {
        if (counter.getEndDate() != null && counter.getEndDate().before(new Date())) {
            return 2;
        }
        Optional<CounterHistory> counterHistories = counterHistoryService.findFirstByCounterIdAndStatus(counter.getId(), ECommonStatus.ACTIVE);
        if (counterHistories.isEmpty()) {
            return 0;
        }
        return 1;
    }

    @Override
    public Integer mapPeriodToNumber(ECounterPeriod period, Date startDate, Date endDate) {
        if (ECounterPeriod.NONE.equals(period)) return null;

        Calendar startCalendar = Calendar.getInstance();
        Calendar endCalendar = Calendar.getInstance();
        endDate = new Date(endDate.getTime() + 1000); // Increase 1 second
        startCalendar.setTime(startDate);
        endCalendar.setTime(endDate);

        switch (period) {
            case YEARLY:
                return endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
            case MONTHLY:
                int diffYear = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
                return diffYear * 12 + endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);
            case DAILY:
                return (int) ((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
            default:
                return 0;
        }
    }

    private void validateAttribute(CreateCounterReq req) {
        ProgramAttributeServiceType programAttributeServiceType = programAttributeServiceTypeRepository
                .findByProgramIdAndServiceTypeAndEnableCounterLevelAndAttribute(req.getProgramId(), EServiceType.COUNTER, EBoolean.YES, req.getCounterLevelAttribute()).orElse(null);

        if (Objects.isNull(programAttributeServiceType))
            throw new BusinessException(
                    ErrorCode.COUNTER_LEVEL_ATTRIBUTE_INVALID,
                    null,
                    null
            );

        ProgramTransactionAttribute programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(req.getProgramId(), req.getCounterLevelAttribute()).orElse(null);

        if (Objects.isNull(programTransactionAttribute))
            throw new BusinessException(
                    ErrorCode.COUNTER_LEVEL_ATTRIBUTE_INVALID,
                    null,
                    null
            );
    }

    private void validateCounterRuleType(CreateCounterReq req) {
        // Only accept counterRuleType = SCHEME | POST | PRE

        // Case serviceType = SCHEME
        if (EServiceType.SCHEME.equals(req.getServiceType())) {
            // Case counterRuleType = SCHEME
            if (ECounterRuleType.SCHEME.equals(req.getCounterRuleType())) {
                // When counterRuleType = SCHEME, rule must be empty
                if (CollectionUtils.isNotEmpty(req.getRules())) {
                    throw new BusinessException(ErrorCode.COUNTER_RULE_TYPE_INVALID, null, null);
                }

                // When counterType = VALUE, only accept counterAttribute = RedeemPoint || AwardPoint || GrossAmount
                if (ECounterType.VALUE.equals(req.getCounterType())) {
                    if (!ECounterAttribute.RedeemPoint.equals(req.getCounterAttribute())
                            && !ECounterAttribute.AwardPoint.equals(req.getCounterAttribute())
                            && !ECounterAttribute.GrossAmount.equals(req.getCounterAttribute())) {
                        throw new BusinessException(ErrorCode.COUNTER_ATTRIBUTE_INVALID, null, null);
                    }
                }
            } else if (ECounterRuleType.PRE.equals(req.getCounterRuleType())) {
                // Only accept counterType = VALUE | FREQUENCY
                // When counterType = VALUE, only accept counterAttribute = GrossAmount
                if (!ECounterType.FREQUENCY.equals(req.getCounterType())) {
                    if (!ECounterType.VALUE.equals(req.getCounterType())) {
                        throw new BusinessException(ErrorCode.COUNTER_RULE_TYPE_INVALID, null, null);
                    }
                    if (!ECounterAttribute.GrossAmount.equals(req.getCounterAttribute())) {
                        throw new BusinessException(ErrorCode.COUNTER_ATTRIBUTE_INVALID, null, null);
                    }
                }
            } else {
                throw new BusinessException(ErrorCode.COUNTER_RULE_TYPE_INVALID, null, null);
            }
        } else if (EServiceType.LIMITATION.equals(req.getServiceType())) {
            // When serviceType = LIMITATION, must choose counterRuleType = POST
            if (!ECounterRuleType.POST.equals(req.getCounterRuleType())) {
                throw new BusinessException(ErrorCode.COUNTER_RULE_TYPE_INVALID, null, null);
            }

            // When counterType = VALUE, only accept counterAttribute = RedeemPoint || AwardPoint || GrossAmount
            if (ECounterType.VALUE.equals(req.getCounterType())) {
                if (!ECounterAttribute.RedeemPoint.equals(req.getCounterAttribute())
                        && !ECounterAttribute.AwardPoint.equals(req.getCounterAttribute())
                        && !ECounterAttribute.GrossAmount.equals(req.getCounterAttribute())) {
                    throw new BusinessException(ErrorCode.COUNTER_ATTRIBUTE_INVALID, null, null);
                }
            }
        }
    }

    private Boolean isEffectiveTimeValid(Long counterStartDate, Long counterEndDate, List<RuleReq> ruleReqs) {
        if (!ruleReqs.isEmpty()) {
            return ruleReqs.stream().anyMatch(
                    e -> !DateTimes.toDate(e.getStartDate()).before(DateTimes.toDate(counterStartDate))
                            || !DateTimes.toDate(e.getEndDate()).after(DateTimes.toDate(counterEndDate)));
        }
        return true;
    }

    private void validateRule(CreateCounterReq req) {
        if (ECounterRuleType.SCHEME.equals(req.getCounterRuleType()) && Objects.nonNull(req.getRules())) {
            throw new BusinessException(ErrorCode.COUNTER_RULE_MUST_EMPTY, null, null);
        }

        if (ECounterRuleType.PRE.equals(req.getCounterRuleType()) || ECounterRuleType.POST.equals(req.getCounterRuleType())) {
            if (Objects.isNull(req.getRules())) {
                throw new BusinessException(ErrorCode.COUNTER_RULE_CANNOT_EMPTY, null, null);
            }
        }
    }
}