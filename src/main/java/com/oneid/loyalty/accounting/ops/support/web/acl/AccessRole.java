package com.oneid.loyalty.accounting.ops.support.web.acl;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum AccessRole {
    MEMBER("LoyaltyAccounting_User", "loyalty_member", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT,
    }),

    MEMBER_TRANSACTION("LoyaltyAccounting_Transaction", "LoyaltyAccounting_Transaction", new AccessPermission[]{
            AccessPermission.VIEW
    }),

    MEMBER_BALANCE("LoyaltyAccounting_User_Balance", "LoyaltyAccounting_User_Balance", new AccessPermission[]{
            AccessPermission.VIEW
    }),

    BUSINESS("LoyaltyAccounting_Business", "loyalty_business", new AccessPermission[]{
            AccessPermission.VIEW,
    }),

    CURRENCY("LoyaltyAccounting_Currency", "loyalty_currency", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT,
    }),

    CURRENCY_RATE("LoyaltyAccounting_CurrencyRate", "loyalty_currency_rate", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.APPROVE_OR_REJECT,
            AccessPermission.EDIT
    }),

    PROGRAM("LoyaltyAccounting_Program", "loyalty_program_list", new AccessPermission[]{
            AccessPermission.APPROVE_OR_REJECT,
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    POOL("LoyaltyAccounting_Pool", "loyalty_pool", new AccessPermission[]{
            AccessPermission.APPROVE_OR_REJECT,
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    TIER("LoyaltyAccounting_Tier", "loyalty_tier", new AccessPermission[]{
            AccessPermission.APPROVE_OR_REJECT,
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    REFUNDING_TRANSACTION("LoyaltyAccounting_Refunding_Transaction", "LoyaltyAccounting_Refunding_Transaction", new AccessPermission[]{
            AccessPermission.EDIT
    }),

    REFUNDING_CRATE_NEW_TRANSACTION("LoyaltyAccounting_Refunding_CreateNew_Transaction", "LoyaltyAccounting_Refunding_CreateNew_Transaction", new AccessPermission[]{
            AccessPermission.EDIT
    }),

    TRANSACTION("LoyaltyAccounting_Transaction_Management", "loyalty_transaction_list", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT,
            AccessPermission.EXPORT
    }),

    TRANSACTION_REQUEST("LoyaltyAccounting_Transaction_Request", "loyalty_transaction_request", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT,
            AccessPermission.EXPORT
    }),

    MEMBER_PRODUCT_ACCOUNT("LoyaltyAccounting_Card", "LoyaltyAccounting_Card", new AccessPermission[]{
            AccessPermission.VIEW
    }),

    MEMBER_CARD("LoyaltyAccounting_MemberCard", "LoyaltyAccounting_MemberCard", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CARD("LoyaltyAccounting_Card", "LoyaltyAccounting_Card", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CORPORATION("LoyaltyAccounting_Corporation", "LoyaltyAccounting_Corporation", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CHAIN("LoyaltyAccounting_Chain", "LoyaltyAccounting_Chain", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.EDIT,
            AccessPermission.CREATE
    }),

    STORE("LoyaltyAccounting_Store", "loyalty_store", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.EDIT,
            AccessPermission.CREATE
    }),

    TERMINAL("LoyaltyAccounting_Terminal", "LoyaltyAccounting_Terminal", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    SCHEME("LoyaltyAccounting_Scheme", "loyalty_scheme", new AccessPermission[]{
            AccessPermission.APPROVE_OR_REJECT,
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CARD_POLICY("LoyaltyAccounting_CardPolicy", "LoyaltyAccounting_CardPolicy", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    MEMBER_CARD_BIN("LoyaltyAccounting_CardBin", "LoyaltyAccounting_CardBin", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    MEMBER_CARD_TYPE("LoyaltyAccounting_CardType", "LoyaltyAccounting_CardType", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    MEMBER_CARD_PR("LoyaltyAccounting_cpr", "LoyaltyAccounting_cpr", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.EXPORT
    }),

    GIFT_CARD_BIN("LoyaltyAccounting_GiftCard_CardBin", "LoyaltyAccounting_GiftCard_CardBin", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    GIFT_CARD_TYPE("LoyaltyAccounting_GiftCard_CardType", "LoyaltyAccounting_GiftCard_CardType", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    GIFT_CARD_PR("LoyaltyAccounting_GiftCard_cpr", "LoyaltyAccounting_GiftCard_cpr", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    GIFT_CARD("LoyaltyAccounting_gift_card", "LoyaltyAccounting_gift_card", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    GIFT_CARD_TRANSFER("LoyaltyAccounting_GiftCard_Transferring", "LoyaltyAccounting_GiftCard_Transferring", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.EXPORT
    }),

    MEMBER_CARD_TRANSFER("LoyaltyAccounting_MemberCardTransfer", "LoyaltyAccounting_MemberCardTransfer", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    OPERATOR("LoyaltyAccounting_Operator", "LoyaltyAccounting_Operator", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    MAKER_CHECKER("MakerChecker", "MakerChecker", new AccessPermission[]{
            AccessPermission.MAKER_ROLE,
            AccessPermission.CHECKER_ROLE
    }),

    SCHEME_SEQUENCE("LoyaltyAccounting_SchemeSequence", "LoyaltyAccounting_SchemeSequence", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    COUNTER("LoyaltyAccounting_Counter", "loyalty_counter", new AccessPermission[]{
            AccessPermission.CHECKER_ROLE,
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    LIMITATION("LoyaltyAccounting_Limit", "loyalty_limit", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    TIER_POLICY("LoyaltyAccounting_TierPolicy", "LoyaltyAccounting_TierPolicy", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    BATCH_ADJUST_PARTNER_TRANSACTION("LoyaltyAccounting_tcb_batch_adj_partner_txn", "LoyaltyAccounting_tcb_batch_adj_partner_txn", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EXPORT,
    }),

    ADJUST_MEMBER_TIER("LoyaltyAccounting_adjust_member_tier", "LoyaltyAccounting_adjust_member_tier", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.APPROVE_OR_REJECT,
            AccessPermission.EDIT,
            AccessPermission.EXPORT
    }),

    MEMBER_CARD_LISTING("LoyaltyAccounting_member_card", "LoyaltyAccounting_member_card", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    SKU_MANAGEMENT("LoyaltyAccounting_sku_management", "LoyaltyAccounting_sku_management", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.EDIT
    }),

    PROGRAM_ATTRIBUTE_MANAGEMENT("LoyaltyAccounting_program_attribute_management", "LoyaltyAccounting_program_attribute_management", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),
    MEMBER_ATTRIBUTE_MANAGEMENT("LoyaltyAccounting_member_attribute_management", "LoyaltyAccounting_member_attribute_management", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT("LoyaltyAccounting_transaction_attribute_management", "LoyaltyAccounting_transaction_attribute_management", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    SYSTEM_ATTRIBUTE_MANAGEMENT("LoyaltyAccounting_system_attribute_management", "LoyaltyAccounting_system_attribute_management", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    TIER_MATCHING_MANAGEMENT("LoyaltyAccounting_tier_matching_management", "LoyaltyAccounting_tier_matching_management", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),
    PROGRAM_CORPORATION("LoyaltyAccounting_ProgramCorporation", "loyalty_program_corporation", new AccessPermission[]{
            AccessPermission.CHECKER_ROLE,
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    PROGRAM_FUNCTION("LoyaltyAccounting_ProgramFunction", "LoyaltyAccounting_ProgramFunction", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    PROGRAM_LEVEL("LoyaltyAccounting_ProgramLevel", "LoyaltyAccounting_ProgramLevel", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CAMPAIGN("LoyaltyAccounting_Campaign", "loyalty_campaign", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    TRANSACTION_CODE("Txn_Code", "txn_code", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    MEMBER_STATUS("member_status", "member_status", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    BLOCK_MEMBER_STATUS("block_member_status", "block_member_status", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    POINT_EXCHANGE("loyalty_point_exchange", "loyalty_point_exchange", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    PAY_WITH_POINT_TRANSACTION("pwp_txn", "pwp_txn", new AccessPermission[]{
            AccessPermission.VIEW
    }),

    CPM_TRANSACTION("loyalty_cpm", "loyalty_cpm", new AccessPermission[]{
            AccessPermission.VIEW
    }),

    SCHEDULE_EVENT("loyalty_calendar_event", "loyalty_calendar_event", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    TICKET("loyalty_member_voucher", "loyalty_member_voucher", new AccessPermission[]{
            AccessPermission.EXPORT
    });

    String name;
    String code;
    AccessPermission[] permissions;

    public static AccessRole lookup(String name) {
        if (name == null) {
            return null;
        }

        return Stream.of(values())
                .filter(each -> each.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
}