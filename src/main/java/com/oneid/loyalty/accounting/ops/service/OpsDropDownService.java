package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.loyalty.accounting.ops.constant.EOpsFunctionCode;
import com.oneid.loyalty.accounting.ops.constant.EStatus;
import com.oneid.loyalty.accounting.ops.model.req.MerchantDetailReq;
import com.oneid.loyalty.accounting.ops.model.res.CardTypeRes;
import com.oneid.loyalty.accounting.ops.model.res.DropDownStoreRes;
import com.oneid.loyalty.accounting.ops.model.res.DropdownCurrencyRes;
import com.oneid.loyalty.accounting.ops.model.res.DropdownRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTypeRes;
import com.oneid.loyalty.accounting.ops.model.res.LegacyTierRes;
import com.oneid.loyalty.accounting.ops.model.res.MerchantDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.PartnerSupplierRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramRes;
import com.oneid.loyalty.accounting.ops.model.res.ReasonCodeRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityGroupByParentRes;
import com.oneid.loyalty.accounting.ops.model.res.TierPolicyRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionCodeRes;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterLevelType;
import com.oneid.oneloyalty.common.constant.ECounterRuleType;
import com.oneid.oneloyalty.common.constant.EMerchantServiceType;
import com.oneid.oneloyalty.common.constant.EMerchantType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.Collection;
import java.util.List;

public interface OpsDropDownService {
    List<DropdownRes> business(ECommonStatus status);

    List<DropdownRes> corporation(Integer businessId, ECommonStatus status);

    List<DropdownRes> getActiveCorporationsByProgramId(Integer programId);

    List<TransactionCodeRes> getActiveTransactionCodesByProgramId(Integer programId, String type, ECommonStatus status);

    List<DropdownRes> corporationByProgram(Integer programId);

    List<DropdownRes> corporationByProgram(Integer programId, ECommonStatus status);

    List<DropdownRes> chain(Integer corporationId, ECommonStatus status);

    List<DropdownRes> store(Integer chainId, ECommonStatus status);

    List<DropDownStoreRes> getActiveStoresByChainAndProgram(Integer chainId, Integer programId, String codePrefix);

    List<DropdownRes> terminal(Integer storeId, ECommonStatus status);

    List<DropdownRes> cardPolicy(Integer businessId, ECardPolicyType cardPolicyType);

    List<DropdownRes> program(Integer businessId);

    List<DropdownRes> gcBinByBusinessAndProgram(Integer businessId, Integer programId);

    List<GiftCardTypeRes> gcTypeByBusinessAndProgram(Integer businessId, Integer programId);

    List<CardTypeRes> getCardTypesByBusinessAndProgramId(Integer businessId, Integer programId);

    List<DropdownRes> currencyForTransaction(Integer businessId);

    List<DropdownCurrencyRes> baseCurrencyForTransaction(Integer businessId);

    @Deprecated
    List<LegacyTierRes> getTiers(Integer programId);

    List<LegacyTierRes> getActivatedTiers(Integer programId, Sort sort);

    List<ReasonCodeRes> getActiveReasonCodes(Integer businessId, Integer programId, EOpsFunctionCode functionCode);

    List<ReasonCodeRes> getAllReasonCodes(Integer programId, EOpsFunctionCode functionCode, ECommonStatus status);

    Collection<ShortEntityGroupByParentRes> searchTerminals(List<Integer> storeIds, String nameOrCode);

    Collection<ShortEntityGroupByParentRes> searchStores(List<Integer> chainIds, String nameOrCode);

    Collection<ShortEntityGroupByParentRes> searchChains(List<Integer> corporationIds, String nameOrCode);

    List<ProgramRes> getActivePrograms(Integer businessId, List<Integer> excludeProgramIds);

    List<TierPolicyRes> getActiveTierPolicies(Integer businessId, Integer programId);

    List<DropdownRes> getMessageEvent();

    List<PartnerSupplierRes> getActivatedPartnerSuppliers(Integer businessId);

    List<DropdownRes> getActivatedCurrenciesByBusinessCode(String businessCode);

    Page<AttributeCombobox> getSaleIntegration(String keyword, Pageable pageable);

    List<AttributeCombobox> getActivatedVouchers(Integer programId, String status, String keyword, Integer page, Integer limit);

    List<DropdownRes> geActivatedtStoresByCorporationId(Integer corporationId);

    List<DropdownRes> getActivatedChainsByCorporationCode(String businessCode, String corporationCode, ECommonStatus status);

    List<DropdownRes> getActivatedChainsByCorporationId(Integer corporationId, ECommonStatus status);

    MerchantDetailRes getMerchantDetail(MerchantDetailReq req);

    List<DropdownRes> getAllCampaign(Integer programId, ECommonStatus status);

    List<DropdownRes> getAllMemberStatus(Integer programId, ECommonStatus status);

    List<DropdownRes> getPartner(EMerchantServiceType merchantServiceType, EMerchantType merchantType, ECommonStatus status);

    List<DropdownRes> getCounters(Integer programId, EServiceType serviceType, ECommonStatus status, ECounterRuleType counterRuleType);

    List<DropdownRes> getCounterLevels(Integer programId, ECounterLevelType counterLevelType, ECommonStatus status);

    List<DropdownRes> getServiceCodeConfig(String program, String mappingProgram);
}