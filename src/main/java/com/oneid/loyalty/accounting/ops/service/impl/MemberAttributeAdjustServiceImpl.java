package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MasterWorkerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalSendEmailReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MasterWorkerMemberAttributeUpdateFeignReq;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.MemberAttributeAdjustDetailExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.MemberAttributeAdjustExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.MemberAttributeAdjustMultipleDetailExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.MemberAttributeAdjustMultipleExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberAttributeAdjustReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifyMemberAttributeAdjustReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberAttributeAdjustDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberAttributeAdjustRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.MemberAttributeAdjustService;
import com.oneid.loyalty.accounting.ops.util.AuditorAwareUtil;
import com.oneid.loyalty.accounting.ops.util.DateConverter;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.loyalty.accounting.ops.util.excel.entry.EntryContext;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERequestProcessStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.MemberAttributeUpdate;
import com.oneid.oneloyalty.common.entity.MemberRequestAttribute;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramAttribute;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.MemberAttributeUpdateRepository;
import com.oneid.oneloyalty.common.repository.ProgramAttributeRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.MemberAttributeUpdateService;
import com.oneid.oneloyalty.common.service.MemberRequestAttributeService;
import com.oneid.oneloyalty.common.service.MemberService;
import com.oneid.oneloyalty.common.service.ProgramAttributeService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.util.DateUtil;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import com.poiji.bind.Poiji;
import com.poiji.exception.PoijiExcelType;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class MemberAttributeAdjustServiceImpl implements MemberAttributeAdjustService {

    private final List<ECommonStatus> VALID_STATUSES = List.of(ECommonStatus.ACTIVE, ECommonStatus.INACTIVE);

    @Value("${app.file.max-line-3}")
    private Long fileMaxLine3;

    @Autowired
    private MemberService memberService;

    @Autowired
    private OpsCommonExcelService commonExcelService;

    @Autowired
    private ProgramAttributeService programAttributeService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private AuditorAwareUtil auditorAwareUtil;

    @Autowired
    private MemberAttributeUpdateService memberAttributeUpdateService;

    @Autowired
    private MemberRequestAttributeService memberRequestAttributeService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private ProgramService programService;

    @Autowired
    private ProgramAttributeRepository programAttributeRepository;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private MasterWorkerFeignClient masterWorkerFeignClient;

    @Autowired
    private MemberAttributeUpdateRepository memberAttributeUpdateRepository;

    @Override
    public ResourceDTO verifyExcel(VerifyMemberAttributeAdjustReq request, MultipartFile file) throws Exception {
        boolean isMultiple = EBoolean.YES.equals(request.getIsMultipleAttribute());
        String originalFilename = file.getOriginalFilename();

        if (isMultiple) {
            List<MemberAttributeAdjustMultipleExcelDTO> dtos =
                    Optional.ofNullable(mappingToMultipleExcelDto(file)).orElse(Collections.emptyList());
            boolean isValid = verifyMultipleExcelData(request.getProgramId(), dtos);
            return isValid
                    ? exportMultipleFileExcel(originalFilename, dtos, OPSConstant.VERIFY_MEMBER_ATTRIBUTE_N)
                    : null;
        } else {
            List<MemberAttributeAdjustExcelDTO> dtos =
                    Optional.ofNullable(mappingToExcelDto(file)).orElse(Collections.emptyList());
            boolean isValid = verifyExcelData(request.getProgramId(), dtos);
            return isValid ? exportFileExcel(originalFilename, dtos, OPSConstant.VERIFY_MEMBER_ATTRIBUTE_1) : null;
        }
    }

    @Override
    public ResourceDTO create(CreateMemberAttributeAdjustReq req, MultipartFile file) throws Exception {
        boolean isMultiple = EBoolean.YES.equals(req.getIsMultipleAttribute());
        String originalFilename = file.getOriginalFilename();

        if (isMultiple) {
            List<MemberAttributeAdjustMultipleExcelDTO> dtos =
                    Optional.ofNullable(this.mappingToMultipleExcelDto(file)).orElse(Collections.emptyList());
            boolean isValid = verifyMultipleExcelData(req.getProgramId(), dtos);

            if (isValid) {
                return exportMultipleFileExcel(originalFilename, dtos, OPSConstant.VERIFY_MEMBER_ATTRIBUTE_N);
            }

            Long requestId = saveMemberAttribute(req, dtos);
            return buildResourceDTO(requestId);
        } else {
            List<MemberAttributeAdjustExcelDTO> dtos =
                    Optional.ofNullable(this.mappingToExcelDto(file)).orElse(Collections.emptyList());
            boolean isValid = verifyExcelData(req.getProgramId(), dtos);

            if (isValid) {
                return exportFileExcel(originalFilename, dtos, OPSConstant.VERIFY_MEMBER_ATTRIBUTE_1);
            }

            Long requestId = saveMemberAttribute(req, dtos);
            return buildResourceDTO(requestId);
        }
    }

    @Override
    public List<ConditionAttributeDto> getAttributeByProgramId(Integer programId) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        programService.findByIdAndBusinessId(programId, business.getId());

        Collection<ProgramAttribute> programAttributes =
                programAttributeRepository.findByProgramIdOrderByCodeAsc(programId);

        return programAttributes.stream()
                .map(attr -> {
                            boolean existMasterData = EBoolean.YES.equals(attr.getHavingMasterData());
                            return ConditionAttributeDto
                                    .builder()
                                    .attribute(attr.getCode())
                                    .name(attr.getName())
                                    .description(attr.getDescription())
                                    .dataType(attr.getDataType())
                                    .dataTypeDisplay(EAttributeDataDisplayType.lookup(attr.getDataTypeDisplay()))
                                    .valueValidationPattern(attr.getValueValidationPattern())
                                    .existMasterData(existMasterData)
                                    .enableValidateMasterData(attr.isEnableValidateMasterData() ? EBoolean.YES : EBoolean.NO)
                                    .build();
                        }
                ).collect(Collectors.toList());
    }


    @Override
    public Page<MemberAttributeAdjustRes> getListAvailable(Integer programId,
                                                           String name,
                                                           EBatchRequestProcessStatus status,
                                                           Date startDate,
                                                           Date endDate,
                                                           Integer offset,
                                                           Integer limit) {

        SpecificationBuilder<MemberAttributeUpdate> specification = new SpecificationBuilder<>();

        specification.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        specification.add(new SearchCriteria("approvalStatus",  EApprovalStatus.APPROVED, SearchOperation.EQUAL));

        if (status != null) {
            specification.add(new SearchCriteria("processStatus", status, SearchOperation.EQUAL));
        }

        if (name != null){
            specification.add(new SearchCriteria("name", name, SearchOperation.MATCH));
        }

        if (startDate != null){
            specification.add(new SearchCriteria("createdAt", startDate, SearchOperation.GREATER_THAN_EQUAL_DATE));
        }

        if (endDate != null){
            specification.add(new SearchCriteria("createdAt", endDate, SearchOperation.LESS_THAN_EQUAL_DATE));
        }

        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit, Sort.by(Sort.Direction.DESC, "updatedAt"));

        Page<MemberAttributeUpdate> memberAttributeUpdatePage = memberAttributeUpdateRepository.findAll(specification, pageRequest);

        List<MemberAttributeAdjustRes> res = new ArrayList<>();

        for (MemberAttributeUpdate member : memberAttributeUpdatePage) {
            MemberAttributeAdjustRes memberAttributeAdjustRes = MemberAttributeAdjustRes.valueOf(member);
            res.add(memberAttributeAdjustRes);
        }
        return new PageImpl<>(res, pageRequest, (int) memberAttributeUpdatePage.getTotalElements());
    }


    @Override
    public Page<MemberAttributeAdjustRes> getInReviews(
            Integer programId,
            String name,
            EApprovalStatus approvalStatus,
            String createdBy,
            Date createdStart,
            Date createdEnd,
            String approvedBy,
            Date approvedStart,
            Date approvedEnd,
            Pageable pageable) {
        createdEnd = createdEnd != null ? new Date(createdEnd.getTime() + OPSConstant.TIMES_OF_DAY) : null;
        approvedEnd = approvedEnd != null ? new Date(approvedEnd.getTime() + OPSConstant.TIMES_OF_DAY) : null;

        Page<MemberAttributeUpdate> result = memberAttributeUpdateService.filter(
                pageable,
                approvalStatus,
                createdBy,
                createdStart,
                createdEnd,
                approvedBy,
                approvedStart,
                approvedEnd,
                programId,
                name
        );

        List<MemberAttributeAdjustRes> memberAttributeAdjustResList = result.stream().map(
                memberAttributeUpdate -> {

                    return MemberAttributeAdjustRes
                            .builder()
                            .code(memberAttributeUpdate.getCode())
                            .name(memberAttributeUpdate.getName())
                            .approvalStatus(memberAttributeUpdate.getApprovalStatus())
                            .approvedAt(memberAttributeUpdate.getApprovedAt())
                            .createdBy(memberAttributeUpdate.getCreatedBy())
                            .approvedBy(memberAttributeUpdate.getApprovedBy())
                            .createdAt(memberAttributeUpdate.getCreatedAt())
                            .build();
                }
        ).collect(Collectors.toList());

        return new PageImpl<>(memberAttributeAdjustResList, pageable, result.getTotalElements());
    }

    @Override
    public void approveBatchRequest(ApprovalReq req) {
        MemberAttributeUpdate memberAttributeUpdateReq = memberAttributeUpdateService.findByCode(req.getId())
                .orElseThrow(() -> new BusinessException(
                                ErrorCode.MEMBER_ATTRIBUTE_UPDATE_NOT_FOUND,
                                "Member attribute update request is not found",
                                LogData.createLogData()
                                        .append("id", req.getId())
                        )
                );

        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            validateApproved(memberAttributeUpdateReq);

            memberAttributeUpdateReq.setApprovedBy(opsReqPendingValidator.getCurrentUser());
            memberAttributeUpdateReq.setApprovedAt(new Date());
            memberAttributeUpdateReq.setApprovalStatus(EApprovalStatus.APPROVED);
            memberAttributeUpdateReq.setProcessStatus(EBatchRequestProcessStatus.PROCESSING);

            memberAttributeUpdateService.save(memberAttributeUpdateReq);

            // Call master worker to do member change request
            try {
                MasterWorkerMemberAttributeUpdateFeignReq feignRequest = MasterWorkerMemberAttributeUpdateFeignReq
                        .builder()
                        .batchRequestId(memberAttributeUpdateReq.getId())
                        .build();

                APIResponse<?> apiResponse = masterWorkerFeignClient.updateMemberAttribute(feignRequest);

                if (ErrorCode.SUCCESS.getValue() == apiResponse.getMeta().getCode()) {
                    Log.info(LogData.createLogData()
                            .append("msg", "Call to master/worker member attribute adjust successfully")
                            .append("Approve transaction batch request ", memberAttributeUpdateReq.getId())
                    );
                } else {
                    Log.error(LogData.createLogData()
                            .append("msg", "Error call to master/worker member attribute adjust")
                            .append("batch request id ", memberAttributeUpdateReq.getId())
                            .append("error code ", apiResponse.getMeta().getCode())
                            .append("error message ", apiResponse.getMeta().getMessage())
                    );
                }
            } catch (Exception e) {
                Log.error(LogData.createLogData()
                        .append("msg", "Error call to master/worker member attribute adjust")
                        .append("batch request id ", memberAttributeUpdateReq.getId())
                        .append("error message ", e.getMessage())
                );
            }
            memberAttributeUpdateReq = memberAttributeUpdateService.findById(memberAttributeUpdateReq.getId());
        } else if (EApprovalStatus.REJECTED.equals(req.getStatus())) {
            validateRejected(memberAttributeUpdateReq);
            memberAttributeUpdateReq.setApprovedBy(opsReqPendingValidator.getCurrentUser());
            memberAttributeUpdateReq.setApprovedAt(new Date());
            memberAttributeUpdateReq.setApprovalStatus(EApprovalStatus.REJECTED);
            memberAttributeUpdateReq.setRejectReason(req.getComment());
            memberAttributeUpdateReq = memberAttributeUpdateService.save(memberAttributeUpdateReq);
        } else {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "EApprovalStatus mismatch ", req.getStatus());
        }
        if (req.isSendEmail()) {
            this.sendEmail(memberAttributeUpdateReq);
        }
    }

    @Override
    public void cancelInReview(CancelReq req) {
        MemberAttributeUpdate memberAttributeUpdateReq = memberAttributeUpdateService.findByCode(req.getId().toString())
                .orElseThrow(() -> new BusinessException(
                                ErrorCode.MEMBER_ATTRIBUTE_UPDATE_NOT_FOUND,
                                "Member attribute update request is not found",
                                LogData.createLogData()
                                        .append("id", req.getId())
                        )
                );
        if (!EApprovalStatus.PENDING.equals(memberAttributeUpdateReq.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to cancel",
                    LogData.createLogData().append("id", req.getId()));
        }

        memberAttributeUpdateReq.setApprovalStatus(EApprovalStatus.CANCELLED);

        memberAttributeUpdateService.save(memberAttributeUpdateReq);

        if (req.isNotification()) {
            this.sendEmail(memberAttributeUpdateReq);
        }
    }

    @Override
    public MemberAttributeAdjustDetailRes getDetail(String code) {
        MemberAttributeUpdate entity = memberAttributeUpdateService.findByCode(code)
                .orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_ATTRIBUTE_UPDATE_NOT_FOUND, null, null));

        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Program program = programService.findByIdAndBusinessId(entity.getProgramId(), business.getId());

        MemberRequestAttribute memberRequestAttribute =
                memberRequestAttributeService.findFirstByRequestId(entity.getId())
                        .orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_REQUEST_ATTRIBUTE_NOT_FOUND, null, null));

        return MemberAttributeAdjustDetailRes
                .builder()
                .approvalStatus(entity.getApprovalStatus())
                .createdBy(entity.getCreatedBy())
                .createdAt(entity.getCreatedAt())
                .approvedBy(StringUtils.isEmpty(entity.getApprovedBy()) ? null : entity.getApprovedBy())
                .approvedAt(entity.getApprovedAt() == null ? null : entity.getApprovedAt())
                .reason(StringUtils.isEmpty(entity.getReason()) ? null : entity.getReason())
                .processStatus(entity.getProcessStatus())
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .code(entity.getCode())
                .name(entity.getName())
                .description(StringUtils.isEmpty(entity.getDescription()) ? null : entity.getDescription())
                .isMultipleAttribute(entity.getIsMultipleAttribute())
                .totalRequests(entity.getTotalRequests())
                .failedRequests(Objects.nonNull(entity.getFailedRequests()) ? entity.getFailedRequests() : null)
                .successRequests(Objects.nonNull(entity.getSuccessRequests()) ? entity.getSuccessRequests() : null)
                .memberAttribute(EBoolean.NO.equals(entity.getIsMultipleAttribute()) ? memberRequestAttribute.getMemberAttribute() : null)
                .attributeValue(EBoolean.NO.equals(entity.getIsMultipleAttribute()) ? memberRequestAttribute.getAttributeValue() : null)
                .startDate(EBoolean.NO.equals(entity.getIsMultipleAttribute()) ? memberRequestAttribute.getStartDate() : null)
                .endDate(EBoolean.NO.equals(entity.getIsMultipleAttribute()) ? memberRequestAttribute.getEndDate() : null)
                .status(EBoolean.NO.equals(entity.getIsMultipleAttribute()) ? memberRequestAttribute.getStatus() : null)
                .rejectReason(StringUtils.isEmpty(entity.getRejectReason()) ? null : entity.getRejectReason())
                .completeAt(EApprovalStatus.APPROVED.equals(entity.getApprovalStatus()) ? entity.getUpdatedAt() : null)
                .build();
    }

    @Override
    public Page<MemberAttributeAdjustDetailRes> getDetails(String code, ERequestProcessStatus processStatus, Pageable pageable) {
        MemberAttributeUpdate entity = memberAttributeUpdateService.findByCode(code)
                .orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_ATTRIBUTE_UPDATE_NOT_FOUND, null, null));

        SpecificationBuilder<MemberRequestAttribute> specificationBuilder = new SpecificationBuilder<>();
        specificationBuilder.add(new SearchCriteria("requestId", entity.getId(), SearchOperation.EQUAL));
        if (Objects.nonNull(processStatus)) {
            specificationBuilder.add(new SearchCriteria("processStatus", processStatus, SearchOperation.EQUAL));
        }

        Page<MemberRequestAttribute> page = memberRequestAttributeService.find(specificationBuilder, pageable);

        return page.map(request ->
                MemberAttributeAdjustDetailRes
                        .builder()
                        .loyaltyCusId(request.getLoyaltyCusId())
                        .memberAttribute(EBoolean.YES.equals(entity.getIsMultipleAttribute()) ? request.getMemberAttribute() : null)
                        .attributeValue(EBoolean.YES.equals(entity.getIsMultipleAttribute()) ? request.getAttributeValue() : null)
                        .startDate(EBoolean.YES.equals(entity.getIsMultipleAttribute()) ? request.getStartDate() : null)
                        .endDate(EBoolean.YES.equals(entity.getIsMultipleAttribute()) ? request.getEndDate() : null)
                        .status(EBoolean.YES.equals(entity.getIsMultipleAttribute()) ? request.getStatus() : null)
                        .errorMessage(StringUtils.isEmpty(request.getErrorMessage()) ? null : request.getErrorMessage())
                        .attributeProcessStatus(Objects.nonNull(request.getProcessStatus()) ? request.getProcessStatus() : null)
                        .build());
    }

    @Override
    public ResourceDTO exportFile(String code) {
        MemberAttributeUpdate entity = memberAttributeUpdateService.findByCode(code)
                .orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_ATTRIBUTE_UPDATE_NOT_FOUND, null, null));

        List<MemberAttributeAdjustDetailRes> requests = new LinkedList<>();

        Pageable pageable = PageRequest.of(0, OPSConstant.SIZE_OF_BATCH, Sort.by("id").ascending());

        Page<MemberAttributeAdjustDetailRes> page;

        while (true) {
            page = getDetails(code, null, pageable);
            requests.addAll(page.getContent());
            if (page.hasNext()) {
                pageable = pageable.next();
            } else {
                break;
            }
        }

        EntryContext context = EntryContext
                .builder()
                .moduleId(OPSConstant.ATTRIBUTE)
                .objectId(EBoolean.YES.equals(entity.getIsMultipleAttribute()) ? OPSConstant.MEMBER_ATTRIBUTE_N : OPSConstant.MEMBER_ATTRIBUTE_1)
                .build();

        String ddMMyyyy = DateUtil.formatToStringDDMMYYYY(new Date());

        String fileName = String.format(
                OPSConstant.FILE_NAME_EXPORT_MEMBER_ATTRIBUTE_ADJUST,
                ddMMyyyy,
                entity.getName()
        );

        if (EBoolean.YES.equals(entity.getIsMultipleAttribute())) {
            List<MemberAttributeAdjustMultipleDetailExcelDTO> data = requests
                    .stream()
                    .map(this::buildMultipleExport)
                    .collect(Collectors.toList());
            return commonExcelService.opsExport(context, fileName, data);
        } else {
            List<MemberAttributeAdjustDetailExcelDTO> data = requests
                    .stream()
                    .map(this::buildExport)
                    .collect(Collectors.toList());
            return commonExcelService.opsExport(context, fileName, data);
        }
    }

    private void validateApproved(MemberAttributeUpdate batchRequest) {
        if (!EApprovalStatus.PENDING.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.MEMBER_ATTRIBUTE_UPDATE_APPROVAL_STATUS_NOT_VALID);
        }
        if (!EBatchRequestProcessStatus.PENDING.equals(batchRequest.getProcessStatus())) {
            throw new BusinessException(ErrorCode.MEMBER_ATTRIBUTE_UPDATE_PROCESS_STATUS_NOT_VALID);
        }
    }

    private void validateRejected(MemberAttributeUpdate batchRequest) {
        if (!EApprovalStatus.PENDING.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.MEMBER_ATTRIBUTE_UPDATE_APPROVAL_STATUS_NOT_VALID);
        }
        if (!EBatchRequestProcessStatus.PENDING.equals(batchRequest.getProcessStatus())) {
            throw new BusinessException(ErrorCode.MEMBER_ATTRIBUTE_UPDATE_PROCESS_STATUS_NOT_VALID);
        }
    }

    private List<MemberAttributeAdjustExcelDTO> mappingToExcelDto(MultipartFile file) throws Exception {
        return Poiji.fromExcel(file.getInputStream(), PoijiExcelType.XLSX, MemberAttributeAdjustExcelDTO.class);
    }

    private List<MemberAttributeAdjustMultipleExcelDTO> mappingToMultipleExcelDto(MultipartFile file) throws Exception {
        return Poiji.fromExcel(file.getInputStream(), PoijiExcelType.XLSX, MemberAttributeAdjustMultipleExcelDTO.class);
    }

    private boolean verifyExcelData(Integer programId, List<MemberAttributeAdjustExcelDTO> dtos) {
        isOverMaxSizeLimit(dtos);

        boolean hasError = false;

        if (dtos.isEmpty()) {
            MemberAttributeAdjustExcelDTO errorDto = new MemberAttributeAdjustExcelDTO();
            errorDto.setResponseStatus(OPSConstant.INVALID);
            errorDto.setErrorMessage("loyalty_id is a required field");
            dtos.add(errorDto);
            hasError = true;

            return hasError;
        }

        Set<String> seenLoyaltyIDs = new HashSet<>();

        for (MemberAttributeAdjustExcelDTO dto : dtos) {
            try {
                if (StringUtils.isBlank(dto.getLoyaltyId())) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "loyalty_id is a required field", null);
                }

                if (!seenLoyaltyIDs.add(dto.getLoyaltyId())) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "loyalty_id is duplicated", null);
                }

                if (!isLoyaltyIDExist(programId, dto.getLoyaltyId())) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "loyalty_id not found", null);
                }

                dto.setResponseStatus(OPSConstant.VALID);

            } catch (BusinessException e) {
                dto.setResponseStatus(OPSConstant.INVALID);
                dto.setErrorMessage(e.getMessage());
                hasError = true;
            }
        }

        return hasError;
    }

    private boolean verifyMultipleExcelData(Integer programId, List<MemberAttributeAdjustMultipleExcelDTO> dtos) {
        isOverMaxSizeLimit(dtos);

        boolean hasError = false;

        if (dtos.isEmpty()) {
            MemberAttributeAdjustMultipleExcelDTO errorDto = new MemberAttributeAdjustMultipleExcelDTO();
            errorDto.setResponseStatus(OPSConstant.INVALID);
            errorDto.setErrorMessage("loyalty_id, member_attribute, attribute_value, status is a required field");
            dtos.add(errorDto);
            hasError = true;

            return hasError;
        }

        for (MemberAttributeAdjustMultipleExcelDTO dto : dtos) {
            try {
                if (StringUtils.isBlank(dto.getLoyaltyId())) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "loyalty_id is a required field", null);
                }

                if (!isLoyaltyIDExist(programId, dto.getLoyaltyId())) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "loyalty_id not found", null);
                }

                if (StringUtils.isBlank(dto.getMemberAttribute())) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "member_attribute is a required field", null);
                }

                if (Objects.isNull(programAttributeService.findByProgramIdAndAttribute(programId, dto.getMemberAttribute()))) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "member_attribute not found", null);
                }

                if (StringUtils.isBlank(dto.getAttributeValue())) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "attribute_value is a required field", null);
                }

                Date startDate = DateConverter.parseDate(dto.getStartDate());
                Date endDate = DateConverter.parseDate(dto.getEndDate());

                if (StringUtils.isNotBlank(dto.getStartDate()) && startDate == null) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "start_date is not in correct format", null);
                }

                if (StringUtils.isNotBlank(dto.getEndDate()) && endDate == null) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "end_date is not in correct format", null);
                }

                if (startDate == null && endDate != null) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "start_date must be required if end_date has been entered and vice versa", null);
                }

                if (startDate != null && endDate == null) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "end_date must be required if start_date has been entered and vice versa", null);
                }


                if (startDate != null && startDate.after(endDate)) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "start_date must be less than end_date", null);
                }

                if (StringUtils.isBlank(dto.getStatus())) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "status is a required field", null);
                }

                ECommonStatus status = ECommonStatus.of(dto.getStatus());

                if (Objects.isNull(status)) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "status is not in correct format", null);
                }

                if (!VALID_STATUSES.contains(status)) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "status is not in correct format", null);
                }

            } catch (BusinessException e) {
                dto.setResponseStatus(OPSConstant.INVALID);
                dto.setErrorMessage(e.getMessage());
                hasError = true;
            }
        }

        return hasError;
    }

    private boolean isLoyaltyIDExist(Integer programId, String loyaltyID) {
        Optional<Member> memberOpt = memberService.findMemberByProgramIdAndPartnerCustomerId(programId, loyaltyID);
        return memberOpt.isPresent();
    }

    private void isOverMaxSizeLimit(List<?> dtos) {
        if (dtos.size() > this.fileMaxLine3) {
            throw new BusinessException(ErrorCode.FILE_MAX_SIZE);
        }
    }

    private ResourceDTO exportMultipleFileExcel(String fileName, List<MemberAttributeAdjustMultipleExcelDTO> dtos, String objectId) {
        EntryContext context = EntryContext.builder()
                .moduleId(OPSConstant.ATTRIBUTE)
                .objectId(objectId)
                .build();
        return commonExcelService.opsExport(context, fileName, dtos);
    }

    private ResourceDTO exportFileExcel(String fileName, List<MemberAttributeAdjustExcelDTO> dtos, String objectId) {
        EntryContext context = EntryContext.builder()
                .moduleId(OPSConstant.ATTRIBUTE)
                .objectId(objectId)
                .build();
        return commonExcelService.opsExport(context, fileName, dtos);
    }

    private ResourceDTO buildResourceDTO(Long requestId) {
        return ResourceDTO.builder()
                .filename(String.valueOf(requestId))
                .resource(null)
                .build();
    }

    private Long saveMemberAttribute(CreateMemberAttributeAdjustReq req, List<?> dtos) {
        MemberAttributeUpdate requestEntity = new MemberAttributeUpdate();

        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        requestEntity.setBusinessId(business.getId());
        requestEntity.setProgramId(req.getProgramId());
        requestEntity.setCode(UUID.randomUUID().toString());
        requestEntity.setName(req.getName());
        requestEntity.setDescription(req.getDescription());
        requestEntity.setIsMultipleAttribute(req.getIsMultipleAttribute());
        requestEntity.setTotalRequests(dtos.size());
        requestEntity.setProcessStatus(EBatchRequestProcessStatus.PENDING);
        requestEntity.setApprovalStatus(EApprovalStatus.PENDING);
        requestEntity.setReason(req.getMadeReason());

        MemberAttributeUpdate result = memberAttributeUpdateService.save(requestEntity);

        List<MemberRequestAttribute> details = new ArrayList<>();

        for (Object dtoObj : dtos) {
            MemberRequestAttribute detail = new MemberRequestAttribute();
            detail.setRequestId(result.getId());
            detail.setProcessStatus(ERequestProcessStatus.PENDING);

            if (dtoObj instanceof MemberAttributeAdjustExcelDTO) {
                MemberAttributeAdjustExcelDTO dto = (MemberAttributeAdjustExcelDTO) dtoObj;

                detail.setLoyaltyCusId(dto.getLoyaltyId());
                detail.setMemberAttribute(req.getMemberAttribute());
                detail.setAttributeValue(req.getAttributeValue());
                detail.setStartDate(req.getStartDate() != null ? req.getStartDate() : null);
                detail.setEndDate(req.getEndDate() != null ? req.getEndDate() : null);
                detail.setStatus(req.getStatus());
            } else if (dtoObj instanceof MemberAttributeAdjustMultipleExcelDTO) {
                MemberAttributeAdjustMultipleExcelDTO dto = (MemberAttributeAdjustMultipleExcelDTO) dtoObj;

                detail.setLoyaltyCusId(dto.getLoyaltyId());
                detail.setMemberAttribute(dto.getMemberAttribute());
                detail.setAttributeValue(dto.getAttributeValue());
                detail.setStartDate(StringUtils.isNotBlank(dto.getStartDate()) ? DateConverter.parseDate(dto.getStartDate()) : null);
                detail.setEndDate(StringUtils.isNotBlank(dto.getEndDate()) ? DateConverter.parseDate(dto.getEndDate()) : null);
                detail.setStatus(ECommonStatus.of(dto.getStatus()));
            }

            details.add(detail);
        }

        if (req.isSendEmail()) {
            this.sendEmail(result);
        }

        memberRequestAttributeService.saveAll(details);
        return result.getId();
    }

    private void sendEmail(MemberAttributeUpdate request) {
        MakerCheckerInternalSendEmailReq req = MakerCheckerInternalSendEmailReq.builder()
                .id(String.valueOf(request.getId()))
                .status(request.getApprovalStatus().name())
                .madeReason(request.getReason())
                .madeByUserName(request.getCreatedBy())
                .createdAt(request.getCreatedAt())
                .build();

        makerCheckerInternalFeignClient.sendEmailDefault(EMakerCheckerType.MEMBER_ATTRIBUTE_ADJUST, req);
    }

    private MemberAttributeAdjustDetailExcelDTO buildExport(MemberAttributeAdjustDetailRes res) {
        MemberAttributeAdjustDetailExcelDTO entry = new MemberAttributeAdjustDetailExcelDTO();

        entry.setLoyaltyId(res.getLoyaltyCusId());
        entry.setProcessStatus(Objects.isNull(res.getAttributeProcessStatus()) ? null : res.getAttributeProcessStatus().getDisplayName());
        entry.setErrorMessage(StringUtils.isEmpty(res.getErrorMessage()) ? null : res.getErrorMessage());

        return entry;
    }

    private MemberAttributeAdjustMultipleDetailExcelDTO buildMultipleExport(MemberAttributeAdjustDetailRes res) {
        MemberAttributeAdjustMultipleDetailExcelDTO entry = new MemberAttributeAdjustMultipleDetailExcelDTO();

        entry.setLoyaltyId(res.getLoyaltyCusId());
        entry.setMemberAttribute(res.getMemberAttribute());
        entry.setAttributeValue(res.getAttributeValue());
        entry.setStartDate(res.getStartDate() == null ? null : DateTimes.formatDateTime(res.getStartDate()));
        entry.setEndDate(res.getEndDate() == null ? null : DateTimes.formatDateTime(res.getEndDate()));
        entry.setStatus(res.getStatus().getDisplayName());
        entry.setProcessStatus(Objects.isNull(res.getAttributeProcessStatus()) ? null : res.getAttributeProcessStatus().getDisplayName());
        entry.setErrorMessage(StringUtils.isEmpty(res.getErrorMessage()) ? null : res.getErrorMessage());

        return entry;
    }
}