package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@JsonInclude(value = Include.NON_NULL)
@EqualsAndHashCode
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
public class ProductInfoRes {
    private String arrangementId;

    private String prdAccArrId;

    private DropdownRes productCode;

    private DropdownRes productLv1;

    private DropdownRes productLv2;

    private DropdownRes productLv3;

    private DropdownRes productLv4;

    private DropdownRes productLv5;

    private DropdownRes productLv6;

    private DropdownRes prdBranchCode;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date prodOpenDate;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date prodExpireDate;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date prdBillCycle;

    private DropdownRes prdStatus;

    private DropdownRes prdCcyCode;

    private DropdownRes prdCombo;

    private DropdownRes prdJointHolderInd;

    private DropdownRes prdAutoBillInd;

    private DropdownRes prdSalesChannel;

    private DropdownRes prdPaymentDate;

    private DropdownRes prdIssueDate;

    private DropdownRes prdInsuranceRenew;
}