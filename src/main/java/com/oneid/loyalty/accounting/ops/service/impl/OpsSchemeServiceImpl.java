package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.config.MakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerStatus;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyRuleFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltySchemeFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetSchemeRuleReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.kafka.event.ResetRuleEvent;
import com.oneid.loyalty.accounting.ops.kafka.event.ResetSchemeRuleEvent;
import com.oneid.loyalty.accounting.ops.mapper.SchemeMapper;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateSchemeReq;
import com.oneid.loyalty.accounting.ops.model.req.EscrowedPointRes;
import com.oneid.loyalty.accounting.ops.model.req.SchemeLimitReq;
import com.oneid.loyalty.accounting.ops.model.req.SchemeRuleCounter;
import com.oneid.loyalty.accounting.ops.model.req.UpdateSchemeReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifyCreateListSchemeRuleReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifySchemeInfoReq;
import com.oneid.loyalty.accounting.ops.model.res.CounterRes;
import com.oneid.loyalty.accounting.ops.model.res.CounterShortInformationRes;
import com.oneid.loyalty.accounting.ops.model.res.DropdownRes;
import com.oneid.loyalty.accounting.ops.model.res.FormulaGroupRes;
import com.oneid.loyalty.accounting.ops.model.res.PoolShortRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRecordRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeBaseRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeDetailInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeEntryRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeLimitRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionCodeRes;
import com.oneid.loyalty.accounting.ops.service.OpsConditionService;
import com.oneid.loyalty.accounting.ops.service.OpsFormulaService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleSchemeService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.service.OpsSchemeService;
import com.oneid.loyalty.accounting.ops.util.AuditorAwareUtil;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.MongoSearchUtil;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterRuleType;
import com.oneid.oneloyalty.common.constant.EEscrowedPointExpirePolicy;
import com.oneid.oneloyalty.common.constant.EFormulaType;
import com.oneid.oneloyalty.common.constant.EPayWithPointExpiryPolicy;
import com.oneid.oneloyalty.common.constant.EPoolType;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ETransactionCodeType;
import com.oneid.oneloyalty.common.constant.ETransactionType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Campaign;
import com.oneid.oneloyalty.common.entity.Counter;
import com.oneid.oneloyalty.common.entity.CounterLevelObject;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.EscrowedPoint;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramTransactionAttribute;
import com.oneid.oneloyalty.common.entity.RewardPool;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeCounterCondition;
import com.oneid.oneloyalty.common.entity.SchemeLimit;
import com.oneid.oneloyalty.common.entity.SchemeLinkCounter;
import com.oneid.oneloyalty.common.entity.SchemeTransactionCode;
import com.oneid.oneloyalty.common.entity.TransactionCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CounterRepository;
import com.oneid.oneloyalty.common.repository.ProgramPoolRepository;
import com.oneid.oneloyalty.common.repository.SchemeLinkCounterRepository;
import com.oneid.oneloyalty.common.repository.SchemeTransactionCodeRepository;
import com.oneid.oneloyalty.common.repository.TransactionCodeRepository;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CampaignService;
import com.oneid.oneloyalty.common.service.CounterLevelObjectService;
import com.oneid.oneloyalty.common.service.CounterService;
import com.oneid.oneloyalty.common.service.CurrencyService;
import com.oneid.oneloyalty.common.service.EscrowedPointService;
import com.oneid.oneloyalty.common.service.PoolService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.service.RewardPoolService;
import com.oneid.oneloyalty.common.service.SchemeCounterConditionService;
import com.oneid.oneloyalty.common.service.SchemeLimitService;
import com.oneid.oneloyalty.common.service.SchemeLinkCounterService;
import com.oneid.oneloyalty.common.service.SchemeService;
import com.oneid.oneloyalty.common.service.SchemeTransactionCodeService;
import com.oneid.oneloyalty.common.util.LogData;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
public class OpsSchemeServiceImpl implements OpsSchemeService {

    @Autowired
    private SchemeService schemeService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private PoolService poolService;

    @Autowired
    private RewardPoolService rewardPoolService;

    @Autowired
    private OpsRuleService opsRuleService;

    @Autowired
    private OpsFormulaService opsFormulaService;

    @Autowired
    MakerCheckerFeignClient makerCheckerFeignClient;

    @Autowired
    MakerCheckerConfigParam makerCheckerConfigParam;

    @Autowired
    private OpsConditionService opsConditionService;

    @Autowired
    private AuditorAwareUtil auditorAwareUtil;

    @Autowired
    private ProgramPoolRepository programPoolRepository;

    @Autowired
    private SchemeTransactionCodeService schemeTransactionCodeService;

    @Autowired
    CurrencyService currencyService;

    @Autowired
    private SchemeLinkCounterRepository schemeLinkCounterRepository;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private OpsRuleSchemeService opsRuleSchemeService;

    @Autowired
    private SchemeCounterConditionService schemeCounterConditionService;

    @Autowired
    private CampaignService campaignService;

    @Autowired
    private CounterRepository counterRepository;

    @Autowired
    private SchemeLinkCounterService schemeLinkCounterService;

    @Autowired
    private CounterService counterService;

    @Autowired
    private SchemeTransactionCodeRepository schemeTransactionCodeRepository;

    @Autowired
    private TransactionCodeRepository transactionCodeRepository;

    @Autowired
    private EscrowedPointService escrowedPointService;

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private OneloyaltyRuleFeignClient oneloyaltyRuleFeignClient;

    @Autowired
    private OneloyaltySchemeFeignClient oneloyaltySchemeFeignClient;

    @Autowired
    private ProgramTransactionAttributeService programTransactionAttributeService;

    @Autowired
    private CounterLevelObjectService counterLevelObjectService;

    @Autowired
    private SchemeLimitService schemeLimitService;

    @Override
    public void verifySchemeInfo(VerifySchemeInfoReq req) {
        verifySchemeInfoExceptCode(req, null);
    }

    @Override
    public void verifySchemeCombine(CreateSchemeReq req) {
        verifySchemeCombineExceptCode(req, null);
    }

    @Override
    public Object getApproveCreate(CreateSchemeReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        req.setBusinessId(business.getId());
        programService.findByIdAndBusinessId(req.getProgramId(), business.getId());
        verifySchemeRequest(req);
        verifyCounterRuleInCounterIds(req.getCounterIds(), req.getRuleLimits());
        verifySchemeCombine(req);
        validationSchemeCodeDoesNotExistInOtherReqPending(req.getBusinessId(), req.getProgramId(), req.getSchemeCode());
        if (Objects.isNull(req.getPushEvent())) {
            req.setPushEvent(EBoolean.NO);
        }
        req.setRequestType(ERequestType.CREATE);

        return makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.SCHEME, UUID.randomUUID().toString(), req, req.getMadeReason(), req.isSendEmail());
    }

    @Override
    public Object getApproveUpdate(UpdateSchemeReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        programService.findByIdAndBusinessId(req.getProgramId(), business.getId());
        Scheme scheme = schemeService.find(req.getSchemeId()).orElseThrow(
                () -> new BusinessException(ErrorCode.SCHEME_NOT_FOUND, "Scheme not found", req)
        );
        req.setBusinessId(business.getId());
        verifySchemeRequest(req);
        setFieldNotUpdate(req, scheme);
        verifyCounterRuleInCounterIds(req.getCounterIds(), req.getRuleLimits());
        verifySchemeCombineExceptCode(req, scheme.getCode());
        opsReqPendingValidator.verifyEditKey(req.getEditKey(), scheme.getRequestCode(), scheme.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.SCHEME.getType(), scheme.getRequestCode());
        if (Objects.isNull(scheme.getRequestCode())) {
            scheme.setRequestCode(UUID.randomUUID().toString());
            // update request_code in database if request_code is null
            schemeService.save(scheme);
        }
        if (Objects.isNull(req.getPushEvent())) {
            req.setPushEvent(EBoolean.NO);
        }
        req.setRequestType(ERequestType.EDIT);
        return makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.SCHEME,
                scheme.getRequestCode(),
                req, req.getMadeReason(), req.isSendEmail());
    }

    private void setFieldNotUpdate(UpdateSchemeReq req, Scheme scheme) {
        req.setProgramId(scheme.getProgramId());
        req.setSchemeCode(scheme.getCode());
        req.setSchemeType(scheme.getSchemeType());

        if (Objects.nonNull(scheme.getRewardPoolId())) {
            req.setPoolId(scheme.getRewardPoolId());
        } else if (Objects.nonNull(scheme.getPoolId())) {
            Pool pool = poolService.findActive(scheme.getPoolId());
            if (Objects.nonNull(pool.getRefId())) {
                RewardPool rewardPool = rewardPoolService.findActive(pool.getRefId());
                if (Objects.nonNull(rewardPool)) {
                    req.setPoolId(rewardPool.getId());
                }
            }
        }

        req.setPointPoolId(scheme.getPoolId());
        req.setCampaignId(scheme.getCampaignId());
    }

    private SchemeDetailRes setInfoScheme(Scheme scheme, String editKey) {
        Collection<ConditionAttributeDto> attributes = opsConditionService.conditionAttributeDtos(scheme.getProgramId());
        Program program = programService.findById(scheme.getProgramId());
        Business business = businessService.findById(program.getBusinessId());
        Campaign campaign = null;
        Currency currency = null;
        if (Objects.nonNull(scheme.getCampaignId())) {
            campaign = campaignService.find(scheme.getCampaignId()).orElse(null);
        }

        RewardPool rewardPool = null;
        Pool pool = null;
        if (Objects.nonNull(scheme.getRewardPoolId())) {
            rewardPool = rewardPoolService.findById(scheme.getRewardPoolId()).orElse(null);
            if (Objects.nonNull(rewardPool) && EPoolType.POINT.equals(rewardPool.getPoolType())) {
                pool = poolService.findByCodeAndBusinessId(business.getId(), rewardPool.getPoolCode()).orElse(null);
                if (Objects.nonNull(pool)) {
                    currency = currencyService.find(pool.getCurrencyId()).orElse(null);
                }
            }
        } else {
            if (Objects.nonNull(scheme.getPoolId())) {
                // Return null when pool is not found, not throw error
                pool = poolService.find(scheme.getPoolId()).orElse(null);
                if (Objects.nonNull(pool) && Objects.nonNull(pool.getRefId())) {
                    rewardPool = rewardPoolService.findById(pool.getRefId()).orElse(null);
                }
            }
            if (pool != null) {
                currency = currencyService.find(pool.getCurrencyId()).orElse(null);
            }
        }

        List<SchemeLimit> schemeLimitList = schemeLimitService.findActiveBySchemeId(scheme.getId());

        List<SchemeLimitRes> schemeLimitResList = schemeLimitList.stream().map(
                e -> {
                    Counter counter = counterService.findById(e.getCounterId()).orElse(null);
                    if (Objects.nonNull(counter)) {
                        return SchemeLimitRes.builder()
                                .id(e.getId())
                                .code(counter.getCode())
                                .name(counter.getName())
                                .counterId(counter.getId())
                                .allowAwardingRemainingValue(e.getAllowAwardingRemainingValue())
                                .value(e.getValue())
                                .counterRuleType(counter.getCounterRuleType())
                                .build();

                    }
                    return SchemeLimitRes.builder()
                            .id(e.getId())
                            .allowAwardingRemainingValue(e.getAllowAwardingRemainingValue())
                            .value(e.getValue())
                            .build();
                }
        ).collect(Collectors.toList());

        List<SchemeCounterCondition> schemeCounterConditions = schemeCounterConditionService.findActiveBySchemeId(scheme.getId());

        SchemeDetailRes result = new SchemeDetailRes();

        result.setStatus(scheme.getStatus());
        result.setProgram(nullSafer(program, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())));
        result.setCampaign(nullSafer(campaign, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())));
        result.setId(scheme.getId());
        result.setSchemeCode(scheme.getCode());
        result.setSchemeName(scheme.getName());
        result.setSchemeEnName(scheme.getEnName());
        result.setEndDate(scheme.getEndDate());
        result.setStartDate(scheme.getStartDate());
        result.setDescription(scheme.getDescription());
        result.setEnDescription(scheme.getEnDescription());
        result.setProposalCode(scheme.getProposalCode());
        result.setPoolShort(PoolShortRes.of(rewardPool, currency));
        result.setSchemeType(scheme.getSchemeType());
        result.setExpirePolicyType(scheme.getExpirePolicyType());
        result.setExpirePolicyValue(getExpirePolicyValue(scheme.getExpirePolicyType(), scheme.getPeriodDay(), scheme.getExpiredTimeFixed()));
        result.setCreatedBy(scheme.getCreatedBy());
        result.setCreatedAt(scheme.getCreatedAt());
        result.setUpdatedBy(scheme.getUpdatedBy());
        result.setUpdatedAt(scheme.getUpdatedAt());
        result.setApprovedBy(scheme.getApprovedBy());
        result.setApprovedAt(scheme.getApprovedAt());
        result.setRuleLogic(scheme.getRuleLogic());
        result.setRoundingRule(scheme.getRoundingRule());
        result.setMinAmount(scheme.getMinPoint());
        result.setMaxAmount(scheme.getMaxPoint());
        result.setProposalCode(scheme.getProposalCode());
        result.setExpirePolicyType(scheme.getExpirePolicyType());
        result.setExpirePolicyValue(getExpirePolicyValue(scheme.getExpirePolicyType(), scheme.getPeriodDay(), scheme.getExpiredTimeFixed()));
        result.setRuleList(opsRuleService.findAllBySchemeId(scheme.getId(), attributes));
        result.setFormulaGroup(opsFormulaService.getFormulaGroupByScheme(scheme, attributes));
        result.setPoolType(Objects.nonNull(scheme.getPoolId()) ? EPoolType.POINT : EPoolType.VOUCHER);
        result.setEditKey(editKey);
        result.setPushEvent(scheme.getPushEvent());
        result.setSchemeLimitRes(schemeLimitResList);

        SchemeRuleCounter ruleCounter = new SchemeRuleCounter();
        List<SchemeRuleCounter.SchemeCounterRuleCondition> conditions = schemeCounterConditions
                .stream().map(item -> {
                    SchemeRuleCounter.SchemeCounterRuleCondition c = new SchemeRuleCounter.SchemeCounterRuleCondition();
                    c.setId(item.getId());
                    c.setCounterId(item.getCounterId());
                    Counter counterRule = counterService.findById(item.getCounterId()).orElse(null);
                    CounterLevelObject counterLevelObject = null;
                    ProgramTransactionAttribute programTransactionAttribute = null;
                    if (Objects.nonNull(counterRule)) {
                        if (Objects.nonNull(counterRule.getLevel())) {
                            counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(program.getId(), counterRule.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
                        }
                        programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(program.getId(), counterRule.getCounterLevelAttribute()).orElse(null);
                    }
                    ShortEntityRes counterRuleInfo = new ShortEntityRes();

                    if (Objects.nonNull(counterRule)) {
                        counterRuleInfo.setId(counterRule.getId());
                        counterRuleInfo.setCode(counterRule.getCode());
                        counterRuleInfo.setName(counterRule.getName());

                        c.setCounter(counterRuleInfo);
                        c.setCounterPeriod(counterRule.getPeriod());
                        c.setCounterPeriodDisplay(counterRule.getPeriod().getDisplayName());
                        c.setCounterTypeDisplay(Objects.nonNull(counterRule.getType()) ? counterRule.getType().getDisplayName() : null);
                        c.setCounterAttribute(counterRule.getCounterAttribute());
                        c.setCounterLevelType(counterRule.getLevelType());
                        c.setCounterLevel(Objects.nonNull(counterLevelObject)
                                ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null);
                        c.setCounterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                                ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                                programTransactionAttribute.getName()) : null);
                        c.setCounterRuleType(counterRule.getCounterRuleType());
                    }
                    c.setOperator(item.getOperator());
                    c.setValue(item.getValue());
                    c.setIsIgnoreRefund(item.getIsIgnoreRefund());
                    return c;
                }).collect(Collectors.toList());
        ruleCounter.setRuleConditionCounters(conditions);

        if (CollectionUtils.isNotEmpty(schemeCounterConditions)) {
            ruleCounter.setConditionLogic(schemeCounterConditions.get(0).getConditionLogic());
        }
        result.setRuleCounters(ruleCounter);

        List<EscrowedPoint> escrowPoints = escrowedPointService.findBySchemeIdAndStatus(scheme.getId(), ECommonStatus.ACTIVE);

        if (!CollectionUtils.isEmpty(escrowPoints)) {
            EscrowedPoint escrowedPoint = escrowPoints.get(0);
            List<RuleRes> ruleResList = opsRuleService.getRule(escrowedPoint.getCode(), scheme.getProgramId(), EServiceType.ESCROW_POINT);

            EscrowedPointRes escrowedPointRes = EscrowedPointRes
                    .builder()
                    .type(escrowedPoint.getType())
                    .ruleLogic(escrowedPoint.getRuleLogic())
                    .rules(ruleResList)
                    .value(escrowedPoint.getValue())
                    .build();

            result.setEscrowedPointRes(escrowedPointRes);
        }

        return result;
    }

    private String getExpirePolicyValue(EPayWithPointExpiryPolicy expirePolicyType, Integer periodDay, Date expiredTimeFixed) {
        if (EPayWithPointExpiryPolicy.PERIOD_DAY.equals(expirePolicyType)) {
            return periodDay.toString();
        } else if (EPayWithPointExpiryPolicy.FIX_TIME.equals(expirePolicyType)) {
            return DateTimes.toEpochSecond(expiredTimeFixed).toString();
        } else if (EPayWithPointExpiryPolicy.NON_EXPIRY.equals(expirePolicyType)) {
            return null;
        }
        return null;
    }

    @Override
    public Page<SchemeBaseRes> getPage(Integer programId, Integer campaignId,
                                       ESchemeType type, String code, String name, ECommonStatus status, EPoolType poolType,
                                       Date startDate, Date endDate,
                                       Pageable pageable
    ) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Program program = programService.find(programId, business.getId());
        if (ECommonStatus.INACTIVE.equals(program.getStatus())) {
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, "Program not active", null);
        }

        Page<Scheme> page = schemeService.filter(programId, campaignId, type, code, name, status, poolType, startDate, endDate, pageable);

        List<SchemeBaseRes> content = this.convertToBaseRes(business, program, page.getContent());
        return new PageImpl<>(content, page.getPageable(), page.getTotalElements());
    }

    private List<SchemeBaseRes> convertToBaseRes(Business business, Program program, List<Scheme> ess) {
        return ess.stream()
                .map(es -> {
                    RewardPool rewardPool = null;
                    Pool pool = null;
                    if (Objects.nonNull(es.getRewardPoolId())) {
                        rewardPool = rewardPoolService.findById(es.getRewardPoolId())
                                .orElseThrow(() -> new BusinessException(ErrorCode.POOL_NOT_FOUND));
                    } else {
                        pool = poolService.find(es.getPoolId())
                                .orElseThrow(() -> new BusinessException(ErrorCode.POOL_NOT_FOUND));
                    }

                    Campaign campaign = null;
                    if (Objects.nonNull(es.getCampaignId())) {
                        campaign = campaignService.find(es.getCampaignId()).orElse(null);
                    }

                    SchemeBaseRes schemeBaseRes = SchemeBaseRes.builder()
                            .schemeId(es.getId())
                            .schemeCode(es.getCode())
                            .schemeName(es.getName())
                            .description(es.getDescription())
                            .schemeType(es.getSchemeType())
                            .ruleLogic(es.getRuleLogic())
                            .status(es.getStatus())
                            .programId(program.getId())
                            .programName(program.getName())
                            .programCode(program.getCode())
                            .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                            .campaign(nullSafer(campaign, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                            .businessId(business.getId())
                            .businessName(business.getName())
                            .startDate(es.getStartDate() != null ? DateTimes.toEpochSecond(es.getStartDate()) : null)
                            .endDate(es.getEndDate() != null ? DateTimes.toEpochSecond(es.getEndDate()) : null)
                            .build();

                    if (Objects.nonNull(rewardPool)) {
                        schemeBaseRes.setPoolId(rewardPool.getId());
                        schemeBaseRes.setPoolName(rewardPool.getName());
                        schemeBaseRes.setPoolCode(rewardPool.getPoolCode());
                        schemeBaseRes.setPool(new ShortEntityRes(rewardPool.getId(), rewardPool.getName(), rewardPool.getPoolCode()));
                        schemeBaseRes.setPoolType(rewardPool.getPoolType());
                    } else if (Objects.nonNull(pool)) {
                        schemeBaseRes.setPoolName(pool.getName());
                        schemeBaseRes.setPoolCode(pool.getCode());
                        schemeBaseRes.setPool(new ShortEntityRes(null, pool.getName(), pool.getCode()));
                        schemeBaseRes.setPoolType(EPoolType.POINT);
                    }

                    return schemeBaseRes;
                })
                .collect(Collectors.toList());
    }

    @Override
    public SchemeDetailRes getDetails(Integer schemeId) {
        Scheme scheme = schemeService.find(schemeId).orElseThrow(
                () -> new BusinessException(ErrorCode.SCHEME_NOT_FOUND, "Scheme not found", schemeId)
        );

        return setInfoScheme(scheme, null);
    }

    @Override
    public SchemeDetailRes getChangeable(Integer schemeId) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Scheme scheme = schemeService.find(schemeId).orElseThrow(
                () -> new BusinessException(ErrorCode.SCHEME_NOT_FOUND, "Scheme not found", schemeId)
        );
        programService.findByIdAndBusinessId(scheme.getProgramId(), business.getId());
        String editKey = opsReqPendingValidator.generateEditKey(scheme.getRequestCode(), scheme.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.SCHEME.getType(), scheme.getRequestCode());

        return setInfoScheme(scheme, editKey);
    }

    private void validationSchemeCodeDoesNotExistInOtherReqPending(Integer businessId, Integer programId, String schemeCode) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .status(EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue())
                .businessId(businessId)
                .build();
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(EMakerCheckerType.SCHEME, previewReq, null, null, null);

        Optional<CreateSchemeReq> any = Optional.ofNullable(previewRes.getData()).orElseGet(ArrayList::new).stream()
                .map(data -> this.jsonMapper.convertValue(data.getPayload(), CreateSchemeReq.class))
                .filter(ele -> Objects.nonNull(ele.getSchemeCode()))
                .filter(ele -> Objects.nonNull(ele.getProgramId()))
                .filter(ele -> ele.getProgramId().equals(programId) && ele.getSchemeCode().equals(schemeCode))
                .findAny();
        if (any.isPresent()) {
            throw new BusinessException(ErrorCode.SCHEME_CODE_EXISTED,
                    "[VALIDATION SCHEME CODE] scheme code existed in other requests pending", schemeCode);
        }
    }

    private void verifyProgramAndPool(Integer programId, Integer poolId, Integer pointPoolId) {
        programService.findActive(programId); // verify program

        if (Objects.nonNull(poolId)) {
            RewardPool rewardPool = rewardPoolService.findActive(poolId);
            if (!rewardPool.getProgramId().equals(programId)) {
                throw new BusinessException(ErrorCode.POOL_NOT_IN_PROGRAM, "Pool not in program", poolId);
            }
        } else if (Objects.nonNull(pointPoolId)) {
            Pool pool = poolService.findActive(pointPoolId);
            if (!pool.getProgramId().equals(programId)) {
                throw new BusinessException(ErrorCode.POOL_NOT_IN_PROGRAM, "Pool not in program", poolId);
            }
        }
    }

    private void verifySchemeCode(Integer programId, String schemeCode, String exceptCode) {
        Scheme scheme = schemeService.find(programId, schemeCode).orElse(null);
        if (scheme != null && !scheme.getCode().equals(exceptCode)) {
            throw new BusinessException(ErrorCode.SCHEME_CODE_EXISTED, "Scheme code existed", schemeCode);
        }
    }

    private void verifyStartAndEndDate(Long startDate, Long endDate) {
        if (startDate > endDate) {
            throw new BusinessException(ErrorCode.START_DATE_IS_LESS_END_DATE, "Start date is less than end date", null);
        }
    }

    private void verifySchemeInfoExceptCode(VerifySchemeInfoReq req, String exceptCode) {
        verifyStartAndEndDate(req.getStartDate(), req.getEndDate());
        verifySchemeCode(req.getProgramId(), req.getSchemeCode(), exceptCode);
        verifyProgramAndPool(req.getProgramId(), req.getPoolId(), req.getPointPoolId());
        verifyProgramAndCounter(req.getProgramId(), req.getCounterIds());
        if (Objects.nonNull(req.getCampaignId())) {
            verifyCampaign(req);
        }
    }

    private void verifySchemeCombineExceptCode(CreateSchemeReq req, String exceptCode) {
        /* verify info */
        verifySchemeInfoExceptCode(req, exceptCode);
        Collection<ConditionAttributeDto> attributes = opsConditionService.conditionAttributeDtos(req.getProgramId());

        /* verify rules and condition*/
        if (req.getRuleList() != null) {
            VerifyCreateListSchemeRuleReq rules = new VerifyCreateListSchemeRuleReq();
            rules.setRuleLogic(req.getRuleLogic());
            rules.setRuleList(req.getRuleList());
            opsRuleService.verifyListRule(SchemeMapper.toSchemeCreate(req), rules, attributes);
        }
        opsFormulaService.verify(req.getProgramId(), req.getFormulaGroup(), attributes);
    }

    @Override
    public Page<SchemeEntryRes> getInReviews(
            String schemeCode,
            String schemeName,
            String schemeType,
            String poolType,
            Long startDate,
            Long endDate,
            String status,
            Integer offset,
            Integer limit,
            MakerCheckerInternalPreviewReq req
    ) {
        Map<String, Object> searchPayload = (new MongoSearchUtil())
                .setFilter("payload.scheme_code", schemeCode, false, false, false)
                .setFilter("payload.scheme_name", schemeName, false, true, true, MongoSearchUtil.OPTION_IGNORE_CASE_INSENSITIVITY)
                .setFilter("payload.scheme_en_name", schemeName, false, true, true, MongoSearchUtil.OPTION_IGNORE_CASE_INSENSITIVITY)
                .setFilter("payload.scheme_type", schemeType, false, false, false)
                .setFilter("payload.pool_type", poolType, false, false, false)
                .setFilter("payload.status", status, false, false, false)
                .setFilter("payload.start_date", Objects.nonNull(startDate) ? MakerCheckerInternalPreviewReq.RangeDateReq.builder().fromDate(startDate).build() : null, false, false, false)
                .setFilter("payload.end_date", Objects.nonNull(endDate) ? MakerCheckerInternalPreviewReq.RangeDateReq.builder().toDate(endDate).build() : null, false, false, false)
                .build();

        req.setProperties(searchPayload);
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(EMakerCheckerType.SCHEME, req, offset, limit, null);
        List<SchemeEntryRes> res = previewRes.getData().stream()
                .map(this::convertPreview)
                .collect(Collectors.toList());
        return new PageImpl<>(res, new OffsetBasedPageRequest(offset, limit), previewRes.getMeta().getTotal());
    }

    private SchemeEntryRes convertPreview(MakerCheckerInternalDataDetailRes data) {
        CreateSchemeReq createReq = this.jsonMapper.convertValue(data.getPayload(), CreateSchemeReq.class);
        Program program = programService.find(createReq.getProgramId()).orElse(null);
        RewardPool rewardPool = null;
        Pool pool = null;
        Currency currency = null;

        if (EPoolType.VOUCHER.equals(createReq.getPoolType())) {
            rewardPool = rewardPoolService.findById(createReq.getPoolId()).orElse(null);
        } else if (EPoolType.POINT.equals(createReq.getPoolType())) {
            pool = poolService.findByRefId(createReq.getPoolId()).orElse(null);
            assert pool != null;
            currency = currencyService.find(pool.getCurrencyId()).orElse(null);
        } else {
            pool = poolService.find(createReq.getPoolId()).orElse(null);
            assert pool != null;
            currency = currencyService.find(pool.getCurrencyId()).orElse(null);
        }

        Campaign campaign = null;
        if (Objects.nonNull(createReq.getCampaignId())) {
            campaign = campaignService.find(createReq.getCampaignId()).orElse(null);
        }

        return SchemeEntryRes
                .builder()
                .requestId(data.getId())
                .schemeCode(createReq.getSchemeCode())
                .schemeName(createReq.getSchemeName())
                .schemeEnName(createReq.getSchemeEnName())
                .schemeType(createReq.getSchemeType())
                .startDate(createReq.getStartDate())
                .endDate(createReq.getEndDate())
                .program(program != null ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .poolShort(Objects.nonNull(rewardPool) ? PoolShortRes.of(rewardPool, currency) : PoolShortRes.of(pool, currency))
                .campaign(campaign != null ? new ShortEntityRes(campaign.getId(), campaign.getName(), campaign.getCode()) : null)
                .status(ECommonStatus.of(createReq.getStatus()))
                .createdBy(data.getMadeByUserName())
                .createdAt(data.getMadeDateToDate())
                .approvedBy(data.getCheckedByUserName())
                .approvedAt(data.getCheckedDateToDate())
                .requestType(createReq.getRequestType())
                .madeReason(createReq.getMadeReason())
                .reason(data.getComment())
                .approvalStatus(EMakerCheckerStatus.lookup(data.getStatus()).getMapping())
                .poolType(Objects.nonNull(createReq.getPoolType()) ? createReq.getPoolType() : EPoolType.POINT)
                .build();
    }

    private <I, V> V nullSafer(I value, org.springframework.cglib.core.internal.Function<I, V> executorFunction) {
        return value != null ? executorFunction.apply(value) : null;
    }

    @Override
    public SchemeDetailInReviewRes getInReviewDetail(String reviewId) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(EMakerCheckerType.SCHEME.getType(), reviewId);
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Scheme - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }

        return convert(previewDetailRes.getData());
    }

    private SchemeDetailInReviewRes convert(MakerCheckerInternalDataDetailRes data) {
        CreateSchemeReq createReq = this.jsonMapper.convertValue(data.getPayload(), CreateSchemeReq.class);
        Program program = programService.find(createReq.getProgramId()).orElse(null);
        businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());

        Campaign campaign = null;
        Currency currency = null;
        if (Objects.nonNull(createReq.getCampaignId())) {
            campaign = campaignService.find(createReq.getCampaignId()).orElse(null);
        }
        RewardPool rewardPool = rewardPoolService.findById(createReq.getPoolId()).orElse(null);
        if (createReq.getPoolType().equals(EPoolType.POINT)) {
            assert rewardPool != null;
            Pool pool = poolService.findByRefId(rewardPool.getId()).orElse(null);
            assert pool != null;
            currency = currencyService.find(pool.getCurrencyId()).orElse(null);
        }

        Counter counterFormula = null;
        if (Objects.nonNull(createReq.getFormulaGroup().getCounterId())) {
            counterFormula = counterService.findById(createReq.getFormulaGroup().getCounterId()).orElse(null);
        }
        ShortEntityRes counterFormulaInfo = new ShortEntityRes();

        if (Objects.nonNull(counterFormula)) {
            counterFormulaInfo.setId(counterFormula.getId());
            counterFormulaInfo.setCode(counterFormula.getCode());
            counterFormulaInfo.setName(counterFormula.getName());
        }

        if (Objects.nonNull(createReq.getRuleCounters())) {
            createReq.getRuleCounters().getRuleConditionCounters()
                    .stream()
                    .map(e -> {
                        Counter counterRule = null;
                        if (Objects.nonNull(e.getCounterId())) {
                            counterRule = counterService.findById(e.getCounterId()).orElse(null);
                        }
                        ShortEntityRes counterRuleInfo = new ShortEntityRes();
                        if (Objects.nonNull(counterRule)) {
                            CounterLevelObject counterLevelObject = null;
                            ProgramTransactionAttribute programTransactionAttribute = null;
                            if (Objects.nonNull(program)) {
                                if (Objects.nonNull(counterRule.getLevel())) {
                                    counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(program.getId(), counterRule.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
                                }
                                programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(program.getId(), counterRule.getCounterLevelAttribute()).orElse(null);
                            }


                            counterRuleInfo.setId(counterRule.getId());
                            counterRuleInfo.setCode(counterRule.getCode());
                            counterRuleInfo.setName(counterRule.getName());

                            e.setCounter(counterRuleInfo);
                            e.setCounterPeriod(counterRule.getPeriod());
                            e.setCounterPeriodDisplay(counterRule.getPeriod().getDisplayName());
                            e.setCounterTypeDisplay(Objects.nonNull(counterRule.getType()) ? counterRule.getType().getDisplayName() : null);
                            e.setCounterAttribute(counterRule.getCounterAttribute());
                            e.setCounterLevelType(counterRule.getLevelType());
                            e.setCounterLevel(Objects.nonNull(counterLevelObject)
                                    ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null);
                            e.setCounterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                                    ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                                    programTransactionAttribute.getName()) : null);
                            e.setCounterRuleType(Objects.nonNull(counterRule.getCounterRuleType()) ? counterRule.getCounterRuleType() : ECounterRuleType.RULE_CONDITION);
                        }

                        return e;
                    })
                    .collect(Collectors.toList());
        }
        List<SchemeLimitRes> schemeLimitResList = null;
        if (Objects.nonNull(createReq.getRuleLimits())) {
            schemeLimitResList = createReq.getRuleLimits().stream().map(
                    e -> {
                        Counter counter = counterService.findById(e.getCounterId()).orElse(null);
                        if (Objects.nonNull(counter)) {
                            return SchemeLimitRes.builder()
                                    .id(e.getId())
                                    .code(counter.getCode())
                                    .name(counter.getName())
                                    .allowAwardingRemainingValue(e.getAllowAwardingRemainingValue())
                                    .value(e.getValue())
                                    .counterRuleType(counter.getCounterRuleType())
                                    .build();

                        }
                        return SchemeLimitRes.builder()
                                .id(e.getId())
                                .allowAwardingRemainingValue(e.getAllowAwardingRemainingValue())
                                .value(e.getValue())
                                .build();
                    }
            ).collect(Collectors.toList());
        }

        SchemeDetailInReviewRes result = new SchemeDetailInReviewRes();

        result.setRequestId(data.getId());
        result.setCreatedBy(data.getMadeByUserName());
        result.setCreatedAt(data.getMadeDateToDate());
        result.setApprovedBy(data.getCheckedByUserName());
        result.setApprovedAt(data.getCheckedDateToDate());
        result.setRequestType(createReq.getRequestType());
        result.setApprovalStatus(EMakerCheckerStatus.lookup(data.getStatus()).getMapping());
        result.setMadeReason(createReq.getMadeReason());
        result.setReason(data.getComment());
        result.setStatus(ECommonStatus.of(createReq.getStatus()));
        result.setProgram(nullSafer(program, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())));
        result.setCampaign(nullSafer(campaign, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())));
        result.setId(createReq.getSchemeId());
        result.setSchemeCode(createReq.getSchemeCode());
        result.setSchemeName(createReq.getSchemeName());
        result.setSchemeEnName(createReq.getSchemeEnName());
        result.setEndDate(DateTimes.toDate(createReq.getEndDate()));
        result.setStartDate(DateTimes.toDate(createReq.getStartDate()));
        result.setDescription(createReq.getDescription());
        result.setEnDescription(createReq.getEnDescription());
        assert rewardPool != null;
        result.setPoolShort(PoolShortRes.of(rewardPool, currency));
        result.setSchemeType(createReq.getSchemeType());
        result.setRuleLogic(createReq.getRuleLogic());
        result.setRoundingRule(createReq.getRoundingRule());
        result.setMaxAmount(createReq.getMaxAmount());
        result.setMinAmount(createReq.getMinAmount());
        result.setRuleList(createReq.getRuleList().stream().map(RuleRecordRes::convert).collect(Collectors.toList()));
        result.setFormulaGroup(FormulaGroupRes.convert(createReq.getFormulaGroup(), counterFormulaInfo));
        result.setRuleCounters(createReq.getRuleCounters());
        result.setExpirePolicyType(createReq.getExpirePolicyType());
        result.setExpirePolicyValue(createReq.getExpirePolicyValue());
        result.setProposalCode(createReq.getProposalCode());
        result.setPushEvent(createReq.getPushEvent());
        result.setEscrowedPoint(createReq.getEscrowedPoint());
        result.setSchemeLimitRes(schemeLimitResList);

        return result;
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        MakerCheckerInternalDataDetailRes detailResData = makerCheckerInternalFeignClient.previewChecker(req.getId(), EMakerCheckerType.SCHEME);

        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            if (detailResData != null && detailResData.getPayload() != null) {
                processApprove(detailResData, req);
            }
        } else {
            makerCheckerInternalFeignClient.checkerDefault(EMakerCheckerType.SCHEME, req, null);
        }
    }

    private void processApprove(MakerCheckerInternalDataDetailRes detailResData, ApprovalReq req) {
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        String approvedBy = opsReqPendingValidator.getCurrentUser();
        Object requestType = ((LinkedHashMap<?, ?>) detailResData.getPayload()).get("request_type");
        Scheme scheme;
        String createdBy = null;
        String updatedBy;
        ResetSchemeRuleReq resetSchemeRuleReq = null;
        if (ERequestType.CREATE.getValue().equals(requestType)) {
            CreateSchemeReq payload = this.jsonMapper.convertValue(detailResData.getPayload(), CreateSchemeReq.class);
            Collection<ConditionAttributeDto> attributes = opsConditionService.conditionAttributeDtos(payload.getProgramId());
            verifyCounterRuleInCounterIds(payload.getCounterIds(), payload.getRuleLimits());
            validateCreateScheme(payload);
            verifySchemeRequest(payload);

            // Check reset rule
            scheme = SchemeMapper.toSchemeCreate(payload);
            updatedBy = createdBy = detailResData.getMadeByUserName();
            opsReqPendingValidator.updateInfoChecker(scheme, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
            scheme.setStatus(ECommonStatus.of(payload.getStatus()));

            scheme.setRequestCode(detailResData.getRequestCode());
            scheme.setVersion(detailResData.getVersion());

            Scheme schemeEntity = schemeService.save(scheme);

            List<SchemeTransactionCode> schemeTransactionCodes = payload.getTransactionCodeIds().stream()
                    .map(ele -> convert(schemeEntity.getId(), ele))
                    .collect(Collectors.toList());
            List<SchemeTransactionCode> transactionCodes = schemeTransactionCodeService.saveAll(schemeTransactionCodes);

            List<SchemeLinkCounter> schemeLinkCounterList = payload.getCounterIds().stream()
                    .map(c -> convertSchemeLinkCounter(schemeEntity.getId(), c))
                    .collect(Collectors.toList());
            schemeLinkCounterService.saveAll(schemeLinkCounterList);

            resetSchemeRuleReq = opsRuleSchemeService.createRules(schemeEntity, payload.getRuleList(), attributes);
            resetSchemeRuleReq.setSchemeTransactionCodes(transactionCodes.stream().map(ResetSchemeRuleReq::buildTransactionCodeReq).collect(Collectors.toList()));
            if (Objects.nonNull(payload.getRuleCounters())) {
                opsRuleSchemeService.createRulesCounter(scheme, payload.getRuleCounters());
            }
            opsFormulaService.create(scheme, payload.getFormulaGroup(), attributes);

            // Escrow Point
            if (Objects.nonNull(payload.getEscrowedPoint())) {
                resetRuleReqs = createEscrowPoint(schemeEntity, payload, detailResData, createdBy, updatedBy, approvedBy);
            }

            Integer schemeId = scheme.getId();
            // Tab limit
            if (Objects.nonNull(payload.getRuleLimits())) {
                List<SchemeLimit> schemeLimits = payload.getRuleLimits().stream()
                        .map(c -> convertSchemeLimit (c, schemeId))
                        .collect(Collectors.toList());

                schemeLimitService.saveAll(schemeLimits);
            }
        }

        if (ERequestType.EDIT.getValue().equals(requestType)) {
            UpdateSchemeReq payload = this.jsonMapper.convertValue(detailResData.getPayload(), UpdateSchemeReq.class);
            Collection<ConditionAttributeDto> attributes = opsConditionService.conditionAttributeDtos(payload.getProgramId());

            scheme = schemeService.find(payload.getSchemeId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.SCHEME_NOT_FOUND, "Scheme not found", payload.getSchemeId())
            );
            verifyCounterRuleInCounterIds(payload.getCounterIds(), payload.getRuleLimits());
            validateUpdateScheme(scheme);
            verifyProgramAndCounter(payload.getProgramId(), payload.getCounterIds());

            SchemeMapper.setValuesFromReqUpdate(scheme, payload);
            updatedBy = detailResData.getMadeByUserName();
            opsReqPendingValidator.updateInfoChecker(scheme, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
            scheme.setRequestCode(detailResData.getRequestCode());
            scheme.setVersion(detailResData.getVersion());

            Scheme schemeEntity = schemeService.update(scheme);

            List<SchemeTransactionCode> schemeTransactionCodes = updateSchemeTxnCodes(payload, schemeEntity);
            updateSchemeLinkCounter(payload.getCounterIds(), schemeEntity.getId());

            resetSchemeRuleReq = opsRuleSchemeService.updateRules(schemeEntity, payload.getRuleList(), attributes);
            resetSchemeRuleReq.setSchemeTransactionCodes(schemeTransactionCodes.stream().map(ResetSchemeRuleReq::buildTransactionCodeReq).collect(Collectors.toList()));
            opsRuleSchemeService.updateRulesCounter(scheme, payload.getRuleCounters());
            opsFormulaService.updateFormula(scheme, payload.getFormulaGroup(), attributes);

            // Escrow Point
            List<EscrowedPoint> escrowPoints = escrowedPointService.findBySchemeIdAndStatus(schemeEntity.getId(), ECommonStatus.ACTIVE);
            if (CollectionUtils.isEmpty(escrowPoints)) {
                if (payload.getEscrowedPoint() != null) {
                    resetRuleReqs = createEscrowPoint(schemeEntity, payload, detailResData, createdBy, updatedBy, approvedBy);
                }
            } else {
                EscrowedPoint escrowedPoint = escrowPoints.get(0);

                if (payload.getEscrowedPoint() != null) {
                    resetRuleReqs = updateEscrowPoint(schemeEntity.getProgramId(), escrowedPoint, payload, detailResData, createdBy, updatedBy, approvedBy);
                } else {
                    inactiveEscrowPoint(schemeEntity.getProgramId(), escrowedPoint, detailResData, approvedBy);
                }
            }

            // Tab limit
            updateSchemeLimit(payload.getRuleLimits(), schemeEntity.getId());
        }

        // Check reset rule
        if (CollectionUtils.isNotEmpty(resetRuleReqs)) {
            APIFeignInternalResponse<?> ruleRes = oneloyaltyRuleFeignClient.checkReset(resetRuleReqs);
            if (ruleRes.getMeta().getCode() != 200) {
                throw new BusinessException(ErrorCode.RESET_RULE_FAILED);
            }
        }

        // Check reset scheme rule
        if (Objects.nonNull(resetSchemeRuleReq)) {
            APIFeignInternalResponse<?> ruleRes = oneloyaltySchemeFeignClient.checkReset(resetSchemeRuleReq);
            if (ruleRes.getMeta().getCode() != 200) {
                throw new BusinessException(ErrorCode.RESET_SCHEME_RULE_FAILED);
            }
        }

        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> plAprroved = makerCheckerInternalFeignClient.checkerDefault(EMakerCheckerType.SCHEME, req, null);

        if (plAprroved.getMeta().getCode() != HttpStatus.OK.value()) {
            throw new BusinessException(
                    ErrorCode.MAKER_CHECKER_CANNOT_CHANGE_STATUS,
                    "Checker fail",
                    LogData.createLogData()
                            .append("id", req.getId()));

        }
        if (EApprovalStatus.APPROVED == req.getStatus()) {
            if (CollectionUtils.isNotEmpty(resetRuleReqs)) {
                ResetRuleEvent<?> ruleEvent = ResetRuleEvent.builder()
                        .id(UUID.randomUUID().toString())
                        .eventType(ResetRuleEvent.RESET_RULE_EVENT_TYPE)
                        .timeStamp(System.currentTimeMillis())
                        .payload(resetRuleReqs)
                        .build();
                applicationEventPublisher.publishEvent(ruleEvent);
            }
            if (Objects.nonNull(resetSchemeRuleReq)) {
                ResetSchemeRuleEvent<?> ruleEvent = ResetSchemeRuleEvent.builder()
                        .id(UUID.randomUUID().toString())
                        .eventType(ResetSchemeRuleEvent.RESET_RULE_EVENT_TYPE)
                        .timeStamp(System.currentTimeMillis())
                        .payload(resetSchemeRuleReq)
                        .build();
                applicationEventPublisher.publishEvent(ruleEvent);
            }
        }
    }

    private List<SchemeTransactionCode> updateSchemeTxnCodes(UpdateSchemeReq payload, Scheme schemeEntity) {
        Set<Integer> activeIds = new HashSet<>();
        Predicate<SchemeTransactionCode> predicate = ele -> {
            if (ECommonStatus.ACTIVE.equals(ele.getStatus())) {
                activeIds.add(ele.getTransactionCodeId());
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        };

        Map<Integer, SchemeTransactionCode> mapSchemeTxnCodeInactive = schemeTransactionCodeService
                .findAllBySchemeId(schemeEntity.getId())
                .stream()
                .filter(predicate)
                .collect(Collectors.toMap(SchemeTransactionCode::getTransactionCodeId,
                        Function.identity(), (k1, k2) -> k2));

        // Handle create a scheme transaction code
        List<SchemeTransactionCode> schemeTxnCodeEntities = new ArrayList<>();

        // Handle edit a scheme transaction code
        // Exp: | create: 1,2,3 | edit: 2,3,4 | we must create a transaction code with id is
        // 4 and remove a transaction code 1
        ArrayList<Integer> createIds = new ArrayList<>(
                CollectionUtils.subtract(payload.getTransactionCodeIds(), activeIds)); // 4
        ArrayList<Integer> deleteIds = new ArrayList<>(
                CollectionUtils.subtract(activeIds, payload.getTransactionCodeIds())); // 1
        ArrayList<Integer> holdIds = new ArrayList<>(payload.getTransactionCodeIds()); // 2, 3
        holdIds.retainAll(activeIds);
        if (!createIds.isEmpty()) {
            for (Integer txnCodeId : createIds) {
                SchemeTransactionCode schemeTransactionCode;
                // Set the active status for obsolete record
                if (mapSchemeTxnCodeInactive.containsKey(txnCodeId)) {
                    schemeTransactionCode = mapSchemeTxnCodeInactive.get(txnCodeId);
                    schemeTransactionCode.setStatus(ECommonStatus.ACTIVE);
                    schemeTransactionCode.setApprovedAt(new Date());
                    schemeTransactionCode.setApprovedBy(opsReqPendingValidator.getCurrentUser());
                } else {
                    // New record
                    schemeTransactionCode = convert(schemeEntity.getId(), txnCodeId);
                }
                schemeTxnCodeEntities.add(schemeTransactionCode);
            }
        }
        if (!deleteIds.isEmpty()) {
            for (Integer deleteId : deleteIds) {
                List<SchemeTransactionCode> schemeTransactionCodes = schemeTransactionCodeService
                        .findActive(schemeEntity.getId(), deleteId);
                for (SchemeTransactionCode ele : schemeTransactionCodes) {
                    ele.setStatus(ECommonStatus.INACTIVE);
                    ele.setApprovedAt(new Date());
                    ele.setApprovedBy(opsReqPendingValidator.getCurrentUser());
                    schemeTxnCodeEntities.add(ele);
                }
            }
        }
        if (!holdIds.isEmpty()) {
            for (Integer holdId : holdIds) {
                List<SchemeTransactionCode> schemeTransactionCodes = schemeTransactionCodeService
                        .findActive(schemeEntity.getId(), holdId);
                for (SchemeTransactionCode ele : schemeTransactionCodes) {
                    ele.setApprovedAt(new Date());
                    ele.setApprovedBy(opsReqPendingValidator.getCurrentUser());
                    schemeTxnCodeEntities.add(ele);
                }
            }
        }
        return schemeTransactionCodeService.saveAll(schemeTxnCodeEntities);
    }

    private void updateSchemeLinkCounter(List<Integer> counterIds, Integer schemeId) {
        Set<Integer> activeIds = new HashSet<>();
        Predicate<SchemeLinkCounter> predicate = ele -> {
            if (ECommonStatus.ACTIVE.equals(ele.getStatus())) {
                activeIds.add(ele.getCounterId());
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        };

        Map<Integer, SchemeLinkCounter> mapSchemeCounterInactive = schemeLinkCounterService
                .findAllBySchemeId(schemeId)
                .stream()
                .filter(predicate)
                .collect(Collectors.toMap(SchemeLinkCounter::getCounterId,
                        Function.identity(), (k1, k2) -> k2));

        // Handle create a scheme link counter
        List<SchemeLinkCounter> schemeCounterEntities = new ArrayList<>();

        // Handle edit a scheme link counter
        // Exp: | create: 1,2,3 | edit: 2,3,4 | we must create a counter with id is
        // 4 and remove a counter 1
        ArrayList<Integer> createIds = new ArrayList<>(
                CollectionUtils.subtract(counterIds, activeIds)); // 4
        ArrayList<Integer> deleteIds = new ArrayList<>(
                CollectionUtils.subtract(activeIds, counterIds)); // 1
        if (!createIds.isEmpty()) {
            for (Integer counterId : createIds) {
                SchemeLinkCounter schemeLinkCounter;
                // Set the active status for obsolete record
                if (mapSchemeCounterInactive.containsKey(counterId)) {
                    schemeLinkCounter = mapSchemeCounterInactive.get(counterId);
                    schemeLinkCounter.setStatus(ECommonStatus.ACTIVE);
                    schemeLinkCounter.setApprovedAt(new Date());
                    schemeLinkCounter.setApprovedBy(opsReqPendingValidator.getCurrentUser());
                } else {
                    // New record
                    schemeLinkCounter = convertSchemeLinkCounter(schemeId, counterId);
                }
                schemeCounterEntities.add(schemeLinkCounter);
            }
        }
        if (!deleteIds.isEmpty()) {
            for (Integer deleteId : deleteIds) {
                List<SchemeLinkCounter> schemeLinkCounters = schemeLinkCounterService
                        .findActive(schemeId, deleteId);
                for (SchemeLinkCounter schemeLinkCounter : schemeLinkCounters) {
                    schemeLinkCounter.setStatus(ECommonStatus.INACTIVE);
                    schemeLinkCounter.setApprovedAt(new Date());
                    schemeLinkCounter.setApprovedBy(opsReqPendingValidator.getCurrentUser());
                    schemeCounterEntities.add(schemeLinkCounter);
                }
            }
        }
        schemeLinkCounterService.saveAll(schemeCounterEntities);
    }

    private SchemeTransactionCode convert(Integer schemeId, Integer ele) {
        SchemeTransactionCode schemeTransactionCode = new SchemeTransactionCode();
        schemeTransactionCode.setSchemeId(schemeId);
        schemeTransactionCode.setTransactionCodeId(ele);
        schemeTransactionCode.setStatus(ECommonStatus.ACTIVE);
        schemeTransactionCode.setApprovedAt(new Date());
        schemeTransactionCode.setApprovedBy(opsReqPendingValidator.getCurrentUser());
        return schemeTransactionCode;
    }

    private void validateCreateScheme(CreateSchemeReq req) {
        Scheme checker = schemeService.find(req.getProgramId(), req.getSchemeCode()).orElse(null);
        if (checker != null) {
            throw new BusinessException(ErrorCode.SCHEME_CODE_EXISTED,
                    "[VALIDATION SCHEME CODE] scheme code existed", req.getSchemeCode());
        }

        programService.findActive(req.getProgramId());
        if (Objects.nonNull(req.getCampaignId())) {
            verifyCampaign(req);
        }

        RewardPool rewardPool = rewardPoolService.findById(req.getPoolId()).orElseThrow(() -> new BusinessException(ErrorCode.POOL_NOT_FOUND, "Pool not found", req.getPoolId()));

        verifyProgramAndCounter(req.getProgramId(), req.getCounterIds());

        boolean notAcceptedPool = !rewardPool.getProgramId().equals(req.getProgramId());

        notAcceptedPool &= Stream.of(programPoolRepository.findPoolByProgramId(req.getProgramId()))
                .noneMatch(objects -> {
                    RewardPool poolIter = (RewardPool) objects[0];
                    return poolIter.getId().equals(req.getPoolId()) && poolIter.getStatus() == ECommonStatus.ACTIVE;
                });

        if (notAcceptedPool) {
            throw new BusinessException(ErrorCode.POOL_NOT_IN_PROGRAM, "Pool not in program", req.getPoolId());
        }
    }

    private void validateUpdateScheme(Scheme scheme) {

//        if (ECommonStatus.INACTIVE.equals((scheme.getStatus()))) {
//            throw new BusinessException(ErrorCode.SCHEME_CANNOT_UPDATE, "Scheme cannot update", scheme.getId());
//        }

        if (Objects.nonNull(scheme.getRewardPoolId())) {
            rewardPoolService.findById(scheme.getRewardPoolId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.REWARD_POOL_NOT_FOUND, null, null));
        } else {
            poolService.find(scheme.getPoolId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.POOL_NOT_FOUND, null, null));
        }
    }

    @Override
    public void cancelInReview(CancelReq req) {
        MakerCheckerInternalDataDetailRes detailRes = makerCheckerInternalFeignClient
                .previewDetailDefault(EMakerCheckerType.SCHEME, String.valueOf(req.getId()));
        if (!EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue().equals(detailRes.getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to cancel",
                    LogData.createLogData().append("id", String.valueOf(req.getId())));
        }
        makerCheckerInternalFeignClient.cancelDefault(EMakerCheckerType.SCHEME, req);
    }

    @Override
    public List<CounterShortInformationRes> getAvailableCounters(Integer programId, ECommonStatus status, boolean filter, List<ECounterRuleType> counterRuleTypes) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        List<Counter> counters = counterRepository.findAvailableInScheme(business.getId(),
                programId, EServiceType.SCHEME, filter ? new Date() : null, status, counterRuleTypes);

        return counters.stream().map(
                counter -> {
                    CounterLevelObject counterLevelObject = null;
                    ProgramTransactionAttribute programTransactionAttribute;
                    if (Objects.nonNull(counter.getLevel())) {
                        counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(programId, counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
                    }
                    programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(programId, counter.getCounterLevelAttribute()).orElse(null);

                    return CounterShortInformationRes.builder()
                            .id(counter.getId())
                            .name(counter.getName())
                            .code(counter.getCode())
                            .description(counter.getDescription())
                            .counterType(counter.getType())
                            .counterTypeDisplay(counter.getType().getDisplayName())
                            .counterStatus(counter.getStatus())
                            .startDate(counter.getStartDate())
                            .endDate(counter.getEndDate())
                            .enableRevert(counter.getEnableRevert())
                            .period(counter.getPeriod())
                            .counterLevel(Objects.nonNull(counterLevelObject)
                                    ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                            .counterAttribute(counter.getCounterAttribute())
                            .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                                    ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                                    programTransactionAttribute.getName()) : null)
                            .counterLevelType(counter.getLevelType())
                            .counterRuleType(counter.getCounterRuleType())
                            .build();
                }
        ).collect(Collectors.toList());
    }

    private void verifyCampaign(VerifySchemeInfoReq req) {
        Campaign campaign = campaignService.findActiveByIdAndProgramId(req.getCampaignId(), req.getProgramId());
        if (campaign.getStartDate().after(DateTimes.toDate(req.getStartDate())) || campaign.getEndDate().before(DateTimes.toDate(req.getEndDate()))) {
            throw new BusinessException(ErrorCode.SCHEME_EFFECTIVE_DATE_MUST_BE_WITHIN_CAMPAIGN, null, null);
        }
    }

    private void verifySchemeRequest(CreateSchemeReq req) {
        RewardPool rewardPool = rewardPoolService.findActive(req.getPoolId());
        Set<EFormulaType> formulaTypeSet;

        if (ESchemeType.PWP.equals(req.getSchemeType())) {
            if (EPayWithPointExpiryPolicy.PERIOD_DAY == req.getExpirePolicyType()) {
                try {
                    int days = Integer.parseInt(req.getExpirePolicyValue());
                    if (days < 1 || days > 999) {
                        throw new OpsBusinessException(OpsErrorCode.PERIOD_DAY_IS_INVALID, "Period day is invalid", "Period day is invalid", null);
                    }
                } catch (Exception ex) {
                    throw new OpsBusinessException(OpsErrorCode.PERIOD_DAY_IS_INVALID, "Period day is invalid", "Period day is invalid", null);
                }
            }
            if (EPayWithPointExpiryPolicy.FIX_TIME == req.getExpirePolicyType()) {
                Long expireTime = Long.valueOf(req.getExpirePolicyValue());
                if (DateTimes.toDate(expireTime).before(new Date())) {
                    throw new OpsBusinessException(OpsErrorCode.FIX_TIME_IS_INVALID, "Fix time is invalid", null);
                }
            }
            formulaTypeSet = Stream.of(EFormulaType.F8, EFormulaType.F9)
                    .collect(Collectors.toSet());

            Pool pool = poolService.findByRefId(rewardPool.getId()).orElse(null);
            req.setPointPoolId(Objects.nonNull(pool) ? pool.getId() : null);
        } else if (EPoolType.VOUCHER.equals(rewardPool.getPoolType())) {
            formulaTypeSet = Stream.of(EFormulaType.F6, EFormulaType.F7)
                    .collect(Collectors.toSet());
        } else {
            // EPoolType.POINT
            if (Objects.isNull(req.getFormulaGroup().getAttribute())) {
                throw new BusinessException(ErrorCode.FORMULA_ATTRIBUTE_NOT_NULL);
            }
            if (Objects.isNull(req.getRoundingRule())) {
                throw new BusinessException(ErrorCode.ROUNDING_RULE_MUST_NOT_BE_NULL);
            }
            formulaTypeSet = Stream.of(EFormulaType.F1, EFormulaType.F2,
                            EFormulaType.F3, EFormulaType.F4, EFormulaType.F5)
                    .collect(Collectors.toSet());
            Pool pool = poolService.findByRefId(rewardPool.getId()).orElse(null);
            req.setPointPoolId(Objects.nonNull(pool) ? pool.getId() : null);
            if (Objects.nonNull(req.getEscrowedPoint())) {
                if (EEscrowedPointExpirePolicy.PERIOD_DAY == req.getEscrowedPoint().getType()
                        || EEscrowedPointExpirePolicy.PERIOD_MONTH == req.getEscrowedPoint().getType()
                        || EEscrowedPointExpirePolicy.PERIOD_YEAR == req.getEscrowedPoint().getType()) {
                    try {
                        int value = Integer.parseInt(req.getEscrowedPoint().getValue());
                        if (value < 1 || value > 360) {
                            throw new OpsBusinessException(OpsErrorCode.PERIOD_IS_INVALID, "Period is invalid", "Period is invalid", null);
                        }
                    } catch (Exception ex) {
                        throw new OpsBusinessException(OpsErrorCode.PERIOD_IS_INVALID, "Period is invalid", "Period is invalid", null);
                    }
                } else if (EEscrowedPointExpirePolicy.FIX_TIME == req.getEscrowedPoint().getType()) {
                    Long expireTime = Long.valueOf(req.getEscrowedPoint().getValue());
                    if (DateTimes.toDate(expireTime).before(new Date())) {
                        throw new OpsBusinessException(OpsErrorCode.FIX_TIME_IS_INVALID, "Fix time is invalid", null);
                    }
                } else if (EEscrowedPointExpirePolicy.PRODUCT_EXPIRED_DATE == req.getEscrowedPoint().getType()
                        || EEscrowedPointExpirePolicy.PRODUCT_EXPIRED_MONTH == req.getEscrowedPoint().getType()
                        || EEscrowedPointExpirePolicy.PRODUCT_EXPIRED_YEAR == req.getEscrowedPoint().getType()) {
                    try {
                        int value = Integer.parseInt(req.getEscrowedPoint().getValue());
                        if (value < 0 || value > 360) {
                            throw new OpsBusinessException(OpsErrorCode.PRODUCT_EXPIRED_TIME_IS_INVALID, "Product expired time is invalid", "Product expired time is invalid", null);
                        }
                    } catch (Exception ex) {
                        throw new OpsBusinessException(OpsErrorCode.PRODUCT_EXPIRED_TIME_IS_INVALID, "Product expired time is invalid", "Product expired time is invalid", null);
                    }
                }
                opsRuleService.validateRules(req.getProgramId(), EServiceType.ESCROW_POINT, req.getEscrowedPoint().getRules(), false);
            }
        }
        req.setPoolType(rewardPool.getPoolType());
        final EFormulaType[] type = {null};
        Optional.ofNullable(req.getFormulaGroup().getFormulaList())
                .orElse(Collections.emptyList())
                .forEach(i -> {
                    if (!formulaTypeSet.contains(i.getFormulaType())) {
                        throw new BusinessException(ErrorCode.FORMULA_TYPE_NOT_SUPPORT, "Formula type invalid", null);
                    }
                    if (EPoolType.VOUCHER.equals(rewardPool.getPoolType()) && Objects.nonNull(type[0]) && !type[0].equals(i.getFormulaType())) {
                        throw new BusinessException(ErrorCode.FORMULA_TYPE_INVALID, "Formula type invalid", null);
                    } else {
                        type[0] = i.getFormulaType();
                    }
                });
    }

    private SchemeLinkCounter convertSchemeLinkCounter(Integer schemeId, Integer counterId) {
        SchemeLinkCounter schemeLinkCounter = new SchemeLinkCounter();
        schemeLinkCounter.setSchemeId(schemeId);
        schemeLinkCounter.setCounterId(counterId);
        schemeLinkCounter.setStatus(ECommonStatus.ACTIVE);
        schemeLinkCounter.setApprovedAt(new Date());
        schemeLinkCounter.setApprovedBy(opsReqPendingValidator.getCurrentUser());
        return schemeLinkCounter;
    }

    private void verifyProgramAndCounter(Integer programId, List<Integer> counterIds) {
        counterIds.forEach(c -> counterService.findByIdAndProgramId(c, programId));
    }

    @Override
    public List<TransactionCodeRes> getTransactionCodeInReviewDetail(
            String code,
            String name,
            ETransactionCodeType type,
            ETransactionType transactionType,
            ECommonStatus status,
            String id
    ) {

        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> data =
                makerCheckerInternalFeignClient.previewDetail(EMakerCheckerType.SCHEME.getType(), id);

        CreateSchemeReq createReq = this.jsonMapper.convertValue(data.getData().getPayload(), CreateSchemeReq.class);
        List<Integer> txnCodeIds = createReq.getTransactionCodeIds();

        if (CollectionUtils.isEmpty(txnCodeIds)) {
            return Collections.EMPTY_LIST;
        }

        List<TransactionCode> list = transactionCodeRepository.filter(txnCodeIds, code, name, type, transactionType, status);

        return list
                .stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    private TransactionCodeRes convert(TransactionCode ele) {
        Program program = programService.findById(ele.getProgramId());
        ShortEntityRes programInfo = new ShortEntityRes(program.getId(), program.getName(), program.getCode());

        return TransactionCodeRes
                .builder()
                .id(ele.getId())
                .status(ele.getStatus())
                .code(ele.getCode())
                .name(ele.getName())
                .type(ele.getTransactionCodeType())
                .transactionType(ele.getTransactionType())
                .program(programInfo)
                .build();
    }

    @Override
    public List<TransactionCodeRes> getTransactionCodeAvailableDetail(
            String code,
            String name,
            ETransactionCodeType type,
            ETransactionType transactionType,
            ECommonStatus status,
            Integer id
    ) {
        List<Integer> txnCodeIds = schemeTransactionCodeRepository.findAllBySchemeId(id)
                .stream()
                .filter(item -> ECommonStatus.ACTIVE.equals(item.getStatus()))
                .map(SchemeTransactionCode::getTransactionCodeId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(txnCodeIds)) {
            return Collections.EMPTY_LIST;
        }

        List<TransactionCode> list = transactionCodeRepository.filter(txnCodeIds, code, name, type, transactionType, status);

        return list
                .stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<CounterRes> getCounterInReviewDetail(
            String code,
            String name,
            ECommonStatus status,
            String id
    ) {
        List<CounterRes> content = new ArrayList<>();

        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> data =
                makerCheckerInternalFeignClient.previewDetail(EMakerCheckerType.SCHEME.getType(), id);

        CreateSchemeReq createReq = this.jsonMapper.convertValue(data.getData().getPayload(), CreateSchemeReq.class);
        List<Integer> counterIds = createReq.getCounterIds();

        if (CollectionUtils.isEmpty(counterIds)) {
            return content;
        }

        List<Counter> list = new ArrayList<>(counterRepository.filter(counterIds, code, name, status));

        for (Counter counter : list) {
            Optional<Program> programOpt = programService.find(counter.getProgramId());

            CounterLevelObject counterLevelObject = null;
            ProgramTransactionAttribute programTransactionAttribute = null;

            if (programOpt.isPresent()) {
                if (Objects.nonNull(counter.getLevel())) {
                    counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(programOpt.get().getId(), counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
                }
                programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(programOpt.get().getId(), counter.getCounterLevelAttribute()).orElse(null);
            }

            CounterRes counterRes = CounterRes.builder()
                    .counterStatus(counter.getStatus())
                    .counterId(counter.getId())
                    .name(counter.getName())
                    .code(counter.getCode())
                    .startDate(counter.getStartDate())
                    .endDate(counter.getEndDate())
                    .counterAttribute(counter.getCounterAttribute())
                    .counterLevel(Objects.nonNull(counterLevelObject)
                            ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                    .period(counter.getPeriod())
                    .periodDisplay(counter.getPeriod().getDisplayName())
                    .enableRevert(counter.getEnableRevert())
                    .counterType(counter.getType())
                    .counterTypeDisplay(Objects.nonNull(counter.getType()) ? counter.getType().getDisplayName() : null)
                    .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                            ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                            programTransactionAttribute.getName()) : null)
                    .counterLevelType(counter.getLevelType())
                    .counterRuleType(counter.getCounterRuleType())
                    .build();
            content.add(counterRes);
        }

        return content;
    }

    @Override
    public List<CounterRes> getCounterAvailableDetail(
            String code,
            String name,
            ECommonStatus status,
            Integer id
    ) {
        List<CounterRes> content = new ArrayList<>();

        List<Integer> counterIds = schemeLinkCounterRepository.findCounterIdsBySchemeId(id, ECommonStatus.ACTIVE);

        if (CollectionUtils.isEmpty(counterIds)) {
            return content;
        }

        List<Counter> list = new ArrayList<>(counterRepository.filter(counterIds, code, name, status));

        for (Counter counter : list) {
            Optional<Program> programOpt = programService.find(counter.getProgramId());

            CounterLevelObject counterLevelObject = null;
            ProgramTransactionAttribute programTransactionAttribute = null;

            if (programOpt.isPresent()) {
                if (Objects.nonNull(counter.getLevel())) {
                    counterLevelObject = counterLevelObjectService.findByProgramIdAndCodeAndStatus(programOpt.get().getId(), counter.getLevel().getValue(), ECommonStatus.ACTIVE).orElse(null);
                }
                programTransactionAttribute = programTransactionAttributeService.findByProgramIdAndAttribute(programOpt.get().getId(), counter.getCounterLevelAttribute()).orElse(null);
            }

            CounterRes counterRes = CounterRes.builder()
                    .counterStatus(counter.getStatus())
                    .counterId(counter.getId())
                    .name(counter.getName())
                    .code(counter.getCode())
                    .startDate(counter.getStartDate())
                    .endDate(counter.getEndDate())
                    .counterAttribute(counter.getCounterAttribute())
                    .counterLevel(Objects.nonNull(counterLevelObject)
                            ? new DropdownRes(counterLevelObject.getId(), counterLevelObject.getCode(), counterLevelObject.getValue()) : null)
                    .period(counter.getPeriod())
                    .periodDisplay(counter.getPeriod().getDisplayName())
                    .enableRevert(counter.getEnableRevert())
                    .counterType(counter.getType())
                    .counterTypeDisplay(Objects.nonNull(counter.getType()) ? counter.getType().getDisplayName() : null)
                    .counterLevelAttribute(Objects.nonNull(programTransactionAttribute)
                            ? new DropdownRes(programTransactionAttribute.getId(), programTransactionAttribute.getAttribute(),
                            programTransactionAttribute.getName()) : null)
                    .counterLevelType(counter.getLevelType())
                    .counterRuleType(counter.getCounterRuleType())
                    .build();
            content.add(counterRes);
        }

        return content;
    }

    private void verifyCounterRuleInCounterIds(List<Integer> counterIds, List<SchemeLimitReq> schemeLimitReqs) {
        if (Objects.nonNull(schemeLimitReqs)) {
            Set<Integer> counterIdsSet = new HashSet<>(Optional.ofNullable(counterIds)
                    .orElse(Collections.emptyList()));
            for (SchemeLimitReq schemeLimitReq : schemeLimitReqs) {
                if (!counterIdsSet.contains(schemeLimitReq.getCounterId())) {
                    throw new BusinessException(ErrorCode.COUNTER_NOT_IN_COUNTERS, "counter not in list counter", null);
                }
            }
        }
    }

    private List<ResetRuleReq> createEscrowPoint(Scheme scheme,
                                                 CreateSchemeReq payload,
                                                 MakerCheckerInternalDataDetailRes detailResData,
                                                 String createdBy, String updatedBy, String approvedBy) {

        EscrowedPoint escrowedPoint = new EscrowedPoint();
        escrowedPoint.setSchemeId(scheme.getId());
        escrowedPoint.setCode(String.format("%s", UUID.randomUUID()));
        escrowedPoint.setType(payload.getEscrowedPoint().getType());
        escrowedPoint.setValue(payload.getEscrowedPoint().getValue());
        escrowedPoint.setRuleLogic(payload.getEscrowedPoint().getRuleLogic());
        escrowedPoint.setStatus(ECommonStatus.ACTIVE);
        opsReqPendingValidator.updateInfoChecker(escrowedPoint, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
        escrowedPoint = escrowedPointService.save(escrowedPoint);

        return opsRuleService.createRule(scheme.getProgramId(), escrowedPoint, payload.getEscrowedPoint().getRules(), detailResData, approvedBy);
    }

    private List<ResetRuleReq> updateEscrowPoint(Integer programId,
                                                 EscrowedPoint escrowedPoint,
                                                 CreateSchemeReq payload,
                                                 MakerCheckerInternalDataDetailRes detailResData,
                                                 String createdBy, String updatedBy, String approvedBy) {

        escrowedPoint.setType(payload.getEscrowedPoint().getType());
        escrowedPoint.setValue(payload.getEscrowedPoint().getValue());
        escrowedPoint.setRuleLogic(payload.getEscrowedPoint().getRuleLogic());
        opsReqPendingValidator.updateInfoChecker(escrowedPoint, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
        escrowedPoint = escrowedPointService.save(escrowedPoint);

        return opsRuleService.editRule(programId, escrowedPoint, payload.getEscrowedPoint().getRules(), detailResData, approvedBy);
    }

    private List<ResetRuleReq> inactiveEscrowPoint(Integer programId,
                                                   EscrowedPoint escrowedPoint,
                                                   MakerCheckerInternalDataDetailRes detailResData,
                                                   String approvedBy) {
        escrowedPoint.setStatus(ECommonStatus.INACTIVE);
        escrowedPointService.save(escrowedPoint);

        return opsRuleService.inactiveRule(programId, escrowedPoint, detailResData, approvedBy);
    }

    private SchemeLimit convertSchemeLimit(SchemeLimitReq schemeLimitReq, Integer schemeId) {
        SchemeLimit schemeLimit = new SchemeLimit();

        schemeLimit.setSchemeId(schemeId);
        schemeLimit.setCounterId(schemeLimitReq.getCounterId());
        schemeLimit.setStatus(ECommonStatus.ACTIVE);
        schemeLimit.setApprovedAt(new Date());
        schemeLimit.setApprovedBy(opsReqPendingValidator.getCurrentUser());
        schemeLimit.setValue(schemeLimitReq.getValue());
        schemeLimit.setAllowAwardingRemainingValue(schemeLimitReq.getAllowAwardingRemainingValue());

        return schemeLimit;
    }

    private void updateSchemeLimit(List<SchemeLimitReq> schemeLimitReqs, Integer schemeId) {
        schemeLimitService.findActiveBySchemeId(schemeId).forEach(c -> {
            c.setStatus(ECommonStatus.INACTIVE);
            schemeLimitService.save(c);
        });

        if (Objects.nonNull(schemeLimitReqs)) {
            List<SchemeLimit> schemeLimits = schemeLimitReqs.stream()
                    .map(c -> convertSchemeLimit(c, schemeId))
                    .collect(Collectors.toList());

            schemeLimitService.saveAll(schemeLimits);
        }
    }
}
