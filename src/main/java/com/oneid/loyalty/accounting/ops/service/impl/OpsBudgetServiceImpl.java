package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerStatus;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.BudgetEditReq;
import com.oneid.loyalty.accounting.ops.model.req.BudgetReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.res.BudgetInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.BudgetRes;
import com.oneid.loyalty.accounting.ops.model.res.BudgetSchemeRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.OpsBudgetService;
import com.oneid.loyalty.accounting.ops.util.AuditorAwareUtil;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.MongoSearchUtil;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EPoolType;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.constant.ESubBudgetPeriod;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Budget;
import com.oneid.oneloyalty.common.entity.BudgetScheme;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.RewardPool;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SubBudget;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BudgetSchemeService;
import com.oneid.oneloyalty.common.service.BudgetService;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.PoolService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.RewardPoolService;
import com.oneid.oneloyalty.common.service.SchemeService;
import com.oneid.oneloyalty.common.service.SubBudgetService;
import com.oneid.oneloyalty.common.util.LogData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class OpsBudgetServiceImpl implements OpsBudgetService {

    @Autowired
    BusinessService businessService;

    @Autowired
    private AuditorAwareUtil auditorAwareUtil;

    @Autowired
    ProgramService programService;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private SchemeService schemeService;

    @Autowired
    private RewardPoolService rewardPoolService;

    @Autowired
    private PoolService poolService;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private BudgetService budgetService;

    @Autowired
    private BudgetSchemeService budgetSchemeService;

    @Autowired
    private SubBudgetService subBudgetService;

    @Override
    public MakerCheckerInternalMakerRes create(BudgetReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        programService.findByIdAndBusinessId(req.getProgramId(), business.getId());
        req.setBusinessId(business.getId());

        validationCodeDoesNotExist(req.getProgramId(), req.getCode());
        validationCodeDoesNotExistInOtherReqPending(req.getBusinessId(), req.getProgramId(), req.getCode());
        validationScheme(checkSchemeIds(req.getSchemeIds(), null), req.getProgramId());
        if (CollectionUtils.isEmpty(req.getSubBudgets())) {
            validateSubBudget(req.getAmount(), req.getSubBudgets());
        }

        req.setRequestType(ERequestType.CREATE);

        return makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.BUDGET, UUID.randomUUID().toString(), req, req.getMadeReason(), req.isSendEmail());
    }

    @Override
    public Page<BudgetInReviewRes> getInReview(Integer programId, String code, String name, ERequestType requestType, Long startDate, Long endDate, String status, Integer offset, Integer limit, MakerCheckerInternalPreviewReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Map<String, Object> searchPayload = (new MongoSearchUtil())
                .setFilter("payload.program_id", programId, false, false, false)
                .setFilter("payload.code", code, false, false, false)
                .setFilter("payload.name", name, false, true, false, MongoSearchUtil.OPTION_IGNORE_CASE_INSENSITIVITY)
                .setFilter("payload.request_type", requestType, false, false, false)
                .setFilter("payload.status", status, false, false, false)
                .setFilter("payload.start_date", Objects.nonNull(startDate) ? MakerCheckerInternalPreviewReq.RangeDateReq.builder().fromDate(startDate).build() : null, false, false, false)
                .setFilter("payload.end_date", Objects.nonNull(endDate) ? MakerCheckerInternalPreviewReq.RangeDateReq.builder().toDate(endDate).build() : null, false, false, false)
                .build();

        req.setBusinessId(business.getId());
        req.setProperties(searchPayload);
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(EMakerCheckerType.BUDGET, req, offset, limit, null);
        List<BudgetInReviewRes> res = previewRes.getData().stream()
                .map(this::convertPreview)
                .collect(Collectors.toList());
        return new PageImpl<>(res, new OffsetBasedPageRequest(offset, limit), previewRes.getMeta().getTotal());
    }

    @Override
    public BudgetInReviewRes getDetailInReview(String id) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(EMakerCheckerType.BUDGET.getType(), id);
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Budget - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.BUDGET.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Budget - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }

        return convertPreviewDetail(previewDetailRes.getData());
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> response = makerCheckerInternalFeignClient.previewDetail(EMakerCheckerType.BUDGET.getType(), req.getId());
        validateRequest(response, req);
        BudgetReq payload = jsonMapper.convertValue(response.getData().getPayload(), BudgetReq.class);
        if (!payload.getBusinessId().equals(business.getId())) {
            throw new BusinessException(ErrorCode.BUDGET_NOT_FOUND, null, null);
        }
        if (req.getStatus() == EApprovalStatus.APPROVED) {
            String createdBy = null;
            String updatedBy;
            Budget budget;
            programService.findActive(payload.getProgramId());
            businessService.findActive(payload.getBusinessId());
            String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(response.getData().getMadeByUserName());

            if (payload.getId() != null) {
                budget = budgetService.find(payload.getId())
                        .orElseThrow(() -> new BusinessException(ErrorCode.BUDGET_REQUEST_NOT_FOUND, null, null));


                validationScheme(checkSchemeIds(payload.getSchemeIds(), budget), payload.getProgramId());

                updatedBy = response.getData().getMadeByUserName();
                convertBudget(budget, payload);

                budget.setRequestCode(response.getData().getRequestCode());
                budget.setVersion(response.getData().getVersion());
                opsReqPendingValidator.updateInfoChecker(budget, response.getData().getMadeDate(), createdBy, updatedBy, approvedBy);
                budgetService.save(budget);

                updateBudgetScheme(payload.getSchemeIds(), payload.getId());
            } else {
                budget = new Budget();

                validationScheme(checkSchemeIds(payload.getSchemeIds(), null), payload.getProgramId());
                convertBudget(budget, payload);
                updatedBy = createdBy = response.getData().getMadeByUserName();

                budget.setIsSubBudgetConfig(CollectionUtils.isNotEmpty(payload.getSubBudgets()) ? EBoolean.YES : EBoolean.NO);
                budget.setRequestCode(response.getData().getRequestCode());
                budget.setVersion(response.getData().getVersion());
                opsReqPendingValidator.updateInfoChecker(budget, response.getData().getMadeDate(), createdBy, updatedBy, approvedBy);
                budget = budgetService.save(budget);

                updateBudgetScheme(payload.getSchemeIds(), budget.getId());
                createSubBudget(payload.getSubBudgets(), budget.getId());
            }
        }
        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> plAprroved = makerCheckerInternalFeignClient
                .checkerDefault(EMakerCheckerType.BUDGET, req, null);
        if (plAprroved.getMeta().getCode() != HttpStatus.OK.value()) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CANNOT_CHANGE_STATUS, "Checker fail",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
    }

    @Override
    public void cancelInReview(CancelReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        MakerCheckerInternalDataDetailRes detailRes = makerCheckerInternalFeignClient
                .previewDetailDefault(EMakerCheckerType.BUDGET, String.valueOf(req.getId()));
        if (!EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue().equals(detailRes.getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to cancel",
                    LogData.createLogData().append("id", String.valueOf(req.getId())));
        }
        Object businessId = ((LinkedHashMap<?, ?>) detailRes.getPayload()).get("business_id");
        if (!businessId.equals(business.getId())) {
            throw new BusinessException(ErrorCode.BUDGET_NOT_FOUND, null, null);
        }
        makerCheckerInternalFeignClient.cancelDefault(EMakerCheckerType.BUDGET, req);
    }

    @Override
    public BudgetInReviewRes getChangeableByRequestId(Integer id) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Budget budget = budgetService.find(business.getId(), id);

        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.BUDGET.getType(), budget.getRequestCode());

        String editKey = opsReqPendingValidator.generateEditKey(budget.getRequestCode(), budget.getVersion());

        return getBudgetDetail(budget, editKey);
    }

    @Override
    public MakerCheckerInternalMakerRes update(Integer id, BudgetEditReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Budget budget = budgetService.find(business.getId(), id);
        Date currentTime = new Date();
        if (DateTimes.toDate(req.getEndDate()).before(currentTime) ||
                DateTimes.toDate(req.getEndDate()).before(budget.getStartDate())) {
            throw new BusinessException(ErrorCode.BUDGET_END_DATE_INVALID, null, null);
        }

        validationScheme(checkSchemeIds(req.getSchemeIds(), budget), budget.getProgramId());

        opsReqPendingValidator.verifyEditKey(req.getEditKey(), budget.getRequestCode(), budget.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.BUDGET.getType(), budget.getRequestCode());

        List<SubBudget> subBudgets = subBudgetService.findByBudgetIdAndStatus(id, ECommonStatus.ACTIVE);
        BudgetReq payload = BudgetReq.mapperToCreate(id, req, budget, subBudgets);

        return makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.BUDGET,
                Objects.nonNull(budget.getRequestCode()) ? budget.getRequestCode() : UUID.randomUUID().toString(),
                payload, payload.getMadeReason(), payload.isSendEmail());
    }

    @Override
    public Page<BudgetRes>  getAvailableBudgets(Integer programId,
                                               String code,
                                               String name,
                                               ECommonStatus status,
                                               Long startDate,
                                               Long endDate,
                                               Pageable pageable) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());

        SpecificationBuilder specification = new SpecificationBuilder();

        specification.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));

        if (status != null)
            specification.add(new SearchCriteria("status", status, SearchOperation.EQUAL));

        if (programId != null)
            specification.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        if (code != null)
            specification.add(new SearchCriteria("code", code, SearchOperation.EQUAL));

        if (name != null)
            specification.add(new SearchCriteria("name", name, SearchOperation.MATCH));

        if (startDate != null)
            specification.add(new SearchCriteria("startDate", DateTimes.toDate(startDate), SearchOperation.GREATER_THAN_EQUAL_DATE));

        if (endDate != null)
            specification.add(new SearchCriteria("endDate", DateTimes.toDate(endDate), SearchOperation.LESS_THAN_EQUAL_DATE));

        Page<Budget> page = budgetService.find(specification, pageable);
        Program program = programService.findById(programId);
        ShortEntityRes programInfo = new ShortEntityRes(program.getId(), program.getName(), program.getCode());
        List<BudgetRes> res = page.getContent().stream().map(budget -> BudgetRes.builder()
                .id(budget.getId())
                .program(programInfo)
                .status(budget.getStatus())
                .name(budget.getName())
                .code(budget.getCode())
                .startDate(budget.getStartDate())
                .endDate(budget.getEndDate())
                .isSubBudgetConfig(budget.getIsSubBudgetConfig())
                .build()).collect(Collectors.toList());
        return new PageImpl<>(res, pageable, page.getTotalElements());
    }

    @Override
    public BudgetInReviewRes getAvailableById(Integer id) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Budget budget = budgetService.findByIdAndBusinessId(id, business.getId());
        return getBudgetDetail(budget, null);
    }

    @Override
    public List<BudgetSchemeRes> getSchemes(Integer programId, ESchemeType schemeType, EPoolType poolType) {
        // Get current business
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());

        Program program = programService.findByIdAndBusinessId(programId, business.getId());

        // Get scheme IDs from budget if needed
        final List<BudgetScheme> budgetSchemes = budgetSchemeService.findByStatus(ECommonStatus.ACTIVE);
        // Get schemes
        List<Scheme> schemes = schemeService.getSchemesInBudget(
                program.getId(),
                schemeType,
                poolType,
                null
        );

        // Map schemes to response objects
        return schemes.stream()
                .map(scheme -> mapSchemeToSchemeRes(scheme, budgetSchemes))
                .collect(Collectors.toList());
    }

    private void validationCodeDoesNotExist(Integer programId, String code) {
        Budget checker = budgetService.findByProgramIdAndCode(programId, code).orElse(null);
        if (checker != null) {
            throw new BusinessException(ErrorCode.BUDGET_CODE_EXISTED,
                    "[VALIDATION BUDGET] code existed", code);
        }
    }

    private void validationCodeDoesNotExistInOtherReqPending(Integer businessId, Integer programId, String code) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .status(EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue())
                .businessId(businessId)
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(EMakerCheckerType.BUDGET, previewReq, null, null, null);

        Optional<BudgetReq> any = Optional.ofNullable(previewRes.getData()).orElseGet(ArrayList::new).stream()
                .map(data -> this.jsonMapper.convertValue(data.getPayload(), BudgetReq.class))
                .filter(ele -> Objects.nonNull(ele.getCode()))
                .filter(ele -> Objects.nonNull(ele.getProgramId()))
                .filter(ele -> ele.getProgramId().equals(programId) && ele.getCode().equals(code))
                .findAny();

        if (any.isPresent()) {
            throw new BusinessException(ErrorCode.BUDGET_CODE_EXISTED,
                    "[VALIDATION BUDGET] code existed in other requests pending", code);
        }
    }

    private void validationScheme(List<Integer> schemeIds, Integer programId) {
        budgetSchemeService.findFirstBySchemeIdAndStatus(schemeIds, ECommonStatus.ACTIVE)
                .ifPresent(i -> {
                    throw new BusinessException(ErrorCode.SCHEME_ALREADY_EXIST_ANOTHER_BUDGET);
                });
        List<Scheme> schemes = schemeService.findByProgramIdAndListIds(programId, schemeIds);
        Set<Integer> poolCodes = schemes
                .stream()
                .map(Scheme::getRewardPoolId)
                .collect(Collectors.toSet());

        if (poolCodes.size() > 1) {
            throw new BusinessException(ErrorCode.SCHEMES_JUST_HAVE_SAME_POOL);
        }
    }

    private BudgetInReviewRes convertPreview(MakerCheckerInternalDataDetailRes data) {
        BudgetReq budgetReq = this.jsonMapper.convertValue(data.getPayload(), BudgetReq.class);
        Program program = programService.find(budgetReq.getProgramId()).orElse(null);
        return BudgetInReviewRes
                .builder()
                .requestId(data.getId())
                .program(program != null ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .code(budgetReq.getCode())
                .name(budgetReq.getName())
                .startDate(DateTimes.toDate(budgetReq.getStartDate()))
                .endDate(DateTimes.toDate(budgetReq.getEndDate()))
                .approvalStatus(EApprovalStatus.valueOf(data.getStatus()))
                .status(ECommonStatus.of(budgetReq.getStatus()))
                .isSubBudgetConfig(CollectionUtils.isEmpty(budgetReq.getSubBudgets()) ? EBoolean.NO : EBoolean.YES)
                .createdBy(data.getMadeByUserName())
                .createdAt(data.getMadeDateToDate())
                .approvedBy(data.getCheckedByUserName())
                .approvedAt(data.getCheckedDateToDate())
                .requestType(budgetReq.getRequestType())
                .build();
    }

    private BudgetInReviewRes convertPreviewDetail(MakerCheckerInternalDataDetailRes data) {
        BudgetReq budgetReq = this.jsonMapper.convertValue(data.getPayload(), BudgetReq.class);
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        if (!business.getId().equals(budgetReq.getBusinessId())) {
            throw new BusinessException(ErrorCode.BUDGET_NOT_FOUND, null, null);
        }
        Program program = programService.findByIdAndBusinessId(budgetReq.getProgramId(), business.getId());
        BudgetInReviewRes res = BudgetInReviewRes.builder()
                .requestId(data.getId())
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .status(ECommonStatus.of(budgetReq.getStatus()))
                .code(budgetReq.getCode())
                .name(budgetReq.getName())
                .description(budgetReq.getDescription())
                .startDate(DateTimes.toDate(budgetReq.getStartDate()))
                .endDate(DateTimes.toDate(budgetReq.getEndDate()))
                .amount(budgetReq.getAmount())
                .allowRemainingValue(EBoolean.of(budgetReq.getAllowRemainingValue()))
                .approvalStatus(EApprovalStatus.valueOf(data.getStatus()))
                .createdBy(data.getMadeByUserName())
                .createdAt(data.getMadeDateToDate())
                .approvedBy(data.getCheckedByUserName())
                .approvedAt(data.getCheckedDateToDate())
                .reason(data.getComment())
                .madeReason(data.getMadeReason())
                .requestType(budgetReq.getRequestType())
                .build();

        if (!CollectionUtils.isEmpty(budgetReq.getSubBudgets())) {
            res.setSubBudgets(budgetReq.getSubBudgets().parallelStream().map(BudgetInReviewRes.SubBudget::from)
                    .collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(budgetReq.getSchemeIds())) {
            List<Scheme> schemes = schemeService.findByIdIn(budgetReq.getSchemeIds());
            List<SchemeRes> schemeRes = getScheme(schemes);
            res.setSchemes(schemeRes);
        }

        return res;
    }

    private void validateRequest(APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> response, ApprovalReq req) {
        if (!EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue().equals(response.getData().getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to reject or approve",
                    LogData.createLogData()
                            .append("id", req.getId())
                            .append("approve_status", req.getStatus()));
        }
        if (response.getMeta().getCode() != HttpStatus.OK.value()) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Checker id not found",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
    }

    private void convertBudget(Budget budget, BudgetReq payload) {
        budget.setId(payload.getId());
        budget.setBusinessId(payload.getBusinessId());
        budget.setCode(payload.getCode());
        budget.setProgramId(payload.getProgramId());
        budget.setName(payload.getName());
        budget.setDescription(payload.getDescription());
        budget.setStatus(ECommonStatus.of(payload.getStatus()));
        budget.setStartDate(DateTimes.toDate(payload.getStartDate()));
        budget.setEndDate(DateTimes.toDate(payload.getEndDate()));
        budget.setAmount(payload.getAmount());
        budget.setAllowRemainingValue(EBoolean.of(payload.getAllowRemainingValue()));
    }

    private void updateBudgetScheme(List<Integer> schemeIds, Integer budgetId) {
        Set<Integer> activeIds = new HashSet<>();
        Predicate<BudgetScheme> predicate = ele -> {
            if (ECommonStatus.ACTIVE.equals(ele.getStatus())) {
                activeIds.add(ele.getSchemeId());
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        };

        Map<Integer, BudgetScheme> mapBudgetSchemeInactive = budgetSchemeService
                .findByBudgetId(budgetId)
                .stream()
                .filter(predicate)
                .collect(Collectors.toMap(BudgetScheme::getSchemeId,
                        Function.identity(), (k1, k2) -> k2));

        // Handle create a budget scheme
        List<BudgetScheme> budgetSchemeEntities = new ArrayList<>();

        // Handle edit a budget scheme
        // Exp: | create: 1,2,3 | edit: 2,3,4 | we must create a scheme with id is
        // 4 and remove a scheme 1
        ArrayList<Integer> createIds = new ArrayList<>(
                CollectionUtils.subtract(schemeIds, activeIds)); // 4
        ArrayList<Integer> deleteIds = new ArrayList<>(
                CollectionUtils.subtract(activeIds, schemeIds)); // 1
        if (!createIds.isEmpty()) {
            for (Integer schemeId : createIds) {
                BudgetScheme budgetScheme;
                // Set the active status for obsolete record
                if (mapBudgetSchemeInactive.containsKey(schemeId)) {
                    budgetScheme = mapBudgetSchemeInactive.get(schemeId);
                    budgetScheme.setStatus(ECommonStatus.ACTIVE);
                    budgetScheme.setApprovedAt(new Date());
                    budgetScheme.setApprovedBy(opsReqPendingValidator.getCurrentUser());
                } else {
                    // New record
                    budgetScheme = convertBudgetScheme(budgetId, schemeId);
                }
                budgetSchemeEntities.add(budgetScheme);
            }
        }
        if (!deleteIds.isEmpty()) {
            for (Integer deleteId : deleteIds) {
                List<BudgetScheme> budgetSchemes = budgetSchemeService
                        .findActive(budgetId, deleteId);
                for (BudgetScheme budgetScheme : budgetSchemes) {
                    budgetScheme.setStatus(ECommonStatus.INACTIVE);
                    budgetScheme.setApprovedAt(new Date());
                    budgetScheme.setApprovedBy(opsReqPendingValidator.getCurrentUser());
                    budgetSchemeEntities.add(budgetScheme);
                }
            }
        }
        budgetSchemeService.saveAll(budgetSchemeEntities);
    }

    private void createSubBudget(List<BudgetReq.SubBudget> subBudgets, Integer budgetId) {
        subBudgets.forEach(i -> {
            SubBudget subBudget = new SubBudget();
            subBudget.setBudgetId(budgetId);
            subBudget.setAmount(i.getAmount());
            subBudget.setPeriod(i.getPeriod());
            subBudget.setOrderNumber(i.getOrderNumber());
            subBudget.setStatus(ECommonStatus.ACTIVE);
            subBudget.setApprovedAt(new Date());
            subBudget.setApprovedBy(opsReqPendingValidator.getCurrentUser());

            subBudgetService.save(subBudget);
        });
    }

    private BudgetScheme convertBudgetScheme(Integer budgetId, Integer schemeId) {
        BudgetScheme budgetScheme = new BudgetScheme();
        budgetScheme.setBudgetId(budgetId);
        budgetScheme.setSchemeId(schemeId);
        budgetScheme.setStatus(ECommonStatus.ACTIVE);
        budgetScheme.setApprovedAt(new Date());
        budgetScheme.setApprovedBy(opsReqPendingValidator.getCurrentUser());
        return budgetScheme;
    }

    private BudgetInReviewRes getBudgetDetail(Budget budget, String editKey) {
        Program program = programService.find(budget.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        BudgetInReviewRes res = BudgetInReviewRes.builder()
                .id(budget.getId())
                .editKey(StringUtils.isBlank(editKey) ? null : editKey)
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .code(budget.getCode())
                .name(budget.getName())
                .description(budget.getDescription())
                .status(budget.getStatus())
                .amount(budget.getAmount())
                .allowRemainingValue(budget.getAllowRemainingValue())
                .createdBy(budget.getCreatedBy())
                .updatedBy(budget.getUpdatedBy())
                .approvedBy(budget.getApprovedBy())
                .createdAt(budget.getCreatedAt())
                .updatedAt(budget.getUpdatedAt())
                .approvedAt(budget.getApprovedAt())
                .version(budget.getVersion())
                .startDate(budget.getStartDate())
                .endDate(budget.getEndDate())
                .build();

        List<SubBudget> subBudgets = subBudgetService.findByBudgetIdAndStatus(budget.getId(), ECommonStatus.ACTIVE);
        if (!CollectionUtils.isEmpty(subBudgets)) {
            res.setSubBudgets(subBudgets.parallelStream().map(BudgetInReviewRes.SubBudget::from)
                    .collect(Collectors.toList()));
        }

        List<Integer> schemeIds = budgetSchemeService.findSchemeIdByBudgetIdAndStatus(budget.getId(), ECommonStatus.ACTIVE);
        if (!CollectionUtils.isEmpty(schemeIds)) {
            List<Scheme> schemes = schemeService.findByIdIn(schemeIds);
            List<SchemeRes> schemeRes = getScheme(schemes);
            res.setSchemes(schemeRes);
        }

        return res;
    }

    private List<SchemeRes> getScheme(List<Scheme> schemes) {
        return schemes.parallelStream().map(i -> {
            ShortEntityRes poolRes = null;
            if (i.getRewardPoolId() != null) {
                Optional<RewardPool> rewardPoolOpt = rewardPoolService.findById(i.getRewardPoolId());
                if (rewardPoolOpt.isPresent()) {
                    RewardPool rewardPool = rewardPoolOpt.get();
                    poolRes = new ShortEntityRes(rewardPool.getId(), rewardPool.getName(), rewardPool.getPoolCode());
                }
            } else {
                Optional<Pool> poolOpt = poolService.find(i.getPoolId());
                if (poolOpt.isPresent()) {
                    Pool pool = poolOpt.get();
                    poolRes = new ShortEntityRes(pool.getId(), pool.getName(), pool.getCode());
                }
            }
            return SchemeRes.builder()
                    .id(i.getId())
                    .pool(poolRes)
                    .code(i.getCode())
                    .name(i.getName())
                    .startDate(i.getStartDate())
                    .endDate(i.getEndDate())
                    .status(i.getStatus())
                    .build();
        }).collect(Collectors.toList());
    }

    private void validateSubBudget(BigDecimal amount, List<BudgetReq.SubBudget> subBudgets) {
        BudgetReq.SubBudget subBudgetDaily = null;
        BudgetReq.SubBudget subBudgetMonthly = null;
        for (BudgetReq.SubBudget subBudget : subBudgets) {
            if (ESubBudgetPeriod.DAILY.equals(subBudget.getPeriod())) {
                subBudgetDaily = subBudget;
            } else {
                subBudgetMonthly = subBudget;
            }

            if (subBudget.getAmount().compareTo(amount) >= 0) {
                throw new BusinessException(ErrorCode.SCHEMES_JUST_HAVE_SAME_POOL);
            }
        }

        if (Objects.nonNull(subBudgetDaily)
                && Objects.nonNull(subBudgetMonthly)
                && subBudgetDaily.getAmount().compareTo(subBudgetMonthly.getAmount()) >= 0) {
            throw new BusinessException(ErrorCode.AMOUNT_SUB_BUDGET_DAILY_MUST_LESS_THAN_AMOUNT_SUB_BUDGET_MONTHLY);

        }
    }

    /**
     * Maps a Scheme entity to a SchemeRes response object
     *
     * @param scheme The scheme entity to map
     * @return The mapped SchemeRes object
     */
    private BudgetSchemeRes mapSchemeToSchemeRes(Scheme scheme, List<BudgetScheme> budgetSchemes) {
        ShortEntityRes poolRes = getPoolForScheme(scheme);
        Integer budgetSchemeId = null;
        
        if (budgetSchemes != null) {
            budgetSchemeId = budgetSchemes.stream()
                .filter(bs -> bs.getSchemeId().equals(scheme.getId()))
                .map(BudgetScheme::getBudgetId)
                .findFirst()
                .orElse(null);
        }

        return BudgetSchemeRes.builder()
                .id(scheme.getId())
                .pool(poolRes)
                .code(scheme.getCode())
                .name(scheme.getName())
                .startDate(scheme.getStartDate())
                .endDate(scheme.getEndDate())
                .status(scheme.getStatus())
                .linkedBudgetId(budgetSchemeId)
                .build();
    }

    /**
     * Gets the pool information for a scheme
     *
     * @param scheme The scheme to get the pool for
     * @return A ShortEntityRes representing the pool, or null if not found
     */
    private ShortEntityRes getPoolForScheme(Scheme scheme) {
        // Check for reward pool
        if (scheme.getRewardPoolId() != null) {
            return rewardPoolService.findById(scheme.getRewardPoolId())
                    .map(rewardPool -> new ShortEntityRes(
                            rewardPool.getId(),
                            rewardPool.getName(),
                            rewardPool.getPoolCode()))
                    .orElse(null);
        }

        // Check for regular pool
        return poolService.find(scheme.getPoolId())
                .map(pool -> new ShortEntityRes(
                        pool.getId(),
                        pool.getName(),
                        pool.getCode()))
                .orElse(null);
    }

    private List<Integer> checkSchemeIds(List<Integer> schemeIds, Budget budget) {
        if (Objects.nonNull(budget)) {
            List<Integer> schemeBudgetIds = budgetSchemeService
                    .findByBudgetId(budget.getId())
                    .stream()
                    .map(BudgetScheme::getSchemeId)
                    .collect(Collectors.toList());

            return schemeIds.stream()
                    .filter(item -> !schemeBudgetIds.contains(item))
                    .collect(Collectors.toList());
        }

        return schemeIds;
    }
}