package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.res.DropdownRes;
import com.oneid.loyalty.accounting.ops.model.res.ProductInfoRes;
import com.oneid.loyalty.accounting.ops.service.OpsCustomerProductInfoService;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.entity.AttributeMasterData;
import com.oneid.oneloyalty.common.entity.CustomerProductInfo;
import com.oneid.oneloyalty.common.entity.Ps2MtdProductCode;
import com.oneid.oneloyalty.common.service.AttributeMasterDataService;
import com.oneid.oneloyalty.common.service.CustomerProductInfoService;
import com.oneid.oneloyalty.common.service.Ps2MtdProductCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class OpsCustomerProductInfoServiceImpl implements OpsCustomerProductInfoService {

    private final String pattern = "dd-MMM-yyyy";
    @Autowired
    private CustomerProductInfoService customerProductInfoService;
    @Autowired
    private Ps2MtdProductCodeService ps2MtdProductCodeService;
    @Autowired
    private AttributeMasterDataService attributeMasterDataService;

    @Override
    public Page<ProductInfoRes> getProductInfoList(String partnerCustomerId,
                                                   String arrangementId,
                                                   String productCode,
                                                   String productLv1,
                                                   String productLv2,
                                                   Date startOpenDate,
                                                   Date endOpenDate,
                                                   Date startExpireDate,
                                                   Date endExpireDate,
                                                   String status,
                                                   Pageable pageRequest) {
        // Filter CustomerProductInfo
        Page<CustomerProductInfo> customerProductInfos =
                customerProductInfoService.filter(partnerCustomerId, arrangementId, productCode, productLv1, productLv2,
                        startOpenDate, endOpenDate, startExpireDate, endExpireDate, status, pageRequest);

        List<ProductInfoRes> result = customerProductInfos.stream().map(c -> {
            Ps2MtdProductCode ps2MtdProductCode = null;

            // Get product code by product level
            if (Objects.nonNull(c.getProductLv6())) {
                ps2MtdProductCode = ps2MtdProductCodeService.findFirstByPdLv6Id(c.getProductLv6()).orElse(null);
            }
            if (Objects.nonNull(c.getProductLv5()) && Objects.isNull(ps2MtdProductCode)) {
                ps2MtdProductCode = ps2MtdProductCodeService.findFirstByPdLv5Id(c.getProductLv5()).orElse(null);
            }
            if (Objects.nonNull(c.getProductLv4()) && Objects.isNull(ps2MtdProductCode)) {
                ps2MtdProductCode = ps2MtdProductCodeService.findFirstByPdLv4Id(c.getProductLv4()).orElse(null);
            }
            if (Objects.nonNull(c.getProductLv3()) && Objects.isNull(ps2MtdProductCode)) {
                ps2MtdProductCode = ps2MtdProductCodeService.findFirstByPdLv3Id(c.getProductLv3()).orElse(null);
            }
            if (Objects.nonNull(c.getProductLv2()) && Objects.isNull(ps2MtdProductCode)) {
                ps2MtdProductCode = ps2MtdProductCodeService.findFirstByPdLv2Id(c.getProductLv2()).orElse(null);
            }
            if (Objects.nonNull(c.getProductLv1()) && Objects.isNull(ps2MtdProductCode)) {
                ps2MtdProductCode = ps2MtdProductCodeService.findFirstByPdLv1Id(c.getProductLv1()).orElse(null);
            }

            AttributeMasterData branchCode = attributeMasterDataService.findFirstByAttributeCodeAndValue("PRD_BRANCH_CODE", c.getPrdBranchCode());
            AttributeMasterData prdStatus = attributeMasterDataService.findFirstByAttributeCodeAndValue("PRD_STATUS", c.getPrdStatus());
            AttributeMasterData prdCcyCode = attributeMasterDataService.findFirstByAttributeCodeAndValue("PRD_CCY_CODE", c.getPrdCcyCode());
            AttributeMasterData prdCombo = attributeMasterDataService.findFirstByAttributeCodeAndValue("TXN_PRD_COMBO", c.getPrdCombo());
            AttributeMasterData prdJointHolderInd = attributeMasterDataService.findFirstByAttributeCodeAndValue("TXN_PRD_JOINT_HOLDER_IND", c.getPrdJointHolderInd());
            AttributeMasterData prdAutoBillInd = attributeMasterDataService.findFirstByAttributeCodeAndValue("TXN_PRD_AUTO_BILL_IND", c.getPrdAutoBillInd());
            AttributeMasterData prdSalesChannel = attributeMasterDataService.findFirstByAttributeCodeAndValue("TXN_PRD_SALES_CHANNEL", c.getPrdSalesChannel());
            AttributeMasterData prdPaymentDate = attributeMasterDataService.findFirstByAttributeCodeAndValue("TXN_PRD_PAYMENT_DATE", c.getPrdPaymentDate());
            AttributeMasterData prdIssueDate = attributeMasterDataService.findFirstByAttributeCodeAndValue("TXN_PRD_ISSUE_DATE", c.getPrdIssueDate());
            AttributeMasterData prdInsuranceRenew = attributeMasterDataService.findFirstByAttributeCodeAndValue("TXN_PRD_INSURANCE_RENEW", c.getPrdInsuranceRenew());

            // Prepare Response
            ProductInfoRes res = ProductInfoRes.builder()
                    .arrangementId(c.getArrangementId())
                    .prdAccArrId(c.getPrdAccArrId())
                    .prdBranchCode(new DropdownRes(null, c.getPrdBranchCode(), Objects.nonNull(branchCode) ? branchCode.getDescription() : null, null))
                    .prodOpenDate(DateTimes.parseDate(pattern, c.getProdOpenDate()))
                    .prodExpireDate(DateTimes.parseDate(pattern, c.getProdExpireDate()))
                    .prdBillCycle(DateTimes.parseDate(pattern, c.getPrdBillCycle()))
                    .prdStatus(new DropdownRes(null, c.getPrdStatus(), Objects.nonNull(prdStatus) ? prdStatus.getDescription() : null, null))
                    .prdCcyCode(new DropdownRes(null, c.getPrdCcyCode(), Objects.nonNull(prdCcyCode) ? prdCcyCode.getDescription() : null, null))
                    .prdCombo(new DropdownRes(null, c.getPrdCombo(), Objects.nonNull(prdCombo) ? prdCombo.getDescription() : null, null))
                    .prdJointHolderInd(new DropdownRes(null, c.getPrdJointHolderInd(), Objects.nonNull(prdJointHolderInd) ? prdJointHolderInd.getDescription() : null, null))
                    .prdAutoBillInd(new DropdownRes(null, c.getPrdAutoBillInd(), Objects.nonNull(prdAutoBillInd) ? prdAutoBillInd.getDescription() : null, null))
                    .prdSalesChannel(new DropdownRes(null, c.getPrdSalesChannel(), Objects.nonNull(prdSalesChannel) ? prdSalesChannel.getDescription() : null, null))
                    .prdPaymentDate(new DropdownRes(null, c.getPrdPaymentDate(), Objects.nonNull(prdPaymentDate) ? prdPaymentDate.getDescription() : null, null))
                    .prdIssueDate(new DropdownRes(null, c.getPrdIssueDate(), Objects.nonNull(prdIssueDate) ? prdIssueDate.getDescription() : null, null))
                    .prdInsuranceRenew(new DropdownRes(null, c.getPrdInsuranceRenew(), Objects.nonNull(prdInsuranceRenew) ? prdInsuranceRenew.getDescription() : null, null))
                    .build();
            if (Objects.nonNull(ps2MtdProductCode)) {
                res.setProductCode(new DropdownRes(null, c.getProductCode(), Objects.nonNull(c.getProductCode()) ? ps2MtdProductCode.getPdLv5Vi() : null, Objects.nonNull(c.getProductCode()) ? ps2MtdProductCode.getPdLv5() : null));
                res.setProductLv1(new DropdownRes(null, c.getProductLv1(), null, Objects.nonNull(c.getProductLv1()) ? ps2MtdProductCode.getPdLv1() : null));
                res.setProductLv2(new DropdownRes(null, c.getProductLv2(), null, Objects.nonNull(c.getProductLv2()) ? ps2MtdProductCode.getPdLv2() : null));
                res.setProductLv3(new DropdownRes(null, c.getProductLv3(), null, Objects.nonNull(c.getProductLv3()) ? ps2MtdProductCode.getPdLv3() : null));
                res.setProductLv4(new DropdownRes(null, c.getProductLv4(), null, Objects.nonNull(c.getProductLv4()) ? ps2MtdProductCode.getPdLv4() : null));
                res.setProductLv5(new DropdownRes(null, c.getProductLv5(), null, Objects.nonNull(c.getProductLv5()) ? ps2MtdProductCode.getPdLv5() : null));
                res.setProductLv6(new DropdownRes(null, c.getProductLv6(), null, Objects.nonNull(c.getProductLv6()) ? ps2MtdProductCode.getPdLv6() : null));
            }
            return res;
        }).collect(Collectors.toList());
        return new PageImpl<>(result, pageRequest, customerProductInfos.getTotalElements());
    }

    @Override
    public List<DropdownRes> getAllProductCode() {
        List<Object[]> ps2MtdProductCodes = ps2MtdProductCodeService.findDistinctCodeAndPdLv5();
        return ps2MtdProductCodes.stream()
                .map(p -> new DropdownRes(null, (String) p[0], null, (String) p[1]))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> getAllProductLv1() {
        List<Object[]> ps2MtdProductCodes = ps2MtdProductCodeService.findDistinctPdLv1IdAndPdLv1();
        return ps2MtdProductCodes.stream()
                .map(p -> new DropdownRes(null, (String) p[0], null, (String) p[1]))
                .collect(Collectors.toList());
    }

    @Override
    public List<DropdownRes> getAllProductLv2() {
        List<Object[]> ps2MtdProductCodes = ps2MtdProductCodeService.findDistinctPdLv2IdAndPdLv2();
        return ps2MtdProductCodes.stream()
                .map(p -> new DropdownRes(null, (String) p[0], null, (String) p[1]))
                .collect(Collectors.toList());
    }
}