package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.BudgetEditReq;
import com.oneid.loyalty.accounting.ops.model.req.BudgetReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.res.BudgetInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.BudgetRes;
import com.oneid.loyalty.accounting.ops.model.res.BudgetSchemeRes;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EPoolType;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface OpsBudgetService {

    MakerCheckerInternalMakerRes create(BudgetReq req);

    Page<BudgetInReviewRes> getInReview(Integer programId,
                                        String code,
                                        String name,
                                        ERequestType requestType,
                                        Long startDate,
                                        Long endDate,
                                        String status,
                                        Integer offset,
                                        Integer limit,
                                        MakerCheckerInternalPreviewReq req);

    BudgetInReviewRes getDetailInReview(String id);

    void approve(ApprovalReq req);

    void cancelInReview(CancelReq req);

    BudgetInReviewRes getChangeableByRequestId(Integer id);

    MakerCheckerInternalMakerRes update(Integer id, BudgetEditReq req);

    Page<BudgetRes> getAvailableBudgets(Integer programId,
                                        String code,
                                        String name,
                                        ECommonStatus status,
                                        Long startDate,
                                        Long endDate,
                                        Pageable pageable);

    BudgetInReviewRes getAvailableById(Integer id);

    List<BudgetSchemeRes> getSchemes(Integer programId, ESchemeType schemeType, EPoolType poolType);
}