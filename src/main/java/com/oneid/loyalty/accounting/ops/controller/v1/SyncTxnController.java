package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.feign.OneloyaltyElasticFeignClient;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(value = {"v1/sync"})
public class SyncTxnController extends BaseController {

    @Autowired
    OneloyaltyElasticFeignClient oneloyaltyElasticFeignClient;

    @PostMapping("/{point_txn_id}")
    public ResponseEntity<?> searchListHistory(@PathVariable("point_txn_id") String pointTnxId) {
        oneloyaltyElasticFeignClient.syncElastic(UUID.randomUUID().toString(), List.of(pointTnxId));

        return success(null);
    }
}