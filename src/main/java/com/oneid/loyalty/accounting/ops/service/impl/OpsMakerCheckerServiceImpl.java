package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalCountReq;
import com.oneid.loyalty.accounting.ops.model.res.TotalRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsMakerCheckerService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.util.AuditorAwareUtil;
import com.oneid.loyalty.accounting.ops.util.MongoSearchUtil;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.TransactionBatchRequestService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class OpsMakerCheckerServiceImpl implements OpsMakerCheckerService {

    @Autowired
    private AuditorAwareUtil auditorAwareUtil;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private TransactionBatchRequestService batchRequestService;

    @Override
    public TotalRequestRes countTotalRequest(String requestType) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        EMakerCheckerType type = EMakerCheckerType.of(requestType);
        if (Objects.isNull(type)) {
            log.error("request_type is invalid, type: {}", requestType);
            throw new BusinessException(ErrorCode.BAD_REQUEST);
        } else if (EMakerCheckerType.MEMBER_STATUS.equals(type)) {
            List<String> scopes = auditorAwareUtil.getScopes();
            List<String> permission = new ArrayList<>();
            for (String scope : scopes) {
                if ("view:member_status".equals(scope)) {
                    permission.add(AccessRole.MEMBER_STATUS.getCode());
                }
                if ("view:block_member_status".equals(scope)) {
                    permission.add(AccessRole.BLOCK_MEMBER_STATUS.getCode());
                }
            }

            Map<String, Object> searchPayload = (new MongoSearchUtil())
                    .setFilter("payload.permission", permission, true, false, false)
                    .build();

            MakerCheckerInternalCountReq.Filter filter = new MakerCheckerInternalCountReq.Filter();
            filter.setBusinessId(business.getId());
            filter.setProperties(searchPayload);

            MakerCheckerInternalCountReq req = MakerCheckerInternalCountReq.builder()
                    .filter(filter)
                    .build();

            return makerCheckerInternalFeignClient.countTotalRequest(type, req);
        } else if (EMakerCheckerType.TRANSACTION_REQUEST.equals(type)) {
            return this.countTotalRequest(business.getId(), null);
        } else {
            MakerCheckerInternalCountReq.Filter filter = new MakerCheckerInternalCountReq.Filter();
            if (!EMakerCheckerType.CURRENCY_RATE.equals(type)) {
                filter.setBusinessId(business.getId());
            }
            MakerCheckerInternalCountReq req = MakerCheckerInternalCountReq.builder()
                    .filter(filter)
                    .build();
            return makerCheckerInternalFeignClient.countTotalRequest(type, req);
        }
    }

    private TotalRequestRes countTotalRequest(Integer businessId, Integer programId) {
        List<Object[]> data = batchRequestService.countTotalRequestByStatus(businessId, programId);
        TotalRequestRes response = new TotalRequestRes();
        if (CollectionUtils.isEmpty(data)) {
            return response;
        }else {
            data.stream()
                    .filter(obj -> Objects.nonNull(obj[0]) && Objects.nonNull(obj[1]))
                    .forEach(obj -> {
                        EApprovalStatus status = (EApprovalStatus) obj[0];
                        Long total = (Long) obj[1];
                        switch (status) {
                            case PENDING:
                                response.setTotalPending(total);
                                break;
                            case CANCELLED:
                                response.setTotalCancel(total);
                                break;
                            case APPROVED:
                                response.setTotalApproved(total);
                                break;
                            case REJECTED:
                                response.setTotalReject(total);
                                break;
                            default:
                                // When status is DRAFT
                                response.setTotalDraft(total);
                                break;
                        }
                    });
            return response;
        }
    }
}