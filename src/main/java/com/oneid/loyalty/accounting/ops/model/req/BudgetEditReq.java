package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERequestType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Convert;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
@AllArgsConstructor
public class BudgetEditReq implements Serializable {

    private static final long serialVersionUID = 2813661866947854920L;

    @NotBlank(message = "'edit_key' must not be blank")
    @Size(max = 255)
    private String editKey;

    @NotBlank(message = "Name cannot be blank")
    @Size(max = 100, message = "name cannot exceed {max} characters")
    private String name;

    @Size(max = 255, message = "Description cannot exceed {max} characters")
    private String description;

    @NotBlank(message = "Status cannot be blank")
    @Convert(converter = ECommonStatus.Converter.class)
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    private String status;

    @NotNull(message = "'end_date' must not be null")
    private Long endDate;

    @NotNull(message = "'scheme' must not be null")
    private List<Integer> schemeIds;

    private ERequestType requestType;

    private String madeReason;

    @JsonProperty("send_email")
    private boolean sendEmail;
}