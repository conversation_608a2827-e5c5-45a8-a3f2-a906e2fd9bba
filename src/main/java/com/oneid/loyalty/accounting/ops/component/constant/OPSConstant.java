package com.oneid.loyalty.accounting.ops.component.constant;

/**
 * author viet.le2
 */
public interface OPSConstant {
    String LOG_ID = "logId";
    String KEY_X_REQUEST_ID = "x-request-id";
    String REQUEST = "REQUEST";
    String RESPONSE = "RESPONSE";
    String MSG = "msg";
    String REQUEST_ID = "request_id";
    String REQUEST_URL = "request_url";
    String REQUEST_METHOD = "request_method";
    String REQUEST_PARAM = "request_param";
    String REQUEST_BODY = "request_body";
    String RESPONSE_META = "response_meta";
    String RESPONSE_TIME = "response_time";
    String INVALID = "Invalid";
    String VALID = "Valid";
    String TIER_ADJ = "tier-adjustment";
    String TXN = "txn";
    String ATTRIBUTE = "attribute";
    String AVAILABLE_TXN = "available-txn";
    String VERIFY_TXN_ADJ = "verify-txn-adjustment";
    String VERIFY_TXN_SALE = "verify-txn-sale";
    String VERIFY_TXN_REVERT_FULL = "verify-txn-revert-full";
    String VERIFY_TXN_REVERT_POINT = "verify-txn-revert-point";
    String VERIFY_TXN_REVERT_PARTIAL = "verify-txn-revert-partial";
    String VERIFY_ATTRIBUTE = "verify-attribute";

    String AVAILABlE_ATTRIBUTE = "available-attribute";
    String REASON_FORMAT = "[%s] %s";

    Integer TIMES_OF_DAY = 86400000;
    String FILE_EXT_XLSX = "xlsx";
    String FILE_EXT_YML = "yml";

    String FILE_NAME_EXPORT_TRANSACTION_BATCH = "OOP_%s_%s_%s_%s.xlsx";

    String FILE_NAME_EXPORT_ATTRIBUTE = "AttributeManagement_%s_%s_MasterData.xlsx";
    String DATE_FORMAT = "yyyy-MM-dd";

    String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    Integer SIZE_OF_BATCH = 200;

    String DEFAULT_TRANSACTION_CHANNEL = "OPS";

    String DEFAULT_TRANSACTION_SERVICE_CODE = "OLSTransactionRequest";
    String DEFAULT_OFFSET = "0";
    String DEFAULT_LIMIT_10 = "10";
    String DEFAULT_LIMIT_20 = "20";
}
