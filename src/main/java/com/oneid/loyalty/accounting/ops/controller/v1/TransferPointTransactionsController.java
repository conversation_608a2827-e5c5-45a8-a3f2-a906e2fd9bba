package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.model.res.TransferPointTransactionRes;
import com.oneid.loyalty.accounting.ops.service.OpsTransactionService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.oneloyalty.common.constant.EPayWithPointStatus;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Date;

@RestController
@RequestMapping("v1/transfer-point-transactions")
@Validated
public class TransferPointTransactionsController extends BaseController {
    @Autowired
    OpsTransactionService opsTransactionService;

    @GetMapping("/list")
    @Authorize(role = AccessRole.TRANSACTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getList(
            @RequestParam(name = "program_id") @NotNull(message = "'program_id' must not be null'") Integer programId,
            @RequestParam(name = "invoice_no", required = false) String invoiceNo,
            @RequestParam(name = "txn_ref_no", required = false) String txnRefNo,
            @RequestParam(name = "account_type", required = false) EOpsIdType accountType,
            @RequestParam(name = "account_code", required = false) String accountCode,
            @RequestParam(name = "txn_time_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date txnTimeStart,
            @RequestParam(name = "txn_time_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date txnTimeEnd,
            @RequestParam(name = "status", required = false) ETransactionStatus status,
            @MakerCheckerOffsetPageable Pageable pageable
    ) {

        Page<TransferPointTransactionRes> page = opsTransactionService.getTransferPointTransactionList(programId, invoiceNo, txnRefNo,
                accountType, accountCode, txnTimeStart, txnTimeEnd, status, pageable);

        return success(page, (int) pageable.getOffset(), pageable.getPageSize());
    }

    @GetMapping("/details/{txnRefNo}")
    @Authorize(role = AccessRole.TRANSACTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getDetail(@PathVariable String txnRefNo) {
        return success(opsTransactionService.getTransferPointTransactionInfoDetail(txnRefNo));
    }
}