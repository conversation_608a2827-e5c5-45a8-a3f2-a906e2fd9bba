package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerStatus;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.constant.EScheduleEventAttribute;
import com.oneid.loyalty.accounting.ops.constant.ESchemeSortingField;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.feign.JobManagementFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyRuleFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.CreateEventReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.UpdateScheduleStatus;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalSearchReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.kafka.event.ResetRuleEvent;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.*;
import com.oneid.loyalty.accounting.ops.model.res.AvailableScheduleRes;
import com.oneid.loyalty.accounting.ops.model.res.EditScheduleRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleConditionRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.loyalty.accounting.ops.model.res.ScheduleHistoryRes;
import com.oneid.loyalty.accounting.ops.service.OpsAttributeService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.model.res.ScheduleDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.OpsScheduleService;
import com.oneid.loyalty.accounting.ops.util.AuditorAwareUtil;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.loyalty.accounting.ops.util.MongoSearchUtil;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.ESchedulePeriod;
import com.oneid.oneloyalty.common.constant.EScheduleType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramAttributeServiceType;
import com.oneid.oneloyalty.common.entity.Rule;
import com.oneid.oneloyalty.common.entity.RuleCondition;
import com.oneid.oneloyalty.common.entity.Schedule;
import com.oneid.oneloyalty.common.entity.ScheduleHistory;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.ProgramAttributeServiceTypeRepository;
import com.oneid.oneloyalty.common.repository.RuleConditionRepository;
import com.oneid.oneloyalty.common.repository.ScheduleRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.RuleService;
import com.oneid.oneloyalty.common.service.ScheduleHistoryService;
import com.oneid.oneloyalty.common.service.ScheduleService;
import com.oneid.oneloyalty.common.util.LogData;
import lombok.SneakyThrows;
import com.oneid.oneloyalty.common.util.OffsetBasedPageRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class OpsScheduleServiceImpl implements OpsScheduleService {

    @Autowired
    private OpsAttributeService opsAttributeService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private AuditorAwareUtil auditorAwareUtil;

    @Autowired
    private OpsRuleService opsRuleService;

    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private OneloyaltyRuleFeignClient oneloyaltyRuleFeignClient;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private ScheduleHistoryService scheduleHistoryService;

    @Autowired
    private JobManagementFeignClient jobManagementFeignClient;

    @Autowired
    private RuleService ruleService;

    @Autowired
    private RuleConditionRepository ruleConditionRepository;

    @Autowired
    private ProgramAttributeServiceTypeRepository programAttributeServiceTypeRepository;

    @Override
    public Object createSchedule(CreateScheduleReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        req.setBusinessId(business.getId());
        programService.findByIdAndBusinessId(req.getProgramId(), business.getId());

        validateCreateSchedule(req);

        if (ESchedulePeriod.DAILY.equals(req.getPeriodType())){
            req.setRunDate(null);
        }

        req.setRequestType(ERequestType.CREATE);
            return makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.CALENDAR_EVENT,
                UUID.randomUUID().toString(), req, req.getMadeReason(), req.isSendEmail());
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> response = makerCheckerInternalFeignClient
                .previewDetail(EMakerCheckerType.CALENDAR_EVENT.getType(), req.getId());
        validatePendingRequest(response, req);
        CreateScheduleReq payload = objectMapper.convertValue(response.getData().getPayload(), CreateScheduleReq.class);
        ResetRuleReq resetRuleReq = null;
        String createdBy = null;
        String updatedBy = null;
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            programService.findActive(payload.getProgramId());
            businessService.findActive(payload.getBusinessId());
            String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(response.getData().getMadeByUserName());
            Schedule schedule;
            if (payload.getId() != null){
                schedule = scheduleService.findById(payload.getId());
                if (schedule == null) {
                    throw new BusinessException(ErrorCode.SCHEDULE_NOT_FOUND);
                }
                if (!payload.getStatus().equals(schedule.getStatus())) {
                    UpdateScheduleStatus updateScheduleStatus = new UpdateScheduleStatus();
                    updateScheduleStatus.setPaused(payload.getStatus().equals(ECommonStatus.INACTIVE));
                    jobManagementFeignClient.updateScheduleStatus(schedule.getCode(), updateScheduleStatus);
                }
                mappingSchedule(schedule, payload);
                if (payload.getRules() != null) {
                    resetRuleReq = opsRuleService.editRule(schedule, payload.getRules().get(0), response.getData(), approvedBy);
                }
                setScheduleEvent(schedule, payload, response.getData(), approvedBy);
                updatedBy = response.getData().getMadeByUserName();

            } else {
                schedule = new Schedule();
                if (!DateTimes.toDate(payload.getStartDate()).after(response.getData().getMadeDateToDate())) {
                    throw new BusinessException(ErrorCode.INVALID_SCHEDULE_START_DATE);
                }
                mappingSchedule(schedule, payload);
                schedule.setRequestCode(response.getData().getRequestCode());
                if (payload.getRules() != null) {
                    resetRuleReq = opsRuleService.createRule(schedule, payload.getRules().get(0), response.getData(), approvedBy);
                }
                setScheduleEvent(schedule, payload, response.getData(), approvedBy);
                updatedBy = createdBy = response.getData().getMadeByUserName();
            }
            if (!DateTimes.toDate(payload.getEndDate()).after(schedule.getStartDate())) {
                throw new BusinessException(ErrorCode.INVALID_SCHEDULE_END_DATE, null, null);
            }
            schedule.setRequestCode(response.getData().getRequestCode());
            schedule.setVersion(response.getData().getVersion());

            opsReqPendingValidator.updateInfoChecker(schedule, response.getData().getMadeDate(), createdBy, updatedBy, approvedBy);
            Schedule savedSchedule = scheduleRepository.save(schedule);
            jobManagementFeignClient.createSchedule(buildCreateEventReq(savedSchedule));

            if (resetRuleReq != null ) {
                APIFeignInternalResponse<?> ruleRes = oneloyaltyRuleFeignClient.checkReset(resetRuleReqs);
                if (ruleRes.getMeta().getCode() != 200) {
                    throw new BusinessException(ErrorCode.RESET_RULE_FAILED);
                }
            }
        }
        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> approvedPayload = makerCheckerInternalFeignClient
                .checkerDefault(EMakerCheckerType.CALENDAR_EVENT, req, null);
        if (approvedPayload.getMeta().getCode() != HttpStatus.OK.value()) {
            throw new BusinessException(
                    ErrorCode.MAKER_CHECKER_CANNOT_CHANGE_STATUS,
                    "Checker fail",
                    LogData.createLogData()
                            .append("id", req.getId()));

        }
        if (EApprovalStatus.APPROVED == req.getStatus() && CollectionUtils.isNotEmpty(resetRuleReqs)) {
            ResetRuleEvent<?> ruleEvent = ResetRuleEvent.builder()
                    .id(UUID.randomUUID().toString())
                    .eventType(ResetRuleEvent.RESET_RULE_EVENT_TYPE)
                    .timeStamp(System.currentTimeMillis())
                    .payload(resetRuleReqs)
                    .build();
            applicationEventPublisher.publishEvent(ruleEvent);
        }
    }

    @SneakyThrows
    private CreateEventReq buildCreateEventReq(Schedule schedule) {
        Program program = programService.findById(schedule.getProgramId());

        CreateEventReq.EventConfig eventConfig = CreateEventReq.EventConfig.builder()
                .eventDate(schedule.getEventDate() != null ? DateTimes.convertToLocalTimeString(schedule.getEventDate().toString()) : null )
                .eventProduct(schedule.getEventProduct())
                .eventName(schedule.getEventName())
                .eventGroup(schedule.getEventGroup())
                .eventAmount(schedule.getEventAmount())
                .eventType(schedule.getEventType())
                .eventCode(schedule.getEventCode())
                .build();

        List<Rule> lstRule = ruleService.findByProgramIdAndServiceTypeAndServiceCode(schedule.getProgramId(), EServiceType.CALENDAR_EVENT, schedule.getCode());
        Map<String, CreateEventReq.Attribute> attributeMap = lstRule.stream()
                .filter(rule -> ECommonStatus.ACTIVE.equals(rule.getStatus()))
                .flatMap(rule -> ruleConditionRepository.findByRuleId(rule.getId()).stream())
                .filter(ruleCondition -> ECommonStatus.ACTIVE.equals(ruleCondition.getStatus()))
                .collect(Collectors.toMap(
                        RuleCondition::getAttribute,
                        ruleCondition -> CreateEventReq.Attribute.builder()
                                .attribute(ruleCondition.getAttribute())
                                .operator(ruleCondition.getOperator())
                                .value(ruleCondition.getValue())
                                .build()
                ));
        Map<String, List<CreateEventReq.Attribute>> groupedAttributes = programAttributeServiceTypeRepository
                .findByProgramIdAndServiceType(schedule.getProgramId(), EServiceType.CALENDAR_EVENT)
                .stream()
                .filter(programAttributeServiceType -> attributeMap.containsKey(programAttributeServiceType.getAttribute()))
                .collect(Collectors.groupingBy(
                        ProgramAttributeServiceType::getGroupName,
                        Collectors.mapping(
                                programAttributeServiceType -> attributeMap.get(programAttributeServiceType.getAttribute()),
                                Collectors.toList()
                        )
                ));
        List<CreateEventReq.Rule> rules = groupedAttributes.entrySet().stream()
                .map(entry -> CreateEventReq.Rule.builder()
                        .groupAttribute(entry.getKey())
                        .attributes(entry.getValue())
                        .build()
                )
                .collect(Collectors.toList());

        CreateEventReq.SchedulePayload schedulePayload = CreateEventReq.SchedulePayload.builder()
                .rules(rules)
                .programCode(program.getCode())
                .scheduleCode(schedule.getCode())
                .scheduleId(schedule.getId())
                .scheduleType(schedule.getScheduleType().getValue())
                .schedulePeriod(schedule.getSchedulePeriod().getValue())
                .startDate(schedule.getStartDate().after(DateTimes.currentTime()) ? DateTimes.convertToLocalTimeString(schedule.getStartDate().toString()) : null)
                .endDate(DateTimes.convertToLocalTimeString(schedule.getEndDate().toString()))
                .executionTime(ESchedulePeriod.ONCE.equals(schedule.getSchedulePeriod()) ?
                        DateTimes.convertTimeWithMicroSecond(
                                String.format("%s %s", schedule.getRunDate(), schedule.getRunTime())) : null)
                .cron(ESchedulePeriod.DAILY.equals(schedule.getSchedulePeriod())
                        ? buildCronExpression(schedule.getRunTime(), schedule.getRunDate()) : null)
                .eventDetails(eventConfig)
                .isPaused(ECommonStatus.ACTIVE.equals(schedule.getStatus()) ? "False" : "True")
                .build();

        CreateEventReq req = new CreateEventReq();
        req.setExecutionDate(DateTimes.convertTimeWithMicroSecond(null));
        req.setConf(schedulePayload);
        return req;
    }

    private String buildCronExpression(String runTime, String runDate){
        String[] runTimeArray = Arrays.copyOf(runTime.split(":"), 2);
        ArrayUtils.reverse(runTimeArray);
        String runTimeCron = String.join(" ", runTimeArray);

        String runDateCron;
        String dayOfWeek = null;
        if (runDate != null){
            String[] runDateArray = Arrays.copyOf(runDate.split("/"), 2);
            runDateCron = String.join(" ", runDateArray);
            dayOfWeek = DateTimes.getDayOfWeek(runDate);
        } else {
            runDateCron = "* *";
        }
        return String.format("%s %s %s", runTimeCron, runDateCron, dayOfWeek != null ? dayOfWeek : "*");
    }

    @SneakyThrows
    private void setScheduleEvent(Schedule schedule, CreateScheduleReq createScheduleReq, MakerCheckerInternalDataDetailRes data, String approveBy){
        Class<?> eventDetailReqClass = createScheduleReq.getClass();
        for (EScheduleEventAttribute eventAttribute : EScheduleEventAttribute.values()){
            List<String> values = opsAttributeService.getMasterData(
                            schedule.getProgramId(),
                            eventAttribute.getValue(),
                            EAttributeType.PROGRAM_TRANSACTION).stream()
                    .map(AttributeCombobox::getValue).collect(Collectors.toList());

            Field attribute = eventDetailReqClass.getDeclaredField(eventAttribute.getFieldName());
            attribute.setAccessible(true);
            if (!values.contains(attribute.get(createScheduleReq))) {
                throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID,
                        String.format("Invalid %s value: %s", eventAttribute.getValue(), attribute.get(createScheduleReq)),
                        null);
            }
        }
        schedule.setEventCode(createScheduleReq.getEventCode());
        schedule.setEventName(createScheduleReq.getEventName());
        schedule.setEventGroup(createScheduleReq.getEventGroup());
        schedule.setEventType(createScheduleReq.getEventType());
        schedule.setEventAmount(createScheduleReq.getEventAmount());
        schedule.setEventProduct(createScheduleReq.getEventProduct());
        schedule.setEventDate(DateTimes.toDate(createScheduleReq.getEventDate()));
    }

    @Override
    public void cancel(CancelReq cancelReq) {
        String createdBy = opsReqPendingValidator.getCurrentUser();
        MakerCheckerInternalDataDetailRes detailRes = makerCheckerInternalFeignClient
                .previewDetailDefault(EMakerCheckerType.CALENDAR_EVENT, String.valueOf(cancelReq.getId()));
        if (!createdBy.equals(detailRes.getMadeByUserName())){
            throw new OpsBusinessException(OpsErrorCode.ACCESS_DENIED, "Access denied", cancelReq);
        }
        if (!EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue().equals(detailRes.getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to cancel",
                    LogData.createLogData().append("id", String.valueOf(cancelReq.getId())));
        }
        makerCheckerInternalFeignClient.cancelDefault(EMakerCheckerType.CALENDAR_EVENT, cancelReq);
    }

    @Override
    public Page<AvailableScheduleRes> getAvailableSchedule(
            Integer programId,
            String scheduleCode,
            String scheduleName,
            EScheduleType scheduleType,
            ECommonStatus scheduleStatus,
            Long startDateReq,
            Long endDateReq,
            Integer offset,
            Integer limit) {

        Sort sort = Sort.by(Sort.Direction.DESC, ESchemeSortingField.CREATED_AT.getMappingColumn());
        Pageable pageRequest = new com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest(offset, limit, sort);

        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        SpecificationBuilder specification = new SpecificationBuilder();

        specification.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));

        if (scheduleStatus != null)
            specification.add(new SearchCriteria("status", scheduleStatus, SearchOperation.EQUAL));

        if (programId != null)
            specification.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        if (scheduleCode != null){
            String formattedCode = scheduleCode.replace("_", "\\_").replace("%", "\\%");
            specification.add(new SearchCriteria("code", formattedCode, SearchOperation.EQUAL));
        }

        if (scheduleName != null){
            String formattedName = scheduleName.replace("_", "\\_").replace("%", "\\%");
            specification.add(new SearchCriteria("name", formattedName, SearchOperation.MATCH));
        }


        if (scheduleType != null) {
            specification.add(new SearchCriteria("scheduleType", scheduleType, SearchOperation.EQUAL));
        }

        if (startDateReq != null){
            specification.add(new SearchCriteria("startDate", DateTimes.toDate(startDateReq), SearchOperation.GREATER_THAN_EQUAL_DATE));
        }

        if (endDateReq != null){
            specification.add(new SearchCriteria("endDate", DateTimes.toDate(endDateReq), SearchOperation.LESS_THAN_EQUAL_DATE));
        }
        Page<Schedule> page = scheduleService.findAll(specification, pageRequest);
        List<AvailableScheduleRes> resList = new ArrayList<>();
        for (Schedule schedule : page.getContent()){
            Program program = programService.findById(schedule.getProgramId());

            AvailableScheduleRes availableScheduleRes = new AvailableScheduleRes();
            availableScheduleRes.setId(schedule.getId());
            availableScheduleRes.setName(schedule.getName());
            availableScheduleRes.setCode(schedule.getCode());
            availableScheduleRes.setScheduleType(schedule.getScheduleType());
            availableScheduleRes.setSchedulePeriod(schedule.getSchedulePeriod());
            availableScheduleRes.setStatus(schedule.getStatus());
            availableScheduleRes.setStartDate(schedule.getStartDate());
            availableScheduleRes.setEndDate(schedule.getEndDate());

            availableScheduleRes.setProgram(new ShortEntityRes(program.getId(), program.getName(), program.getCode()));
            resList.add(availableScheduleRes);
        }
        return new PageImpl<AvailableScheduleRes>(resList, pageRequest, page.getTotalElements());
    }

    @Override
    public ScheduleDetailRes getAvailableScheduleDetails(Integer scheduleId) {
        Schedule existSchedule = scheduleService.findById(scheduleId);
        if (existSchedule == null) {
            throw new BusinessException(ErrorCode.SCHEDULE_NOT_FOUND);
        }
        ScheduleDetailRes res = buildScheduleDetailRes(existSchedule);
        buildEventDetailRes(res, existSchedule);
        return res;
    }

    @Override
    public Page<ScheduleHistoryRes> getScheduleHistories(
            Integer scheduleId,
            Long startDateReq,
            Long endDateReq,
            Integer offset,
            Integer limit) {

        Sort sort = Sort.by(Sort.Direction.DESC, ESchemeSortingField.CREATED_AT.getMappingColumn());
        Pageable pageRequest = new com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest(offset, limit, sort);

        Schedule existSchedule = scheduleService.findById(scheduleId);
        if (existSchedule == null) {
            throw new BusinessException(ErrorCode.SCHEDULE_NOT_FOUND);
        }
        SpecificationBuilder specification = new SpecificationBuilder();

        specification.add(new SearchCriteria("scheduleId", scheduleId, SearchOperation.EQUAL));
        if (startDateReq != null){
            specification.add(new SearchCriteria("startDate", DateTimes.toDate(startDateReq), SearchOperation.GREATER_THAN_EQUAL_DATE));
        }

        if (endDateReq != null){
            specification.add(new SearchCriteria("endDate", DateTimes.toDate(endDateReq), SearchOperation.LESS_THAN_EQUAL_DATE));
        }
        Page<ScheduleHistory> page = scheduleHistoryService.findAll(specification, pageRequest);
        List<ScheduleHistoryRes> resList = new ArrayList<>();
        List<ScheduleHistory> sortedList = page.getContent().stream()
                .sorted(Comparator.comparing(ScheduleHistory::getId).reversed())
                .collect(Collectors.toList());
        for(ScheduleHistory scheduleHistory : sortedList){
            ScheduleHistoryRes scheduleHistoryRes = new ScheduleHistoryRes();
            scheduleHistoryRes.setStartDate(DateTimes.formatDateTime(scheduleHistory.getStartDate()));
            scheduleHistoryRes.setEndDate(DateTimes.formatDateTime(scheduleHistory.getEndDate()));
            scheduleHistoryRes.setTotalCustomers(scheduleHistory.getTotalCustomers());
            scheduleHistoryRes.setTotalSuccessEvents(scheduleHistory.getTotalSuccessEvents());
            scheduleHistoryRes.setTotalFailedEvents(scheduleHistory.getTotalFailedEvents());

            resList.add(scheduleHistoryRes);
        }
        return new PageImpl<ScheduleHistoryRes>(resList, pageRequest, page.getTotalElements());
    }

    @SneakyThrows
    private void buildEventDetailRes(ScheduleDetailRes scheduleDetailRes, Schedule schedule) {
        Class<?> existSchedule = schedule.getClass();
        Map<String, ShortEntityRes> eventMapping = new HashMap<>();
        for (EScheduleEventAttribute eventAttribute : EScheduleEventAttribute.values()){
            List<AttributeCombobox> attributeComboboxes = opsAttributeService.getMasterData(
                    schedule.getProgramId(),
                    eventAttribute.getValue(),
                    EAttributeType.PROGRAM_TRANSACTION);

            List<String> values = attributeComboboxes.stream()
                    .map(AttributeCombobox::getValue).collect(Collectors.toList());

            Field savedAttribute = existSchedule.getDeclaredField(eventAttribute.getFieldName());
            savedAttribute.setAccessible(true);
            Object savedAttributeValue = savedAttribute.get(schedule);
            if (!values.contains(savedAttributeValue)) {
                throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID,
                        String.format("Invalid %s value: %s", eventAttribute.getValue(), savedAttribute.get(schedule)),
                        null);
            } else {
                attributeComboboxes.forEach(attributeCombobox -> {
                    if (attributeCombobox.getValue().equals(savedAttributeValue)) {
                        eventMapping.put(eventAttribute.getFieldName(), new ShortEntityRes(null,
                                attributeCombobox.getDescription(), attributeCombobox.getValue()));
                    }
                });
            }
        }
        scheduleDetailRes.setEventCode(eventMapping.get(EScheduleEventAttribute.EVENT_CODE.getFieldName()));
        scheduleDetailRes.setEventName(eventMapping.get(EScheduleEventAttribute.EVENT_NAME.getFieldName()));
        scheduleDetailRes.setEventGroup(eventMapping.get(EScheduleEventAttribute.EVENT_GROUP.getFieldName()));
        scheduleDetailRes.setEventType(eventMapping.get(EScheduleEventAttribute.EVENT_TYPE.getFieldName()));
    }

    private ScheduleDetailRes buildScheduleDetailRes(Schedule schedule) {
        Program program = programService.findById(schedule.getProgramId());

        List<RuleRes> ruleRes = opsRuleService.getRule(schedule.getCode(), program.getId(), EServiceType.CALENDAR_EVENT);

        ShortEntityRes shortEntityRes = new ShortEntityRes();
        shortEntityRes.setId(program.getId());
        shortEntityRes.setName(program.getName());
        shortEntityRes.setCode(program.getCode());

        return ScheduleDetailRes.builder()
                .program(shortEntityRes)
                .status(schedule.getStatus())
                .code(schedule.getCode())
                .name(schedule.getName())
                .startDate(schedule.getStartDate())
                .endDate(schedule.getEndDate())
                .scheduleType(schedule.getScheduleType())
                .periodType(schedule.getSchedulePeriod())
                .runTime(schedule.getRunTime())
                .runDate(schedule.getRunDate())
                .description(schedule.getDescription())
                .eventAmount(schedule.getEventAmount())
                .eventProduct(schedule.getEventProduct())
                .eventDate(schedule.getEventDate())
                .rules(ruleRes)
                .build();

    }

    @SneakyThrows
    @Override
    public ScheduleDetailRes getInReviewScheduleRequestById(String reviewId) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(EMakerCheckerType.CALENDAR_EVENT.getType(), reviewId);
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Schedule - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.CALENDAR_EVENT.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Schedule - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }

        CreateScheduleReq payload = this.jsonMapper.convertValue(previewDetailRes.getData().getPayload(), CreateScheduleReq.class);

        Program program = programService.find(payload.getProgramId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
        );

        Business business = businessService.find(payload.getBusinessId()).orElseThrow(
                () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND)
        );

        Class<?> existSchedule = payload.getClass();
        Map<String, ShortEntityRes> eventMapping = new HashMap<>();
        for (EScheduleEventAttribute eventAttribute : EScheduleEventAttribute.values()){
            List<AttributeCombobox> attributeComboboxes = opsAttributeService.getMasterData(
                    program.getId(),
                    eventAttribute.getValue(),
                    EAttributeType.PROGRAM_TRANSACTION);

            List<String> values = attributeComboboxes.stream()
                    .map(AttributeCombobox::getValue).collect(Collectors.toList());

            Field savedAttribute = existSchedule.getDeclaredField(eventAttribute.getFieldName());
            savedAttribute.setAccessible(true);
            Object savedAttributeValue = savedAttribute.get(payload);
            if (!values.contains(savedAttributeValue)) {
                throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID,
                        String.format("Invalid %s value: %s", eventAttribute.getValue(), savedAttributeValue),
                        null);
            } else {
                attributeComboboxes.forEach(attributeCombobox -> {
                    if (attributeCombobox.getValue().equals(savedAttributeValue)) {
                        eventMapping.put(eventAttribute.getFieldName(), new ShortEntityRes(null,
                                attributeCombobox.getDescription(), attributeCombobox.getValue()));
                    }
                });
            }
        }
        List<RuleRes> ruleInReview = new ArrayList<>();

        if (!EScheduleType.BIRTHDAY.equals(payload.getType())) {
            ruleInReview = opsRuleService.getRuleInReview(payload.getProgramId(), payload.getRules(), payload.getCode());
        }

        return ScheduleDetailRes.builder()
                .requestId(payload.getId())
                .businessId(business.getId())
                .businessName(business.getName())
                .businessCode(business.getCode())
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .requestType(payload.getRequestType())
                .code(payload.getCode())
                .name(payload.getName())
                .type(payload.getType())
                .periodType(payload.getPeriodType())
                .runTime(payload.getRunTime())
                .runDate(payload.getRunDate())
                .description(payload.getDescription())
                .eventAmount(payload.getEventAmount())
                .eventProduct(payload.getEventProduct())
                .eventDate(DateTimes.toDate(payload.getEventDate()))
                .eventCode(eventMapping.get(EScheduleEventAttribute.EVENT_CODE.getFieldName()))
                .eventName(eventMapping.get(EScheduleEventAttribute.EVENT_NAME.getFieldName()))
                .eventType(eventMapping.get(EScheduleEventAttribute.EVENT_TYPE.getFieldName()))
                .eventGroup(eventMapping.get(EScheduleEventAttribute.EVENT_GROUP.getFieldName()))
                .rules(ruleInReview)
                .pushEvent(payload.getPushEvent())
                .approvalStatus(EApprovalStatus.valueOf(previewDetailRes.getData().getStatus()))
                .status(payload.getStatus())
                .createdBy(previewDetailRes.getData().getMadeByUserName())
                .createdAt(Objects.nonNull(previewDetailRes.getData().getMadeDate())
                        ? Date.from(ZonedDateTime.parse(previewDetailRes.getData().getMadeDate()).toInstant()) : null)
                .approvedBy(previewDetailRes.getData().getCheckedByUserName())
                .approvedAt(previewDetailRes.getData().getCheckedDateToDate())
                .startDate(DateTimes.toDate(payload.getStartDate()))
                .endDate(DateTimes.toDate(payload.getEndDate()))
                .requestType(payload.getRequestType())
                .madeReason(payload.getMadeReason())
                .reason(previewDetailRes.getData().getComment())
                .build();
    }

    private void mappingSchedule(Schedule schedule, CreateScheduleReq payload) {
        schedule.setId(payload.getId());
        schedule.setBusinessId(payload.getBusinessId());
        schedule.setProgramId(payload.getProgramId());
        schedule.setCode(payload.getCode());
        schedule.setName(payload.getName());
        schedule.setDescription(payload.getDescription());
        schedule.setScheduleType(payload.getType());
        schedule.setSchedulePeriod(payload.getPeriodType());
        schedule.setRunTime(payload.getRunTime());
        schedule.setRunDate(payload.getRunDate());
        schedule.setStatus(payload.getStatus());
        schedule.setStartDate(DateTimes.toDate(payload.getStartDate()));
        schedule.setEndDate(DateTimes.toDate(payload.getEndDate()));
    }

    private void validatePendingRequest(APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> response, ApprovalReq req) {
        if (!EMakerCheckerStatus.lookup(EApprovalStatus.PENDING).getValue().equals(response.getData().getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to reject or approve",
                    LogData.createLogData()
                            .append("id", req.getId())
                            .append("approve_status", req.getStatus()));
        }
        if (response.getMeta().getCode() != HttpStatus.OK.value()) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Checker id not found",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
    }

    private void validateRuleCondition(CreateScheduleReq req) {
        if (EScheduleType.SELF_TRIGGER.equals(req.getType()) &&
            req.getRules() == null){
            throw new BusinessException(ErrorCode.RULE_CONDITION_MUST_NOT_BE_NULL);
        }
    }

    private void validateScheduleDate(CreateScheduleReq req){
        // Validate Start Date & End Date
        validateStartDateAndEndDate(req);

        // Validate Run Date
        validateRunDate(req.getPeriodType(), req.getRunDate(), req.getRunTime(), req.getStartDate(), req.getEndDate());

        // Validate Event Date
        validateEventDate(req);
    }

    private void validateEventDate(CreateScheduleReq req){
        if (ESchedulePeriod.ONCE.equals(req.getPeriodType()) && req.getEventDate() == null){
            throw new BusinessException(ErrorCode.INVALID_EVENT_DATE,
                    "Event date must not be null",
                    null);
        }
    }

    private void validateRunDate(ESchedulePeriod period, String runDate, String runTime, Long startDate, Long endDate) {
        if (
                ESchedulePeriod.ONCE.equals(period) &&
                        runDate == null
        ){
            throw new BusinessException(ErrorCode.RUN_DATE_MUST_NOT_BE_NULL);
        }
        if (ESchedulePeriod.ONCE.equals(period)){
            long runDateSecond = DateTimes.parseSecond("dd/MM/yyyy HH:mm:ss", String.format("%s %s", runDate, runTime));
            if ( runDateSecond < startDate || runDateSecond > endDate ){
                throw new BusinessException(ErrorCode.INVALID_RUN_DATE);
            }
        }
    }

    private void validateExistScheduleCode(CreateScheduleReq req) {
        Integer programId = req.getProgramId();
        String code = req.getCode();
        List<Schedule> existSchedules = scheduleService.findByProgramIdAndCode(programId, code);
        if (!existSchedules.isEmpty()) {
            throw new BusinessException(ErrorCode.SCHEDULE_CODE_ALREADY_EXIST);
        }
    }

    private void validatePendingScheduleCode(CreateScheduleReq req) {
        String code = req.getCode();
        MakerCheckerInternalPreviewReq filter = MakerCheckerInternalPreviewReq.builder()
                .status(EMakerCheckerStatus.PENDING.getValue())
                .properties(new HashMap<>())
                .build();
        filter.getProperties().put("payload.code", code);
        MakerCheckerInternalSearchReq searchReq = MakerCheckerInternalSearchReq.builder()
                .filter(filter)
                .build();
        List<MakerCheckerInternalDataDetailRes> res = makerCheckerInternalFeignClient.search(
                EMakerCheckerType.CALENDAR_EVENT.getType(), searchReq).getData();
        if (CollectionUtils.isNotEmpty(res)) {
            throw new BusinessException(ErrorCode.SCHEDULE_CODE_ALREADY_EXIST,
                    "[VALIDATION SCHEDULE CODE] schedule code existed in other requests pending", code, new Object[]{code});
        }
    }

    private void validateStartDateAndEndDate(CreateScheduleReq req) {
        // Validate Back Date

        if (req.getStartDate() <= DateTimes.currentTimeSeconds())
            throw new BusinessException(
                    ErrorCode.INVALID_SCHEDULE_START_DATE);

        if (req.getEndDate() <= req.getStartDate())
            throw new BusinessException(
                    ErrorCode.START_DATE_IS_LESS_END_DATE);
    }

    private void validateCreateSchedule(CreateScheduleReq req) {
        // Validate Rule - Condition
        validateRuleCondition(req);

        // Validate Code
        validateExistScheduleCode(req);
        validatePendingScheduleCode(req);

        // Validate Schedule Date
        validateScheduleDate(req);
    }

    public Page<ScheduleDetailRes> getInReviewScheduleRequest(Integer programId,
                                                              String code,
                                                              String name,
                                                              EScheduleType type,
                                                              ERequestType requestType,
                                                              String status,
                                                              Long fromStartDate,
                                                              Long toStartDate,
                                                              Integer offset,
                                                              Integer limit,
                                                              MakerCheckerInternalPreviewReq req) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Map<String, Object> searchPayload = (new MongoSearchUtil())
                .setFilter("payload.program_id", programId, false, false, false)
                .setFilter("payload.code", code, false, false, false)
                .setFilter("payload.name", name, false, true, false,
                        MongoSearchUtil.OPTION_IGNORE_CASE_INSENSITIVITY)
                .setFilter("payload.type", type, false, false, false)
                .setFilter("payload.request_type", requestType, false, false, false)
                .setFilter("payload.status", status, false, false, false)
                .setFilter("payload.start_date", Objects.nonNull(fromStartDate) && Objects.nonNull(toStartDate)
                        ? MakerCheckerInternalPreviewReq.RangeDateReq.builder().fromDate(fromStartDate)
                        .toDate(toStartDate).build() : null, false, false, false)
                .build();
        req.setBusinessId(business.getId());
        req.setProperties(searchPayload);
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(EMakerCheckerType.CALENDAR_EVENT, req, offset, limit, null);
        List<ScheduleDetailRes> res = previewRes.getData().stream()
                .map(this::convertPreview)
                .collect(Collectors.toList());

        return new PageImpl<>(res, new OffsetBasedPageRequest(offset, limit), previewRes.getMeta().getTotal());
    }

    private ScheduleDetailRes convertPreview(MakerCheckerInternalDataDetailRes data) {
        CreateScheduleReq createScheduleReq = this.jsonMapper.convertValue(data.getPayload(), CreateScheduleReq.class);
        Program program = programService.find(createScheduleReq.getProgramId()).orElse(null);

        return ScheduleDetailRes
                .builder()
                .id(data.getId())
                .program(Objects.nonNull(program) ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .code(createScheduleReq.getCode())
                .name(createScheduleReq.getName())
                .type(createScheduleReq.getType())
                .periodType(createScheduleReq.getPeriodType())
                .runTime(createScheduleReq.getRunTime())
                .runDate(createScheduleReq.getRunDate())
                .description(createScheduleReq.getDescription())
                .pushEvent(createScheduleReq.getPushEvent())
                .approvalStatus(EApprovalStatus.valueOf(data.getStatus()))
                .status(createScheduleReq.getStatus())
                .createdBy(data.getMadeByUserName())
                .createdAt(data.getMadeDateToDate())
                .approvedBy(data.getCheckedByUserName())
                .approvedAt(data.getCheckedDateToDate())
                .startDate(DateTimes.toDate(createScheduleReq.getStartDate()))
                .endDate(DateTimes.toDate(createScheduleReq.getEndDate()))
                .requestType(createScheduleReq.getRequestType())
                .madeReason(createScheduleReq.getMadeReason())
                .sendEmail(createScheduleReq.isSendEmail())
                .build();
    }

    @Override
    public EditScheduleRes getEditDetail(Integer requestId) {
        Business business = businessService.findActiveByCode(auditorAwareUtil.getBusinessCode());
        Schedule schedule = getScheduleOrThrow(requestId);
        Program program = programService.findByIdAndBusinessId(schedule.getProgramId(), business.getId());
        List<RuleRes> rules = opsRuleService.getRule(schedule.getCode(), program.getId(), EServiceType.CALENDAR_EVENT);
        
        validatePendingRequest(schedule);

        return buildEditScheduleResponse(schedule, program, rules);
    }

    @Override
    public MakerCheckerInternalMakerRes edit(Integer requestId, EditScheduleReq req) {
        Schedule schedule = getScheduleOrThrow(requestId);
        validateRequest(req, schedule);

        CreateScheduleReq payload = mapToCreateScheduleReq(requestId, req, schedule);
        return submitScheduleEdit(schedule, payload);
    }
    //region edit's helpers
    private void validatePendingRequest(Schedule schedule) {
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.CALENDAR_EVENT.getType(), schedule.getRequestCode());
    }

    private EditScheduleRes buildEditScheduleResponse(Schedule schedule, Program program, List<RuleRes> rules) {
        String editKey = opsReqPendingValidator.generateEditKey(schedule.getRequestCode(), schedule.getVersion());

        return EditScheduleRes.builder()
                .requestId(schedule.getId())
                .status(schedule.getStatus())
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .code(schedule.getCode())
                .name(schedule.getName())
                .startDate(schedule.getStartDate())
                .endDate(schedule.getEndDate())
                .type(schedule.getScheduleType())
                .periodType(schedule.getSchedulePeriod())
                .runDate(schedule.getRunDate())
                .runTime(schedule.getRunTime())
                .description(schedule.getDescription())
                .eventCode(schedule.getEventCode())
                .eventName(schedule.getEventName())
                .eventGroup(schedule.getEventGroup())
                .eventType(schedule.getEventType())
                .eventAmount(schedule.getEventAmount())
                .eventProduct(schedule.getEventProduct())
                .eventDate(schedule.getEventDate())
                .rules(rules)
                .editKey(editKey)
                .build();
    }

    private Schedule getScheduleOrThrow(Integer requestId) {
        Schedule schedule = scheduleService.findById(requestId);
        if (schedule == null) {
            throw new BusinessException(ErrorCode.SCHEDULE_NOT_FOUND, null, null);
        }
        return schedule;
    }

    private void validateRequest(EditScheduleReq req, Schedule schedule) {
        opsReqPendingValidator.verifyEditKey(req.getEditKey(), schedule.getRequestCode(), schedule.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.CALENDAR_EVENT.getType(), schedule.getRequestCode());
        // Validate run date & run time
//        validateRunDate(schedule.getSchedulePeriod(), schedule.getRunDate(), req.getRunTime(),
//                DateTimes.toEpochSecond(schedule.getStartDate()), DateTimes.toEpochSecond(schedule.getEndDate()));
    }

    private CreateScheduleReq mapToCreateScheduleReq(Integer requestId, EditScheduleReq req, Schedule schedule) {
        CreateScheduleReq payload = new CreateScheduleReq();
        payload.setId(requestId);
        payload.setBusinessId(schedule.getBusinessId());
        payload.setProgramId(schedule.getProgramId());
        payload.setStatus(req.getStatus());
        payload.setCode(schedule.getCode());
        payload.setName(req.getName());
        payload.setStartDate(DateTimes.toEpochSecond(schedule.getStartDate()));
        payload.setEndDate(DateTimes.toEpochSecond(schedule.getEndDate()));
        payload.setType(schedule.getScheduleType());
        payload.setPeriodType(schedule.getSchedulePeriod());
        payload.setRunDate(req.getRunDate());
        payload.setRunTime(req.getRunTime());
        payload.setEventCode(req.getEventCode());
        payload.setEventName(req.getEventName());
        payload.setEventGroup(req.getEventGroup());
        payload.setEventType(req.getEventType());
        payload.setEventProduct(req.getEventProduct());
        payload.setEventAmount(req.getEventAmount());
        payload.setEventDate(req.getEventDate());
        payload.setRules(req.getRules());
        payload.setRequestType(ERequestType.EDIT);
        payload.setMadeReason(req.getMadeReason());
        payload.setSendEmail(req.isSendEmail());
        payload.setDescription(req.getDescription());
        return payload;
    }

    private MakerCheckerInternalMakerRes submitScheduleEdit(Schedule schedule, CreateScheduleReq payload) {
        String requestCode = Objects.nonNull(schedule.getRequestCode()) ? schedule.getRequestCode() : UUID.randomUUID().toString();
        return makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.CALENDAR_EVENT, requestCode, payload,
                payload.getMadeReason(), payload.isSendEmail());
    }
    //endregion
}
