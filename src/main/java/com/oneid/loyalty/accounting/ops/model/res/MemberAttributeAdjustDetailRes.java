package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERequestProcessStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@Setter
@Builder
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class MemberAttributeAdjustDetailRes {

    private String reason;

    private EBatchRequestProcessStatus processStatus;

    private ShortEntityRes program;

    private String description;

    private EBoolean isMultipleAttribute;

    private Integer totalRequests;

    private Integer failedRequests;

    private Integer successRequests;

    private String code;

    private String name;

    private String createdBy;

    private Date createdAt;

    private String approvedBy;

    private Date approvedAt;

    private EApprovalStatus approvalStatus;

    private String rejectReason;

    private Date completeAt;

    private String loyaltyCusId;

    private String memberAttribute;

    private String attributeValue;

    private Date startDate;

    private Date endDate;

    private ECommonStatus status;

    private ERequestProcessStatus attributeProcessStatus;

    private String errorMessage;
}