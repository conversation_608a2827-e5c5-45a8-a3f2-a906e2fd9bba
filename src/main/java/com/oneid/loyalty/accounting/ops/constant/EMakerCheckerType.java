package com.oneid.loyalty.accounting.ops.constant;

import java.util.HashMap;
import java.util.Map;

public enum EMakerCheckerType {
    PROGRAM("Program", "Program", "Program"),
    PROGRAM_FUNCTION("ProgramFunction", "ProgramFunction", "ProgramFunction"),
    PROGRAM_CORPORATION("ProgramCorporation", "ProgramCorporation", "ProgramCorporation"),
    PROGRAM_LEVEL("ProgramLevel", "ProgramLevel", "ProgramLevel"),
    COUNTER("Counter", "Counter", "Counter"),
    GIFT_CARD_PRODUCTION_REQUEST("GiftCardProductionRequest", "GiftCardProductionRequest", "GiftCardProductionRequest"),
    GIFT_CARD_TRANSFER("GiftCardTransfer", "GiftCardTransfer", "GiftCardTransfer"),
    PROGRAM_TIER_POLICY("ProgramTierPolicy", "ProgramTierPolicy", "ProgramTierPolicy"),
    PROGRAM_TIER("ProgramTier", "ProgramTier", "ProgramTier"),
    LIMITATION("Limitation", "Limitation", "Limitation"),
    MEMBER_ATTRIBUTE("MemberAttribute", "MemberAttribute", "MemberAttribute"),
    PROGRAM_TRANSACTION_ATTRIBUTE("ProgramTransactionAttribute", "ProgramTransactionAttribute", "ProgramTransactionAttribute"),
    POOL("Pool", "Pool", "Pool"),
    SCHEME("Scheme", "Scheme", "Scheme"),
    CURRENCY("Currency", "Currency", "Currency"),
    CURRENCY_RATE("CurrencyRate", "CurrencyRate", "CurrencyRate"),
    CAMPAIGN("Campaign", "Campaign", "Campaign"),
    TRANSACTION_REQUEST("TransactionRequest", "TransactionRequest", "TransactionRequest"),
    TRANSACTION_CODE("TransactionCode", "TransactionCode", "TransactionCode"),
    POINT_EXCHANGE("PointExchange", "PointExchange", "PointExchange"),
    CALENDAR_EVENT("CalendarEvent", "CalendarEvent", "CalendarEvent"),
    MEMBER_STATUS("MemberStatus", "MemberStatus", "MemberStatus"),
    MEMBER_ATTRIBUTE_ADJUST("MemberAttributeAdjust", "MemberAttributeAdjust", "MemberAttributeAdjust");

    private final String code;

    private final String name;

    private final String type;

    private static final Map<String, EMakerCheckerType> mapByValue = new HashMap();

    EMakerCheckerType(String code, String name, String type) {
        this.code = code;
        this.name = name;
        this.type = type;
    }

    static {
        EMakerCheckerType[] var0 = values();
        int var1 = var0.length;

        for (int var2 = 0; var2 < var1; ++var2) {
            EMakerCheckerType e = var0[var2];
            mapByValue.put(e.getType(), e);
        }
    }

    public static EMakerCheckerType of(String type) {
        return mapByValue.get(type);
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getType() {
        return this.type;
    }
}