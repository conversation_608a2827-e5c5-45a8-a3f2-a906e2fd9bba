package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EDataFileType;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.poi.ss.formula.functions.EDate;

import java.util.Date;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class EarningFileProcessRes {

    public String name;

    public EFileType fileType;

    public EDataFileType dataFileType;

    public Integer totalFiles;

    public EProcessingStatus status;

    public Date createAt;

    public Date updatedAt;

    public String errorMessage;

    public Long id;

    public Integer totalRecord;

    public Integer totalFailedRecord;

    public Date getUpdatedAt() {
        if (EProcessingStatus.SUCCESS.equals(status) || EProcessingStatus.FAILED.equals(status)) {
            return updatedAt;
        }
        return null;
    }

}
