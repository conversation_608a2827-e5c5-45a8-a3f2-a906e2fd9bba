package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.BudgetEditReq;
import com.oneid.loyalty.accounting.ops.model.req.BudgetReq;
import com.oneid.loyalty.accounting.ops.model.req.CancelReq;
import com.oneid.loyalty.accounting.ops.model.res.BudgetInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.BudgetRes;
import com.oneid.loyalty.accounting.ops.service.OpsBudgetService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerInReviewInput;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EPoolType;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("v1/budget")
@Validated
public class BudgetController extends BaseController {

    @Autowired
    OpsBudgetService opsBudgetService;

    @PostMapping("/create")
    @Authorize(role = AccessRole.BUDGET, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> create(@RequestBody @Valid BudgetReq req) {
        return success(opsBudgetService.create(req));
    }

    @GetMapping("/in-review")
    @Authorize(role = AccessRole.BUDGET, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getListInReview(@RequestParam(name = "program_id") Integer programId,
                                             @RequestParam(name = "code", required = false) String code,
                                             @RequestParam(name = "name", required = false) String name,
                                             @RequestParam(name = "request_type", required = false) ERequestType requestType,
                                             @RequestParam(value = "start_date", required = false) Long startDate,
                                             @RequestParam(value = "end_date", required = false) Long endDate,
                                             @RequestParam(value = "status", required = false) String status,
                                             @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
                                             @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit,
                                             @MakerCheckerInReviewInput MakerCheckerInternalPreviewReq req) {

        Page<BudgetInReviewRes> page = opsBudgetService
                .getInReview(programId, code, name, requestType, startDate, endDate, status, offset, limit, req);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/in-review/{id}")
    @Authorize(role = AccessRole.BUDGET, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReview(@PathVariable("id") String reviewId) {
        return success(opsBudgetService.getDetailInReview(reviewId));
    }

    @PostMapping("/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.BUDGET, permissions = {AccessPermission.APPROVE_OR_REJECT})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        opsBudgetService.approve(req);
        return success(null);
    }

    @PostMapping("/{id}/cancel")
    @Authorize(role = AccessRole.BUDGET, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> cancelRequest(@PathVariable("id") String id,
                                           @RequestBody CancelReq req) {
        req.setId(id);
        opsBudgetService.cancelInReview(req);
        return success(null);
    }

    @GetMapping("/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.BUDGET, permissions = {AccessPermission.EDIT}),
    })
    public ResponseEntity<?> getChangeableByRequestId(@PathVariable("id") Integer requestId) {
        return success(opsBudgetService.getChangeableByRequestId(requestId));
    }

    @PostMapping("/{request_id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.BUDGET, permissions = {AccessPermission.EDIT}),
    })
    public ResponseEntity<?> edit(
            @PathVariable("request_id") Integer requestId,
            @Valid @RequestBody BudgetEditReq req) {
        return success(opsBudgetService.update(requestId, req));
    }

    @GetMapping("/available")
    @Authorize(role = AccessRole.BUDGET, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableCampaignRequest(
            @RequestParam(value = "program_id") Integer programId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "start_date", required = false) Long startDate,
            @RequestParam(value = "end_date", required = false) Long endDate,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit) {
        Page<BudgetRes> page = opsBudgetService.getAvailableBudgets(programId, code, name, status, startDate,
                endDate, new OffsetBasedPageRequest(offset, limit, Sort.by(Sort.Direction.DESC, "id")));

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/available/{id}")
    @Authorize(role = AccessRole.BUDGET, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableById(@PathVariable("id") Integer id) {
        return success(opsBudgetService.getAvailableById(id));
    }

    @GetMapping("/schemes")
    public ResponseEntity<?> getSchemes(
            @RequestParam(value = "program_id") Integer programId,
            @RequestParam(value = "type") ESchemeType type,
            @RequestParam(value = "pool_type") EPoolType poolType
    ) {
        return success(opsBudgetService.getSchemes(programId, type, poolType));
    }
}