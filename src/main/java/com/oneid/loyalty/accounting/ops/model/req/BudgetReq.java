package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.validation.OpsCode;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.ESubBudgetPeriod;
import com.oneid.oneloyalty.common.entity.Budget;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import javax.persistence.Convert;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
@AllArgsConstructor
public class BudgetReq implements Serializable {

    private static final long serialVersionUID = 2813661866947854920L;

    private Integer id;

    private Integer businessId;

    @NotNull(message = "Program id cannot be null")
    private Integer programId;

    @NotBlank(message = "Code cannot be blank")
    @OpsCode
    @Size(max = 32, message = "code cannot exceed {max} characters")
    private String code;

    @NotBlank(message = "Name cannot be blank")
    @Size(max = 100, message = "name cannot exceed {max} characters")
    private String name;

    @Size(max = 255, message = "Description cannot exceed {max} characters")
    private String description;

    @NotBlank(message = "Status cannot be blank")
    @Convert(converter = ECommonStatus.Converter.class)
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    private String status;

    @NotNull(message = "'start_date' must not be null")
    private Long startDate;

    @NotNull(message = "'end_date' must not be null")
    private Long endDate;

    @Positive(message = "amount must be greater than 0")
    private BigDecimal amount;

    @NotBlank(message = "'allow_remaining_value' must not blank")
    @Pattern(regexp = "^(Y|N)?$", message = "'allow_remaining_value' Only accept Y/N values")
    private String allowRemainingValue;

    @NotNull(message = "'scheme' must not be null")
    private List<Integer> schemeIds;

    @Size(max = 2, message = "subBudgets must not have more than 2")
    private List<SubBudget> subBudgets;

    private boolean sendEmail;

    private String madeReason;

    private ERequestType requestType;

    public static BudgetReq mapperToCreate(Integer requestId, BudgetEditReq req, Budget budget, List<com.oneid.oneloyalty.common.entity.SubBudget> subBudgets) {
        BudgetReq payload = new BudgetReq();
        payload.setId(requestId);
        payload.setName(req.getName());
        payload.setEndDate(req.getEndDate());
        payload.setStatus(req.getStatus());
        payload.setDescription(req.getDescription());
        payload.setSchemeIds(req.getSchemeIds());
        payload.setCode(budget.getCode());
        payload.setBusinessId(budget.getBusinessId());
        payload.setProgramId(budget.getProgramId());
        payload.setStartDate(DateTimes.toEpochSecond(budget.getStartDate()));
        payload.setAmount(budget.getAmount());
        payload.setAllowRemainingValue(budget.getAllowRemainingValue().getValue());
        payload.setSubBudgets(CollectionUtils.isNotEmpty(subBudgets) ? subBudgets.parallelStream().map(BudgetReq.SubBudget::from).collect(Collectors.toList()) : new ArrayList<>());
        payload.setRequestType(ERequestType.EDIT);
        payload.setMadeReason(req.getMadeReason());
        payload.setSendEmail(req.isSendEmail());
        return payload;
    }

    @AssertTrue(message = "End date must be greater than start date")
    public boolean isValidStartAndEndDate() {
        if (this.startDate == null || this.endDate == null) {
            return true;
        }
        return this.endDate > this.startDate;
    }

    @Getter
    @Setter
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubBudget {
        @NotNull(message = "order number must not be null")
        private Integer orderNumber;

        @NotBlank(message = "Period cannot be blank")
        @Convert(converter = ESubBudgetPeriod.Converter.class)
        private ESubBudgetPeriod period;

        @Positive(message = "amount must be greater than 0")
        private BigDecimal amount;

        public static SubBudget from(com.oneid.oneloyalty.common.entity.SubBudget subBudget) {
            return SubBudget.builder()
                    .orderNumber(subBudget.getOrderNumber())
                    .period(subBudget.getPeriod())
                    .amount(subBudget.getAmount())
                    .build();
        }
    }
}