package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.res.EarningFileProcessRes;
import com.oneid.loyalty.accounting.ops.service.EarningFileProcessService;
import com.oneid.oneloyalty.common.constant.EDataFileType;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.entity.DataFileHistory;
import com.oneid.oneloyalty.common.service.ControlFileHistoryService;
import com.oneid.oneloyalty.common.service.DataFileHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EarningFileProcessServiceImpl implements EarningFileProcessService {

    @Autowired
    private ControlFileHistoryService controlFileHistoryService;

    @Autowired
    private DataFileHistoryService dataFileHistoryService;

    @Override
    public Page<EarningFileProcessRes> getListAvailable(String name,
                                                        EProcessingStatus status,
                                                        EFileType fileType,
                                                        Date createStartDate,
                                                        Date createEndDate,
                                                        Date updateStartDate,
                                                        Date updateEndDate,
                                                        Integer offset,
                                                        Integer limit) {

        Page<ControlFileHistory> controlFileHistoryResPage = controlFileHistoryService
                .getListAvailable(name, status, fileType, createStartDate, createEndDate, updateStartDate, updateEndDate, offset, limit);

        List<EarningFileProcessRes> res =
                controlFileHistoryResPage.getContent().stream().map(
                        earningFileProcess -> EarningFileProcessRes.builder()
                                .id(earningFileProcess.getId())
                                .name(earningFileProcess.getFileName())
                                .fileType(earningFileProcess.getFileType())
                                .status(earningFileProcess.getStatus())
                                .createAt(earningFileProcess.getCreatedAt())
                                .updatedAt(earningFileProcess.getUpdatedAt())
                                .totalFiles(earningFileProcess.getTotalFiles())
                                .errorMessage(earningFileProcess.getErrorMessage())
                                .build())
                        .collect(Collectors.toList()
                );

        return new PageImpl<>(res, controlFileHistoryResPage.getPageable(), controlFileHistoryResPage.getTotalElements());
    }

    @Override
    public Page<EarningFileProcessRes> getListDetail(EProcessingStatus status,
                                                     EDataFileType dataFileType,
                                                     Boolean totalFailedRecord,
                                                     Integer offset,
                                                     Integer limit,
                                                     Boolean sortStatus,
                                                     Long id) {
        Page<DataFileHistory> dataFileHistoryResPage = dataFileHistoryService
                .getListDetail(status, dataFileType, totalFailedRecord, offset, limit, sortStatus, id);

        List<EarningFileProcessRes> res =
                dataFileHistoryResPage.getContent().stream().map(
                        earningFileProcess -> EarningFileProcessRes.builder()
                                .name(earningFileProcess.getFileName())
                                .status(earningFileProcess.getStatus())
                                .dataFileType(earningFileProcess.getFileType())
                                .totalRecord(earningFileProcess.getTotalRecord())
                                .totalFailedRecord(earningFileProcess.getTotalFailedRecord())
                                .errorMessage(earningFileProcess.getErrorMessage())
                                .build())
                        .collect(Collectors.toList()
                );

        return new PageImpl<>(res, dataFileHistoryResPage.getPageable(), dataFileHistoryResPage.getTotalElements());

    }


}