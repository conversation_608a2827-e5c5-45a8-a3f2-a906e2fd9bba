package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.config.ElasticFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.res.IsSyncElasticlFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "oneloyalty-elastic", url = "${oneloyalty-elastic.base-url}"
        , configuration = ElasticFeignConfig.class)
public interface OneloyaltyElasticFeignClient {

    @RequestMapping(method = RequestMethod.GET, value = "/v1/transactions/{txn_ref_id}")
    APIResponse<IsSyncElasticlFeignRes> searchListHistory(@PathVariable(value = "txn_ref_id") String txnRefId);

    @RequestMapping(method = RequestMethod.POST, value = "/v1/sync")
    ResponseEntity<?> syncElastic(
            @RequestParam("request_id") String requestId,
            @RequestBody List<String> txnRefList
    );
}