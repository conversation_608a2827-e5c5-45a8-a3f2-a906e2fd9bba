package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.model.res.EarningFileProcessRes;
import com.oneid.loyalty.accounting.ops.service.EarningFileProcessService;
import com.oneid.oneloyalty.common.constant.EDataFileType;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.service.ControlFileHistoryService;
import com.oneid.oneloyalty.common.service.DataFileHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("v1/earning-file-process")
@Validated
public class EarningFileProcessController extends BaseController {

    @Autowired
    EarningFileProcessService earningFileProcessService;

    @Autowired
    private ControlFileHistoryService controlFileHistoryService;

    @Autowired
    private  DataFileHistoryService dataFileHistoryService;

    @GetMapping("request/available")
    public ResponseEntity<?> getListAvailable(@RequestParam(name = "name", required = false) String name,
                                              @RequestParam(name = "fileType", required = false) EFileType fileType,
                                              @RequestParam(name = "status", required = false) EProcessingStatus status,
                                              @RequestParam(value = "created_start") @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date createStartDate,
                                              @RequestParam(value = "created_end") @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date createEndDate,
                                              @RequestParam(value = "updated_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date updateStartDate,
                                              @RequestParam(value = "updated_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_TIME_FORMAT) Date updateEndDate,
                                              @RequestParam(name = "offset", defaultValue = "0") Integer offset,
                                              @RequestParam(name = "limit", defaultValue = "10") Integer limit) {
        Page<EarningFileProcessRes> listAvailable = earningFileProcessService.getListAvailable(name, status, fileType, createStartDate, createEndDate, updateStartDate, updateEndDate, offset, limit);

        return success(listAvailable.getContent(), offset, limit, (int) listAvailable.getTotalElements());
    }

    @GetMapping("request/available/{id}/view")
    public ResponseEntity<?> getListDetail(@PathVariable("id") Long id,
                                           @RequestParam(name = "status", required = false) EProcessingStatus status,
                                           @RequestParam(name = "fileType", required = false) EDataFileType dataFileType,
                                           @RequestParam(name = "totalFailedRecord", required = false) Boolean totalFailedRecordFilter,
                                           @RequestParam(name = "offset", defaultValue = "0") Integer offset,
                                           @RequestParam(name = "limit", defaultValue = "10") Integer limit,
                                           @RequestParam(name = "sortStatus", required = false) Boolean sortStatus) {
        Page<EarningFileProcessRes> listDetail = earningFileProcessService.getListDetail(status, dataFileType, totalFailedRecordFilter, offset, limit, sortStatus, id);

        return success(listDetail.getContent(), offset, limit, (int) listDetail.getTotalElements());
    }

    @GetMapping("/requests/available/{id}")
    public ResponseEntity<?> findById (@PathVariable("id") Long id) {
        return success(controlFileHistoryService.findById(id));
    }

}
