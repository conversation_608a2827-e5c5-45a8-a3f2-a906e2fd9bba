package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.model.req.BudgetReq;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.ESubBudgetPeriod;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class BudgetInReviewRes implements Serializable {

    private Integer id;

    private String editKey;

    private String requestId;

    private ShortEntityRes program;

    private String createdBy;

    private String updatedBy;

    private String approvedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date updatedAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;

    private EApprovalStatus approvalStatus;

    private String code;

    private String name;

    private String description;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date startDate;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date endDate;

    private ECommonStatus status;

    private ERequestType requestType;

    private EBoolean isSubBudgetConfig;

    private BigDecimal amount;

    private EBoolean allowRemainingValue;

    private String reason;

    private String madeReason;

    private List<SubBudget> subBudgets;

    private List<SchemeRes> schemes;

    private Integer version;

    @Getter
    @Setter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class SubBudget {
        private Integer orderNumber;

        private ESubBudgetPeriod period;

        private BigDecimal amount;

        public static SubBudget from(BudgetReq.SubBudget subBudget) {
            return SubBudget.builder()
                    .orderNumber(subBudget.getOrderNumber())
                    .period(subBudget.getPeriod())
                    .amount(subBudget.getAmount())
                    .build();
        }

        public static SubBudget from(com.oneid.oneloyalty.common.entity.SubBudget subBudget) {
            return SubBudget.builder()
                    .orderNumber(subBudget.getOrderNumber())
                    .period(subBudget.getPeriod())
                    .amount(subBudget.getAmount())
                    .build();
        }
    }
}
