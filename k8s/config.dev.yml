apiVersion: v1
kind: ConfigMap
metadata:
  name: ${CI_PROJECT_NAME}
data:
  PROFILE: "dev"
  APP_ENVIRONMENT: "dev"
  APP_MODE: "MODE_DEV"
  MEMBER_SERVICE_URL: "https://api-dev.int.vinid.dev/oneloyalty-member"
  MAKER_CHECKER_URL_CHANGE: "https://api-dev.int.vinid.dev/makerchecker/v1/changes"

  CARD_SERVICE_BASE_URL: "https://api-dev.int.vinid.dev/oneloyalty-card"
  CARD_SERVICE_URL: "https://api-dev.int.vinid.dev/oneloyalty-card/v1"
  VOUCHER_SERVICE_URL: "https://api-dev.vinid.dev/onevc/tcb-vsm-ops"
  ONELOYALTY_SERVICE_BASE_URL: "https://api-dev.int.vinid.dev/oneloyalty-service"
  ONELOYALTY_MASTER_WORKER_BASE_URL: "https://api-dev.int.vinid.dev/oneloyalty-mw-service/v1"

  ES_ENDPOINTS: "es.int.vinid.dev:443"
  ES_INDEX_ACTIVITY: "db-oneloyalty-transaction-history-dev"
  OPS_AUTHEN_URL: "https://api-merchant-dev.int.vinid.dev/tcb-int/v1"
  VD_CARD_PREFIX_CODES: "6666"
  SFTP_SESSION_TIMEOUT: "15000"
  SFTP_CHANNEL_TIMEOUT: "15000"
  CPR_SFTP_HOST: "**************"
  CPR_SFTP_PORT: "2022"
  CPR_SFTP_REMOTE_FOLDER: "/home/<USER>/dev/member_card"
  ONELOYALTY_INTEGRATION_OPS: https://api-dev.int.vinid.dev/oneloyaltyops-partner-ops-integration
  VD_CARD_TYPE_CODES: "101"
  MAKER_CHECKER_INTERNAL_URL: "https://api-qc.vinid.dev/loyalty-mace/v1/request"
  MAKER_CHECKER_INTERNAL_API_KEY: "secret_lp"
  TCB_CORPORATION_CODE: TCB
  TCB_BATCH_ADJ_TXN_BUCKET_DIR: loyalty/ops/dev/tcb-batch-adj-txn
  GCP_STORAGE_OPS_BUCKET_NAME: vinid-loyalty-partner-ops-internal-np
  GCP_STORAGE_OPS_SERVICE_ACCOUNT_KEY: "/gcp/storage/gcp-storage-key.json"
  ES_SCHEME_MANAGEMENT_INDEX: "db-oneloyalty-scheme-management-dev"
  KAFKA_BOOTSTRAP_ADDRESS: "confluent-kafka-tw-1.int.onemount.dev:9093,confluent-kafka-tw-2.int.onemount.dev:9093, confluent-kafka-tw-3.int.onemount.dev:9093,confluent-kafka-sg-4.int.onemount.dev:9093,confluent-kafka-sg-5.int.onemount.dev:9093,confluent-kafka-sg-6.int.onemount.dev:9093"
  KAFKA_TRUSTSTORE_LOCATION: "./kafka/confluent-truststore.jks"
  KAFKA_KEYSTORE_LOCATION: "./kafka/oneloyalty-common-clients.int.onemount.dev.jks"
  KAFKA_TOPIC_NAME_COMMON_EVENTS: "oneloyalty-common-events-dev"
  VERSIONING_CURRENT_FORMAT: "v.%d(current)"
  VERSIONING_BASIC_FORMAT: "v.%d"
  GCT_DEFAULT_SEND_SMS_PASSWORD_TIME_SEC: "60"
  SAP_BASE_URL: "https://api-sap1mg-dev.vinid.dev/RESTAdapter/salesorder"
  OAUTH2_URL: "https://oauth-uat.vinid.dev"
  NOTIFICATION_CENTER_URL: "https://api-uat.int.vinid.dev/notification"
  NOTIFICATION_CENTER_TEMPLATE_ID_PASSWORD_FOLDER: "72d4eefe-a1d3-4cd5-8e07-e9a285ef2d89"
  FILE_MAX_LINE: "10000"
  SERVICE_REQUEST_TIMEOUT: "420000"
  ONELOYALTY_AUDIT_LOG_BASE_URL: "https://api-dev.int.vinid.dev/oneloyalty-audit-log"
  ONELOYALTY_CPM_SERVICE_BASE_URL: "https://api-dev.int.vinid.dev/oneloyalty-cpm-service"