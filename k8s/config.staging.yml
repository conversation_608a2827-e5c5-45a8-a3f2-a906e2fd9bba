apiVersion: v1
kind: ConfigMap
metadata:
  name: ${CI_PROJECT_NAME}
data:
  PROFILE: "staging"
  APP_MODE: "MODE_STAGING"
  APP_ENVIRONMENT: "staging"
  MEMBER_SERVICE_URL: "http://oneloyalty-member/oneloyalty-member"
  MAKER_CHECKER_URL_CHANGE: "http://oneloyalty-makerchecker/makerchecker/v1/changes"
  CARD_SERVICE_BASE_URL: "http://oneloyalty-card/oneloyalty-card"
  CARD_SERVICE_URL: "http://oneloyalty-card/oneloyalty-card/v1"
  VOUCHER_SERVICE_URL: "https://tcb-portal-staging.onemount.dev/onevc/tcb-vsm-ops"
  ONELOYALTY_SERVICE_BASE_URL: "http://oneloyalty-service/oneloyalty-service"
  MAKER_CHECKER_URL: "http://oneloyalty-makerchecker/makerchecker/v1"
  MAKER_CHECKER_INTERNAL_URL: "http://oneloyalty-makerchecker-v2/loyalty-mace/v1/request"
  MAKER_CHECKER_INTERNAL_API_KEY: "secret_lp"
  ONELOYALTY_MASTER_WORKER_BASE_URL: "http://oneloyalty-mw-master/oneloyalty-mw-service/v1"
  DEFAULT_SCHEMA_ORACLE: "OLOYALTY"
  ES_ENDPOINTS: "es.int.vinid.dev:443"
  ES_INDEX_ACTIVITY: "db-oneloyalty-transaction-history-staging"
  OPS_AUTHEN_URL: "https://api-merchant-dev.int.vinid.dev/tcb-int/v1"
  VD_CARD_PREFIX_CODES: "6666"
  KAFKA_BOOTSTRAP_ADDRESS: "confluent-kafka-tw-1.int.onemount.dev:9093,confluent-kafka-tw-2.int.onemount.dev:9093, confluent-kafka-tw-3.int.onemount.dev:9093,confluent-kafka-sg-4.int.onemount.dev:9093,confluent-kafka-sg-5.int.onemount.dev:9093,confluent-kafka-sg-6.int.onemount.dev:9093"
  KAFKA_TRUSTSTORE_LOCATION: "./kafka/confluent-truststore.jks"
  KAFKA_KEYSTORE_LOCATION: "./kafka/oneloyalty-common-clients.int.onemount.dev.jks"
  KAFKA_TOPIC_NAME_COMMON_EVENTS: "oneloyalty-common-events-staging"
  KAFKA_SSL_ENABLE: "true"
  SFTP_SESSION_TIMEOUT: "15000"
  SFTP_CHANNEL_TIMEOUT: "15000"
  CPR_SFTP_HOST: "**************"
  CPR_SFTP_PORT: "2022"
  CPR_SFTP_REMOTE_FOLDER: "/reconcile/oneloyalty-partner-ops/uat/member_card"
  ONELOYALTY_INTEGRATION_OPS: "http://oneloyalty-partner-ops-integration/oneloyalty-partner-ops-integration"
  VD_CARD_TYPE_CODES: "101"
  VGC_BUSINESS_CODE: "VGC"
  TCB_CORPORATION_CODE: "TCBLOYALTY"
  TCB_BATCH_ADJ_TXN_BUCKET_DIR: "loyalty/ops/uat/tcb-batch-adj-txn"
  GCP_STORAGE_OPS_BUCKET_NAME: "vinid-loyalty-partner-ops-internal-np"
  GCP_STORAGE_OPS_SERVICE_ACCOUNT_KEY: "/gcp/storage/gcp-storage-key.json"
  ES_SCHEME_MANAGEMENT_INDEX: "db-oneloyalty-scheme-management-staging"
  VERSIONING_CURRENT_FORMAT: "v.%d(current)"
  VERSIONING_BASIC_FORMAT: "v.%d"
  GCT_DEFAULT_SEND_SMS_PASSWORD_TIME_SEC: "60"
  OAUTH2_URL: "https://oauth-uat.vinid.dev"
  SAP_BASE_URL: "https://api-sap1mg-dev.vinid.dev/RESTAdapter/salesorder"
  NOTIFICATION_CENTER_URL: "https://api-uat.int.vinid.dev/notification"
  NOTIFICATION_CENTER_TEMPLATE_ID_PASSWORD_FOLDER: "72d4eefe-a1d3-4cd5-8e07-e9a285ef2d89"
  ENCRYPTION_AES_SECRET_KEY: "n2r5u8x/A?D(G+Kb"
  FILE_MAX_LINE: "10000"
  SERVICE_REQUEST_TIMEOUT: "420000"
  ONELOYALTY_AUDIT_LOG_BASE_URL: "http://oneloyalty-audit-log-service/oneloyalty-audit-log"
  ONELOYALTY_RULES_BASE_URL: "http://oneloyalty-rules/oneloyalty-rules"
  KAFKA_RESET_RULE_TOPIC: "oneloyalty-reset-rule-staging"
  KAFKA_RESET_SCHEME_RULE_TOPIC: "oneloyalty-scheme-rule-staging"
  ONELOYALTY_SCHEME_BASE_URL: "http://oneloyalty-scheme/oneloyalty-scheme"
  VOUCHER_SERVICE_INTERNAL_URL: "https://api-staging.vinid.dev/onevc/internal"
  ONELOYALTY_CPM_SERVICE_BASE_URL: "https://api-staging.int.vinid.dev/oneloyalty-cpm-service"
  ONELOYALTY_AIRFLOW_SERVICE_BASE_URL: "https://loyalty-airflow-stg.int.vinid.dev/"
  ACCEPT_LANGUAGE: "vi"
  ONEU_VOUCHER_SERVICE_INTERNAL_URL: "https://api-staging.vinid.dev/onevc"