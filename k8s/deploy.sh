#!/usr/bin/env bash

if [[ -z $IMAGE_TAG ]]; then
  echo "IMAGE_TAG is empty"
  exit 1
fi
if [[ "$CI_COMMIT_TAG" != "" ]]; then
    export IMAGE_TAG=$CI_COMMIT_TAG
fi
if [ -f main-${ENVIRONMENT}.yml ]; then
  MAIN="main-${ENVIRONMENT}.yml"
else
  MAIN="main.yml"
fi

envsubst < ${MAIN} > k8s-main.yml
envsubst < config.$ENVIRONMENT.yml > k8s-config.yml

kubectl apply -n $NAMESPACE -f k8s-config.yml
kubectl apply -n $NAMESPACE -f k8s-main.yml

if [[ $? != 0 ]]; then
  exit 1;
fi

kubectl rollout status deployments/$CI_PROJECT_NAME -n $NAMESPACE --timeout=400s
if [[ $? != 0 ]]; then
    kubectl -n $NAMESPACE logs $(kubectl -n $NAMESPACE get pods --sort-by=.metadata.creationTimestamp | grep "$CI_PROJECT_NAME" | awk '{print $1}' | tac | head -1 ) --tail=20 && exit 1;
fi
